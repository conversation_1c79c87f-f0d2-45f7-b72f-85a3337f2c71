<?php
// ajax_handler.php
ini_set('display_errors', 0); // Don't show errors directly to user
error_reporting(E_ALL); // Report all errors
ini_set('log_errors', 1); // Log errors to the server's error log
// ini_set('error_log', '/path/to/your/php-error.log'); // Optional: specify log file path

// --- Session Start (Needs to be early) ---
if (session_status() == PHP_SESSION_NONE) {
    session_start(); // Start session if not already started
}
// Set default header, might be overridden for file downloads
header('Content-Type: application/json; charset=utf-8'); // Ensure charset is set early

// --- Database Connection ---
require_once __DIR__ . '/config/database.php';
if (!isset($conn) || !$conn) {
    error_log("AJAX Handler: Database connection failed.");
    send_json_response(false, [], 'Database connection error.');
} else {
     mysqli_set_charset($conn, "utf8mb4"); // Ensure UTF-8 for names
}

// --- Include Log Helper ---
require_once __DIR__ . '/log_helper.php';


// --- Helper Functions ---
function send_json_response($success, $data = [], $message = '') {
    global $conn;
    if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
        mysqli_close($conn);
    }
    // Clean buffer only if it exists and headers not sent
    if (!headers_sent() && ob_get_level() > 0) {
         // Check if buffer exists before cleaning
         while (ob_get_level() > 0) {
             ob_end_clean();
         }
    }
    // Prevent potential circular references in complex data structures
    // Ensure JSON response even for downloads (though they exit before this)
    if (headers_sent($file, $line)) {
         error_log("Headers already sent in $file on line $line before send_json_response");
         // Avoid echoing JSON if headers (like for file download) were already sent
         exit;
    }
    // Re-affirm header with charset JUST before output
    header('Content-Type: application/json; charset=utf-8');
    // Use flags to handle potential UTF-8 issues gracefully
    echo json_encode(['success' => $success, 'data' => $data, 'message' => $message], JSON_INVALID_UTF8_SUBSTITUTE | JSON_PARTIAL_OUTPUT_ON_ERROR);
    exit;
}

// Function to check if user has admin privileges
function require_admin_privileges() {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        error_log("Admin privileges check failed: User not logged in");
        send_json_response(false, [], 'Authentication required. Please log in.');
    }

    // Check if user has admin role
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        error_log("Admin privileges check failed: User ID " . $_SESSION['user_id'] . " with role " . ($_SESSION['role'] ?? 'undefined') . " attempted admin action");
        send_json_response(false, [], 'Access denied. Admin privileges required for this action.');
    }

    // If we get here, the user is logged in and has admin role
    return true;
}

function sanitize_input($conn, $input) {
    if (!$conn) { error_log("Sanitize Input: DB connection invalid."); return null; }
    if (is_array($input)) {
        return array_map(function($item) use ($conn) {
            // Allow nulls through, sanitize strings, keep others as is
            if (is_string($item)) {
                return mysqli_real_escape_string($conn, trim($item));
            } elseif ($item === null) {
                return null;
            } else {
                 // Recursive call for nested arrays
                 return is_array($item) ? sanitize_input($conn, $item) : $item;
            }
        }, $input);
    }
    // Allow nulls, sanitize strings, keep others as is
    if (is_string($input)) {
        return mysqli_real_escape_string($conn, trim($input));
    } elseif ($input === null) {
        return null;
    } else {
        return $input;
    }
}

function formatBytes($bytes, $precision = 2) {
    if (!is_numeric($bytes) || $bytes < 0) { return '0 B'; }
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    // Avoid division by zero if $pow is negative (shouldn't happen with max(bytes,0))
     $divisor = (1 << (10 * $pow)); // $bytes /= pow(1024, $pow);
     if ($divisor > 0) {
        $bytes /= $divisor;
     } else {
         $bytes = 0; // Reset bytes if divisor is zero or negative
     }

    return round($bytes, $precision) . ' ' . $units[$pow];
}

// Activity File Upload Handler
function handle_upload($conn, $activity_ids, $files_array) {
    $upload_dir = __DIR__ . '/uploads/';
    $allowed_mime_types = [ 'application/pdf', 'image/jpeg', 'image/png', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/msword', 'application/vnd.ms-excel' ];
    $allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'docx', 'xlsx', 'doc', 'xls'];
    $max_size = 10 * 1024 * 1024; // 10 MB

    if (!is_dir($upload_dir)) { if (!mkdir($upload_dir, 0775, true)) { error_log("Upload Handler: Failed to create upload directory: " . $upload_dir); return ['success' => false, 'message' => 'Server error: Cannot create upload directory.']; } }
    if (!is_writable($upload_dir)) { error_log("Upload Handler: Upload directory is not writable: " . $upload_dir); return ['success' => false, 'message' => 'Server error: Upload directory not writable.']; }

    $uploaded_count = 0; $errors = [];
    $insert_sql = "INSERT INTO tblactivity_files (activity_id, original_filename, stored_filename, filepath, filesize, filetype, uploaded_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
    $stmt_insert = mysqli_prepare($conn, $insert_sql);
    if (!$stmt_insert) { error_log("Upload Handler: DB prepare error: " . mysqli_error($conn)); return ['success' => false, 'message' => 'Database error preparing insert.']; }

    $num_files = isset($files_array['name']) ? count($files_array['name']) : 0;
    for ($i = 0; $i < $num_files; $i++) {
        $original_name = $files_array['name'][$i];
        $tmp_name = $files_array['tmp_name'][$i];
        $file_size = $files_array['size'][$i];
        $file_type_provided = $files_array['type'][$i];
        $file_error = $files_array['error'][$i];

        if ($file_error !== UPLOAD_ERR_OK) {
            $errors[] = "Error uploading '$original_name': Code " . $file_error;
            continue;
        }
        if (empty($tmp_name) || !is_uploaded_file($tmp_name)) {
            $errors[] = "Error uploading '$original_name': Invalid upload.";
            continue;
        }
        if ($file_size > $max_size) {
            $errors[] = "Error uploading '$original_name': File exceeds max size (" . formatBytes($max_size, 0) . ").";
            continue;
        }

        $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        $finfo = @finfo_open(FILEINFO_MIME_TYPE);
        $detected_mime = $finfo ? @finfo_file($finfo, $tmp_name) : null;
        if ($finfo) @finfo_close($finfo);

        $mime_allowed = $detected_mime && in_array($detected_mime, $allowed_mime_types);
        $ext_allowed = in_array($file_ext, $allowed_extensions);

        if (!$mime_allowed && !$ext_allowed) {
            $errors[] = "Error uploading '$original_name': File type ('$detected_mime' / '$file_ext') not allowed.";
            continue;
        }
        $final_file_type = $mime_allowed ? $detected_mime : ($file_type_provided ?: 'application/octet-stream');

        $safe_original_name = preg_replace("/[^a-zA-Z0-9._-]/", "_", basename($original_name));
        $file_ext_final = strtolower(pathinfo($safe_original_name, PATHINFO_EXTENSION));
        if (empty($file_ext_final)) { // Add default extension if missing after sanitizing
            $ext_map = ['application/pdf' => 'pdf', 'image/jpeg' => 'jpg', 'image/png' => 'png', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx', 'application/msword' => 'doc', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx', 'application/vnd.ms-excel' => 'xls'];
            $file_ext_final = $ext_map[$final_file_type] ?? 'bin';
        }

        foreach ($activity_ids as $activity_id) {
            if (!is_numeric($activity_id)) continue;
            $stored_filename = 'file_' . $activity_id . '_' . uniqid('', true) . '.' . $file_ext_final;
            $destination = rtrim($upload_dir, '/') . '/' . $stored_filename;
            if (copy($tmp_name, $destination)) {
                mysqli_stmt_bind_param($stmt_insert, "isssis", $activity_id, $original_name, $stored_filename, $destination, $file_size, $final_file_type);
                if (mysqli_stmt_execute($stmt_insert)) {
                    $uploaded_count++;
                } else {
                    $db_error = mysqli_stmt_error($stmt_insert);
                    $errors[] = "DB error saving '$original_name' for Activity ID $activity_id: " . $db_error;
                    error_log("Upload Handler DB Error (Activity $activity_id, File $original_name): " . $db_error);
                    @unlink($destination);
                }
            } else {
                $errors[] = "Error copying uploaded file '$original_name' for Activity ID $activity_id.";
                error_log("Upload Handler: Failed to copy '$tmp_name' to '$destination'. Check permissions.");
            }
        }
        @unlink($tmp_name);
    }
    mysqli_stmt_close($stmt_insert);
    if ($uploaded_count > 0 && empty($errors)) { return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' file association(s) created successfully.']; }
    elseif ($uploaded_count > 0) { return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' file association(s) created, but errors occurred: ' . implode('; ', $errors)]; }
    elseif (empty($errors)) { return ['success' => false, 'message' => 'No files were processed or uploaded successfully.']; }
    else { return ['success' => false, 'message' => 'File upload failed: ' . implode('; ', $errors)]; }
}

// Participant File Upload Handler
function handle_participant_upload($conn, $participant_ids, $files_array) {
    $upload_dir = __DIR__ . '/uploads_participants/'; // Separate folder
    $allowed_mime_types = [ 'application/pdf', 'image/jpeg', 'image/png', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/msword', 'application/vnd.ms-excel' ];
    $allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'docx', 'xlsx', 'doc', 'xls'];
    $max_size = 10 * 1024 * 1024;

    if (!is_dir($upload_dir)) { if (!mkdir($upload_dir, 0775, true)) { error_log("Upload Participant Handler: Failed to create dir: " . $upload_dir); return ['success' => false, 'message' => 'Server error: Cannot create upload directory.']; } }
    if (!is_writable($upload_dir)) { error_log("Upload Participant Handler: Dir not writable: " . $upload_dir); return ['success' => false, 'message' => 'Server error: Upload directory not writable.']; }

    $uploaded_count = 0; $errors = [];
    $insert_sql = "INSERT INTO tblparticipant_files (participant_id, original_filename, stored_filename, filepath, filesize, filetype, uploaded_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
    $stmt_insert = mysqli_prepare($conn, $insert_sql);
    if (!$stmt_insert) { error_log("Upload Participant Handler: DB prepare error: " . mysqli_error($conn)); return ['success' => false, 'message' => 'Database error preparing insert.']; }

    $num_files = isset($files_array['name']) ? count($files_array['name']) : 0;
    for ($i = 0; $i < $num_files; $i++) {
        $original_name = $files_array['name'][$i];
        $tmp_name = $files_array['tmp_name'][$i];
        $file_size = $files_array['size'][$i];
        $file_type_provided = $files_array['type'][$i];
        $file_error = $files_array['error'][$i];

        if ($file_error !== UPLOAD_ERR_OK) {
            $errors[] = "Error uploading '$original_name': Code " . $file_error;
            continue;
        }
        if (empty($tmp_name) || !is_uploaded_file($tmp_name)) {
            $errors[] = "Error uploading '$original_name': Invalid upload.";
            continue;
        }
        if ($file_size > $max_size) {
            $errors[] = "Error uploading '$original_name': File exceeds max size (" . formatBytes($max_size, 0) . ").";
            continue;
        }

        // Validate file type
        $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_extensions)) {
            $errors[] = "Error uploading '$original_name': File type not allowed.";
            continue;
        }

        // Double-check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detected_type = finfo_file($finfo, $tmp_name);
        finfo_close($finfo);
        if (!in_array($detected_type, $allowed_mime_types)) {
            $errors[] = "Error uploading '$original_name': File type '$detected_type' not allowed.";
            continue;
        }

        // Use detected type for DB
        $final_file_type = $detected_type;
        $file_ext_final = $file_ext;

        foreach ($participant_ids as $participant_id) {
            if (!is_numeric($participant_id)) continue;
            $stored_filename = 'pfile_' . $participant_id . '_' . uniqid('', true) . '.' . $file_ext_final;
            $destination = rtrim($upload_dir, '/') . '/' . $stored_filename;
            if (copy($tmp_name, $destination)) {
                mysqli_stmt_bind_param($stmt_insert, "isssis", $participant_id, $original_name, $stored_filename, $destination, $file_size, $final_file_type);
                if (mysqli_stmt_execute($stmt_insert)) { $uploaded_count++; }
                else { $db_error = mysqli_stmt_error($stmt_insert); $errors[] = "DB error saving '$original_name' for Participant ID $participant_id: " . $db_error; error_log("Upload Participant Handler DB Error (Participant $participant_id, File $original_name): " . $db_error); @unlink($destination); }
            } else { $errors[] = "Error copying uploaded file '$original_name' for Participant ID $participant_id."; error_log("Upload Participant Handler: Failed to copy '$tmp_name' to '$destination'. Check permissions."); }
        }
        @unlink($tmp_name);
    }
    mysqli_stmt_close($stmt_insert);
    if ($uploaded_count > 0 && empty($errors)) { return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' participant file association(s) created successfully.']; }
    elseif ($uploaded_count > 0) { return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' participant file association(s) created, but errors occurred: ' . implode('; ', $errors)]; }
    elseif (empty($errors)) { return ['success' => false, 'message' => 'No participant files were processed or uploaded successfully.']; }
    else { return ['success' => false, 'message' => 'Participant file upload failed: ' . implode('; ', $errors)]; }
}

// FreeWifi4All File Upload Handler
function handle_fw4a_upload($conn, $fw4a_ids, $files_array) {
    $upload_dir = __DIR__ . '/uploads_fw4a/'; // Dedicated folder for FreeWifi4All files
    $allowed_mime_types = [ 'application/pdf', 'image/jpeg', 'image/png', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/msword', 'application/vnd.ms-excel' ];
    $allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'docx', 'xlsx', 'doc', 'xls'];
    $max_size = 10 * 1024 * 1024; // 10 MB

    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0775, true)) {
            error_log("Upload FW4A Handler: Failed to create dir: " . $upload_dir);
            return ['success' => false, 'message' => 'Server error: Upload directory not available.'];
        }
    }

    $uploaded_count = 0; $errors = [];
    $insert_sql = "INSERT INTO tblfw4a_files (activity_id, original_filename, stored_filename, filepath, filesize, filetype, uploaded_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
    $stmt_insert = mysqli_prepare($conn, $insert_sql);
    if (!$stmt_insert) {
        error_log("Upload FW4A Handler: DB prepare error: " . mysqli_error($conn));
        return ['success' => false, 'message' => 'Database error preparing insert.'];
    }

    $num_files = isset($files_array['name']) ? count($files_array['name']) : 0;
    for ($i = 0; $i < $num_files; $i++) {
        $original_name = $files_array['name'][$i];
        $tmp_name = $files_array['tmp_name'][$i];
        $file_size = $files_array['size'][$i];
        $file_type_provided = $files_array['type'][$i];
        $file_error = $files_array['error'][$i];

        if ($file_error !== UPLOAD_ERR_OK) {
            $errors[] = "Error uploading '$original_name': Code " . $file_error;
            continue;
        }
        if (empty($tmp_name) || !is_uploaded_file($tmp_name)) {
            $errors[] = "Error uploading '$original_name': Invalid upload.";
            continue;
        }
        if ($file_size > $max_size) {
            $errors[] = "Error uploading '$original_name': File exceeds max size (" . formatBytes($max_size, 0) . ").";
            continue;
        }

        $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_extensions)) {
            $errors[] = "Error uploading '$original_name': File type not allowed.";
            continue;
        }

        $file_ext_final = $file_ext;
        $final_file_type = $file_type_provided;

        // Additional MIME type validation
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detected_type = finfo_file($finfo, $tmp_name);
        finfo_close($finfo);

        if (!in_array($detected_type, $allowed_mime_types)) {
            $errors[] = "Error uploading '$original_name': File type '$detected_type' not allowed.";
            continue;
        }

        foreach ($fw4a_ids as $fw4a_id) {
            if (!is_numeric($fw4a_id)) continue;
            $stored_filename = 'fw4a_' . $fw4a_id . '_' . uniqid('', true) . '.' . $file_ext_final;
            $destination = rtrim($upload_dir, '/') . '/' . $stored_filename;
            if (copy($tmp_name, $destination)) {
                mysqli_stmt_bind_param($stmt_insert, "isssis", $fw4a_id, $original_name, $stored_filename, $destination, $file_size, $final_file_type);
                if (mysqli_stmt_execute($stmt_insert)) {
                    $uploaded_count++;
                } else {
                    $db_error = mysqli_stmt_error($stmt_insert);
                    $errors[] = "DB error saving '$original_name' for FW4A ID $fw4a_id: " . $db_error;
                    error_log("Upload FW4A Handler DB Error (FW4A $fw4a_id, File $original_name): " . $db_error);
                    @unlink($destination);
                }
            } else {
                $errors[] = "Error copying uploaded file '$original_name' for FW4A ID $fw4a_id.";
                error_log("Upload FW4A Handler: Failed to copy '$tmp_name' to '$destination'. Check permissions.");
            }
        }
        @unlink($tmp_name);
    }
    mysqli_stmt_close($stmt_insert);
    if ($uploaded_count > 0 && empty($errors)) {
        return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' FW4A file association(s) created successfully.'];
    } elseif ($uploaded_count > 0) {
        return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' FW4A file association(s) created, but errors occurred: ' . implode('; ', $errors)];
    } elseif (empty($errors)) {
        return ['success' => false, 'message' => 'No FW4A files were processed or uploaded successfully.'];
    } else {
        return ['success' => false, 'message' => 'FW4A file upload failed: ' . implode('; ', $errors)];
    }
}

// Letter Requests File Upload Handler
function handle_letter_upload($conn, $letter_ids, $files_array) {
    $upload_dir = __DIR__ . '/uploads_letters/'; // Dedicated folder for Letter Request files
    $allowed_mime_types = [ 'application/pdf', 'image/jpeg', 'image/png', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/msword', 'application/vnd.ms-excel' ];
    $allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'docx', 'xlsx', 'doc', 'xls'];
    $max_size = 10 * 1024 * 1024; // 10 MB

    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0775, true)) {
            error_log("Upload Letter Handler: Failed to create dir: " . $upload_dir);
            return ['success' => false, 'message' => 'Server error: Upload directory not available.'];
        }
    }

    $uploaded_count = 0; $errors = [];
    $insert_sql = "INSERT INTO tblfw4a_files (activity_id, original_filename, stored_filename, filepath, filesize, filetype, uploaded_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
    $stmt_insert = mysqli_prepare($conn, $insert_sql);
    if (!$stmt_insert) {
        error_log("Upload Letter Handler: DB prepare error: " . mysqli_error($conn));
        return ['success' => false, 'message' => 'Database error preparing insert.'];
    }

    $num_files = isset($files_array['name']) ? count($files_array['name']) : 0;
    for ($i = 0; $i < $num_files; $i++) {
        $original_name = $files_array['name'][$i];
        $tmp_name = $files_array['tmp_name'][$i];
        $file_size = $files_array['size'][$i];
        $file_type_provided = $files_array['type'][$i];
        $file_error = $files_array['error'][$i];

        if ($file_error !== UPLOAD_ERR_OK) {
            $errors[] = "Error uploading '$original_name': Code " . $file_error;
            continue;
        }
        if (empty($tmp_name) || !is_uploaded_file($tmp_name)) {
            $errors[] = "Error uploading '$original_name': Invalid upload.";
            continue;
        }
        if ($file_size > $max_size) {
            $errors[] = "Error uploading '$original_name': File exceeds max size (" . formatBytes($max_size, 0) . ").";
            continue;
        }

        $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_extensions)) {
            $errors[] = "Error uploading '$original_name': File type not allowed.";
            continue;
        }

        $file_ext_final = $file_ext;
        $final_file_type = $file_type_provided;

        // Additional MIME type validation
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detected_type = finfo_file($finfo, $tmp_name);
        finfo_close($finfo);

        if (!in_array($detected_type, $allowed_mime_types)) {
            $errors[] = "Error uploading '$original_name': File type '$detected_type' not allowed.";
            continue;
        }

        foreach ($letter_ids as $letter_id) {
            if (!is_numeric($letter_id)) continue;
            $stored_filename = 'letter_' . $letter_id . '_' . uniqid('', true) . '.' . $file_ext_final;
            $destination = rtrim($upload_dir, '/') . '/' . $stored_filename;
            if (copy($tmp_name, $destination)) {
                // Store just the filename in the database, not the full path
                // This will make it easier to handle file deletions
                mysqli_stmt_bind_param($stmt_insert, "isssis", $letter_id, $original_name, $stored_filename, $stored_filename, $file_size, $final_file_type);
                if (mysqli_stmt_execute($stmt_insert)) {
                    $uploaded_count++;
                    error_log("Upload Letter Handler: Successfully uploaded file for Letter ID $letter_id: $stored_filename");
                } else {
                    $db_error = mysqli_stmt_error($stmt_insert);
                    $errors[] = "DB error saving '$original_name' for Letter ID $letter_id: " . $db_error;
                    error_log("Upload Letter Handler DB Error (Letter $letter_id, File $original_name): " . $db_error);
                    @unlink($destination);
                }
            } else {
                $errors[] = "Error copying uploaded file '$original_name' for Letter ID $letter_id.";
                error_log("Upload Letter Handler: Failed to copy '$tmp_name' to '$destination'. Check permissions.");
            }
        }
        @unlink($tmp_name);
    }
    mysqli_stmt_close($stmt_insert);
    if ($uploaded_count > 0 && empty($errors)) {
        return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' Letter file association(s) created successfully.'];
    } elseif ($uploaded_count > 0) {
        return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' Letter file association(s) created, but errors occurred: ' . implode('; ', $errors)];
    } elseif (empty($errors)) {
        return ['success' => false, 'message' => 'No Letter files were processed or uploaded successfully.'];
    } else {
        return ['success' => false, 'message' => 'Letter file upload failed: ' . implode('; ', $errors)];
    }
}

// Inventory File Upload Handler
function handle_inventory_upload($conn, $inventory_ids, $files_array) {
    $upload_dir = __DIR__ . '/uploads_inventory/'; // Dedicated folder for inventory files
    $allowed_mime_types = [ 'application/pdf', 'image/jpeg', 'image/png', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/msword', 'application/vnd.ms-excel' ];
    $allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'docx', 'xlsx', 'doc', 'xls'];
    $max_size = 10 * 1024 * 1024; // 10 MB

    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0775, true)) {
            error_log("Upload Inventory Handler: Failed to create dir: " . $upload_dir);
            return ['success' => false, 'message' => 'Server error: Upload directory not available.'];
        }
    }

    // Create the inventory_files table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS inventory_files (
        file_id INT(11) NOT NULL AUTO_INCREMENT,
        inventory_id INT(11) NOT NULL,
        original_filename VARCHAR(255) NOT NULL,
        filepath VARCHAR(512) NOT NULL,
        filesize INT(11) NOT NULL,
        filetype VARCHAR(100) NOT NULL,
        uploaded_at DATETIME NOT NULL,
        uploaded_by INT(11) DEFAULT NULL,
        PRIMARY KEY (file_id),
        KEY inventory_id (inventory_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    if (!mysqli_query($conn, $create_table_sql)) {
        error_log("Upload Inventory Handler: Failed to create inventory_files table: " . mysqli_error($conn));
        return ['success' => false, 'message' => 'Server error: Failed to create file storage table.'];
    }

    $uploaded_count = 0;
    $errors = [];
    $insert_sql = "INSERT INTO inventory_files (inventory_id, original_filename, filepath, filesize, filetype, uploaded_at, uploaded_by) VALUES (?, ?, ?, ?, ?, NOW(), ?)";
    $stmt_insert = mysqli_prepare($conn, $insert_sql);
    if (!$stmt_insert) {
        error_log("Upload Inventory Handler: DB prepare error: " . mysqli_error($conn));
        return ['success' => false, 'message' => 'Database error preparing insert.'];
    }

    $num_files = isset($files_array['name']) ? count($files_array['name']) : 0;
    for ($i = 0; $i < $num_files; $i++) {
        $original_name = $files_array['name'][$i];
        $tmp_name = $files_array['tmp_name'][$i];
        $file_size = $files_array['size'][$i];
        $file_type_provided = $files_array['type'][$i];
        $file_error = $files_array['error'][$i];

        if ($file_error !== UPLOAD_ERR_OK) {
            $errors[] = "Error uploading '$original_name': Code " . $file_error;
            continue;
        }
        if (empty($tmp_name) || !is_uploaded_file($tmp_name)) {
            $errors[] = "Error uploading '$original_name': Invalid upload.";
            continue;
        }
        if ($file_size > $max_size) {
            $errors[] = "Error uploading '$original_name': File exceeds max size (" . formatBytes($max_size, 0) . ").";
            continue;
        }

        $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_extensions)) {
            $errors[] = "Error uploading '$original_name': File type not allowed.";
            continue;
        }

        // Double-check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detected_type = finfo_file($finfo, $tmp_name);
        finfo_close($finfo);
        if (!in_array($detected_type, $allowed_mime_types)) {
            $errors[] = "Error uploading '$original_name': File type '$detected_type' not allowed.";
            continue;
        }

        // Use detected type for DB
        $final_file_type = $detected_type;
        $file_ext_final = $file_ext;
        $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

        foreach ($inventory_ids as $inventory_id) {
            if (!is_numeric($inventory_id)) continue;
            $stored_filename = 'inventory_' . $inventory_id . '_' . uniqid('', true) . '.' . $file_ext_final;
            $destination = rtrim($upload_dir, '/') . '/' . $stored_filename;
            if (copy($tmp_name, $destination)) {
                mysqli_stmt_bind_param($stmt_insert, "issisi", $inventory_id, $original_name, $destination, $file_size, $final_file_type, $user_id);
                if (mysqli_stmt_execute($stmt_insert)) {
                    $uploaded_count++;
                } else {
                    $db_error = mysqli_stmt_error($stmt_insert);
                    $errors[] = "DB error saving '$original_name' for Inventory ID $inventory_id: " . $db_error;
                    error_log("Upload Inventory Handler DB Error (Inventory $inventory_id, File $original_name): " . $db_error);
                    @unlink($destination);
                }
            } else {
                $errors[] = "Error copying uploaded file '$original_name' for Inventory ID $inventory_id.";
                error_log("Upload Inventory Handler: Failed to copy '$tmp_name' to '$destination'. Check permissions.");
            }
        }
        @unlink($tmp_name);
    }
    mysqli_stmt_close($stmt_insert);
    if ($uploaded_count > 0 && empty($errors)) {
        return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' inventory file association(s) created successfully.'];
    } elseif ($uploaded_count > 0) {
        return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' inventory file association(s) created, but errors occurred: ' . implode('; ', $errors)];
    } elseif (empty($errors)) {
        return ['success' => false, 'message' => 'No inventory files were processed or uploaded successfully.'];
    } else {
        return ['success' => false, 'message' => 'Inventory file upload failed: ' . implode('; ', $errors)];
    }
}

// Tech4ED File Upload Handler
function handle_tech4ed_upload($conn, $tech4ed_ids, $files_array) {
    $upload_dir = __DIR__ . '/uploads_tech4ed/'; // Dedicated folder for Tech4ED files
    $allowed_mime_types = [ 'application/pdf', 'image/jpeg', 'image/png', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/msword', 'application/vnd.ms-excel' ];
    $allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'docx', 'xlsx', 'doc', 'xls'];
    $max_size = 10 * 1024 * 1024; // 10 MB

    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0775, true)) {
            error_log("Upload Tech4ED Handler: Failed to create dir: " . $upload_dir);
            return ['success' => false, 'message' => 'Server error: Upload directory not available.'];
        }
    }

    $uploaded_count = 0;
    $errors = [];
    $insert_sql = "INSERT INTO tbltech4ed_files (tech4ed_id, original_filename, stored_filename, filepath, filesize, filetype, uploaded_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
    $stmt_insert = mysqli_prepare($conn, $insert_sql);
    if (!$stmt_insert) {
        error_log("Upload Tech4ED Handler: DB prepare error: " . mysqli_error($conn));
        return ['success' => false, 'message' => 'Database error preparing insert.'];
    }

    // Process each file
    foreach ($_FILES['files']['name'] as $key => $original_name) {
        if ($_FILES['files']['error'][$key] !== UPLOAD_ERR_OK) {
            $error_code = $_FILES['files']['error'][$key];
            $errors[] = "Error uploading '$original_name': " . upload_error_message($error_code);
            continue;
        }

        $tmp_name = $_FILES['files']['tmp_name'][$key];
        $file_size = $_FILES['files']['size'][$key];

        // Check file size
        if ($file_size > $max_size) {
            $errors[] = "Error uploading '$original_name': File size exceeds limit (10MB).";
            continue;
        }

        // Validate file type
        $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_extensions)) {
            $errors[] = "Error uploading '$original_name': File type not allowed.";
            continue;
        }

        // Double-check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detected_type = finfo_file($finfo, $tmp_name);
        finfo_close($finfo);
        if (!in_array($detected_type, $allowed_mime_types)) {
            $errors[] = "Error uploading '$original_name': File type '$detected_type' not allowed.";
            continue;
        }

        // Use detected type for DB
        $final_file_type = $detected_type;
        $file_ext_final = $file_ext;

        foreach ($tech4ed_ids as $tech4ed_id) {
            if (!is_numeric($tech4ed_id)) continue;
            $stored_filename = 'tech4ed_' . $tech4ed_id . '_' . uniqid('', true) . '.' . $file_ext_final;
            $destination = rtrim($upload_dir, '/') . '/' . $stored_filename;
            if (copy($tmp_name, $destination)) {
                mysqli_stmt_bind_param($stmt_insert, "isssis", $tech4ed_id, $original_name, $stored_filename, $destination, $file_size, $final_file_type);
                if (mysqli_stmt_execute($stmt_insert)) {
                    $uploaded_count++;
                } else {
                    $db_error = mysqli_stmt_error($stmt_insert);
                    $errors[] = "DB error saving '$original_name' for Tech4ED ID $tech4ed_id: " . $db_error;
                    error_log("Upload Tech4ED Handler DB Error (Tech4ED $tech4ed_id, File $original_name): " . $db_error);
                    @unlink($destination);
                }
            } else {
                $errors[] = "Error copying '$original_name' to destination.";
                error_log("Upload Tech4ED Handler: Failed to copy file to: " . $destination);
            }
        }
    }

    mysqli_stmt_close($stmt_insert);

    if ($uploaded_count > 0) {
        $message = "$uploaded_count file(s) uploaded successfully";
        if (count($errors) > 0) {
            $message .= ", but " . count($errors) . " file(s) failed.";
        } else {
            $message .= ".";
        }
        return [
            'success' => true,
            'message' => $message,
            'uploaded_count' => $uploaded_count,
            'errors' => $errors
        ];
    } else {
        return [
            'success' => false,
            'message' => 'Failed to upload any files. ' . (count($errors) > 0 ? $errors[0] : ''),
            'uploaded_count' => 0,
            'errors' => $errors
        ];
    }
}

// LGU File Upload Handler
function handle_lgu_upload($conn, $lgu_ids, $files_array) {
    $upload_dir = __DIR__ . '/uploads_lgu/'; // Separate folder for LGU files
    $allowed_mime_types = [ 'application/pdf', 'image/jpeg', 'image/png', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/msword', 'application/vnd.ms-excel' ];
    $allowed_extensions = ['pdf', 'jpg', 'jpeg', 'png', 'docx', 'xlsx', 'doc', 'xls'];
    $max_size = 10 * 1024 * 1024; // 10 MB

    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0775, true)) {
            error_log("Upload LGU Handler: Failed to create dir: " . $upload_dir);
            return ['success' => false, 'message' => 'Server error: Cannot create upload directory.'];
        }
    }
    if (!is_writable($upload_dir)) {
        error_log("Upload LGU Handler: Dir not writable: " . $upload_dir);
        return ['success' => false, 'message' => 'Server error: Upload directory not writable.'];
    }

    $uploaded_count = 0; $errors = [];

    // Create the tblbpls_files table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS tblbpls_files (
        file_id INT(11) NOT NULL AUTO_INCREMENT,
        lgu_id INT(11) NOT NULL,
        original_filename VARCHAR(255) NOT NULL,
        stored_filename VARCHAR(255) NOT NULL,
        filepath VARCHAR(512) NOT NULL,
        filesize INT(11) NOT NULL,
        filetype VARCHAR(100) NOT NULL,
        uploaded_at DATETIME NOT NULL,
        PRIMARY KEY (file_id),
        KEY lgu_id (lgu_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    if (!mysqli_query($conn, $create_table_sql)) {
        error_log("Upload LGU Handler: Failed to create tblbpls_files table: " . mysqli_error($conn));
        return ['success' => false, 'message' => 'Server error: Failed to create file storage table.'];
    }

    $insert_sql = "INSERT INTO tblbpls_files (lgu_id, original_filename, stored_filename, filepath, filesize, filetype, uploaded_at) VALUES (?, ?, ?, ?, ?, ?, NOW())";
    $stmt_insert = mysqli_prepare($conn, $insert_sql);
    if (!$stmt_insert) {
        error_log("Upload LGU Handler: DB prepare error: " . mysqli_error($conn));
        return ['success' => false, 'message' => 'Database error preparing insert.'];
    }

    $num_files = isset($files_array['name']) ? count($files_array['name']) : 0;
    for ($i = 0; $i < $num_files; $i++) {
        $original_name = $files_array['name'][$i];
        $tmp_name = $files_array['tmp_name'][$i];
        $file_size = $files_array['size'][$i];
        $file_type_provided = $files_array['type'][$i];
        $file_error = $files_array['error'][$i];

        if ($file_error !== UPLOAD_ERR_OK) {
            $errors[] = "Error uploading '$original_name': Code " . $file_error;
            continue;
        }
        if (empty($tmp_name) || !is_uploaded_file($tmp_name)) {
            $errors[] = "Error uploading '$original_name': Invalid upload.";
            continue;
        }
        if ($file_size > $max_size) {
            $errors[] = "Error uploading '$original_name': File exceeds max size (" . formatBytes($max_size, 0) . ").";
            continue;
        }

        // Validate file type
        $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_extensions)) {
            $errors[] = "Error uploading '$original_name': File type not allowed.";
            continue;
        }

        // Double-check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detected_type = finfo_file($finfo, $tmp_name);
        finfo_close($finfo);
        if (!in_array($detected_type, $allowed_mime_types)) {
            $errors[] = "Error uploading '$original_name': File type '$detected_type' not allowed.";
            continue;
        }

        // Use detected type for DB
        $final_file_type = $detected_type;
        $file_ext_final = $file_ext;

        foreach ($lgu_ids as $lgu_id) {
            if (!is_numeric($lgu_id)) continue;
            $stored_filename = 'lgu_' . $lgu_id . '_' . uniqid('', true) . '.' . $file_ext_final;
            $destination = rtrim($upload_dir, '/') . '/' . $stored_filename;
            if (copy($tmp_name, $destination)) {
                mysqli_stmt_bind_param($stmt_insert, "isssis", $lgu_id, $original_name, $stored_filename, $destination, $file_size, $final_file_type);
                if (mysqli_stmt_execute($stmt_insert)) {
                    $uploaded_count++;
                } else {
                    $db_error = mysqli_stmt_error($stmt_insert);
                    $errors[] = "DB error saving '$original_name' for LGU ID $lgu_id: " . $db_error;
                    error_log("Upload LGU Handler DB Error (LGU $lgu_id, File $original_name): " . $db_error);
                    @unlink($destination);
                }
            } else {
                $errors[] = "Error copying uploaded file '$original_name' for LGU ID $lgu_id.";
                error_log("Upload LGU Handler: Failed to copy '$tmp_name' to '$destination'. Check permissions.");
            }
        }
        @unlink($tmp_name);
    }
    mysqli_stmt_close($stmt_insert);
    if ($uploaded_count > 0 && empty($errors)) {
        return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' LGU file association(s) created successfully.'];
    } elseif ($uploaded_count > 0) {
        return ['success' => true, 'uploaded_count' => $uploaded_count, 'message' => $uploaded_count . ' LGU file association(s) created, but errors occurred: ' . implode('; ', $errors)];
    } elseif (empty($errors)) {
        return ['success' => false, 'message' => 'No LGU files were processed or uploaded successfully.'];
    } else {
        return ['success' => false, 'message' => 'LGU file upload failed: ' . implode('; ', $errors)];
    }
}


// --- Main Action Handler ---
$action = $_POST['action'] ?? $_GET['action'] ?? null; // Allow GET for download

if (!$action) { send_json_response(false, [], 'No action specified.'); }

if (!$conn || !@mysqli_ping($conn)) {
    error_log("AJAX Handler: DB connection lost before action: " . $action);
    require_once __DIR__ . '/config/database.php'; // Attempt to reconnect
    if (!$conn || !@mysqli_ping($conn)) { send_json_response(false, [], 'Database connection error before processing action.'); }
    else { error_log("AJAX Handler: Reconnected to DB successfully for action: " . $action); mysqli_set_charset($conn, "utf8mb4"); }
}

switch ($action) {
    case 'getStatDetails':
        // Get detailed activities for a specific statistic filter
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            send_json_response(false, [], 'Invalid request method.');
        }

        $stat_key = isset($_POST['stat_key']) ? sanitize_input($conn, $_POST['stat_key']) : '';
        $filter = isset($_POST['filter']) ? sanitize_input($conn, $_POST['filter']) : '';
        $project = isset($_POST['project']) ? sanitize_input($conn, $_POST['project']) : '';
        $year = isset($_POST['year']) ? sanitize_input($conn, $_POST['year']) : '';

        if (empty($stat_key)) {
            send_json_response(false, [], 'Missing statistic key.');
        }

        if (empty($filter)) {
            send_json_response(false, [], 'Missing filter value.');
        }

        $data = [];
        $error_message = '';
        $project_filter = !empty($project) ? " AND project = '$project'" : "";
        $year_filter = !empty($year) ? " AND YEAR(start) = '$year'" : "";

        try {
            // Determine which field to filter on based on the stat_key
            $filter_field = '';
            switch ($stat_key) {
                case 'sectors':
                    $filter_field = 'sector';
                    break;
                case 'agencies':
                    $filter_field = 'agency';
                    break;
                case 'municipalities':
                    $filter_field = 'municipality';
                    break;
                case 'barangays':
                    $filter_field = 'barangay';
                    break;
                case 'activities':
                    $filter_field = 'indicator';
                    break;
                case 'participants':
                    $filter_field = 'indicator';
                    break;
                case 'participants_gender':
                    // Special case for gender
                    if ($filter == 'Male') {
                        $sql = "SELECT * FROM tblactivity WHERE male > 0 $project_filter ORDER BY start DESC";
                    } else {
                        $sql = "SELECT * FROM tblactivity WHERE female > 0 $project_filter ORDER BY start DESC";
                    }
                    break;
                case 'participants_mode':
                    $filter_field = 'mode';
                    break;
                case 'district1':
                case 'district2':
                    $filter_field = 'municipality';
                    break;
                // Participant stats
                case 'p_total':
                case 'p_male':
                case 'p_female':
                case 'p_f2f':
                case 'p_virtual':
                case 'p_training':
                case 'p_awareness':
                    // For participant stats, we need to query the participant table
                    $participant_filter = '';
                    if ($stat_key == 'p_male') {
                        $participant_filter = " AND p.sex = 'Male'";
                    } elseif ($stat_key == 'p_female') {
                        $participant_filter = " AND p.sex = 'Female'";
                    } elseif ($stat_key == 'p_f2f') {
                        $participant_filter = " AND p.mode = 'Face-to-Face'";
                    } elseif ($stat_key == 'p_virtual') {
                        $participant_filter = " AND p.mode = 'Virtual'";
                    } elseif ($stat_key == 'p_training') {
                        $participant_filter = " AND p.indicator = 'Training'";
                    } elseif ($stat_key == 'p_awareness') {
                        $participant_filter = " AND p.indicator = 'Awareness'";
                    }

                    // Check if the activity_id column exists in tblparticipant
                    $check_column_sql = "SHOW COLUMNS FROM tblparticipant LIKE 'activity_id'";
                    $check_result = mysqli_query($conn, $check_column_sql);
                    $activity_id_exists = mysqli_num_rows($check_result) > 0;
                    mysqli_free_result($check_result);

                    if ($activity_id_exists) {
                        $sql = "SELECT p.*, a.start, a.end, a.activity, a.municipality, a.barangay, a.venue,
                               a.mode as activity_mode, a.indicator as activity_indicator
                               FROM tblparticipant p
                               LEFT JOIN tblactivity a ON p.activity_id = a.id
                               WHERE p.agency = '$filter' $participant_filter
                               " . (!empty($project) ? " AND p.project = '$project'" : "") . "
                               ORDER BY a.start DESC, p.fullname ASC";
                    } else {
                        // If activity_id doesn't exist, just query the participant table without the join
                        $sql = "SELECT p.*
                               FROM tblparticipant p
                               WHERE p.agency = '$filter' $participant_filter
                               " . (!empty($project) ? " AND p.project = '$project'" : "") . "
                               ORDER BY p.fullname ASC";
                    }
                    break;
                case 'p_agencies':
                    // For participants by agency
                    // Check if the activity_id column exists in tblparticipant
                    $check_column_sql = "SHOW COLUMNS FROM tblparticipant LIKE 'activity_id'";
                    $check_result = mysqli_query($conn, $check_column_sql);
                    $activity_id_exists = mysqli_num_rows($check_result) > 0;
                    mysqli_free_result($check_result);

                    if ($activity_id_exists) {
                        $sql = "SELECT p.*, a.start, a.end, a.activity, a.municipality, a.barangay, a.venue,
                               a.mode as activity_mode, a.indicator as activity_indicator
                               FROM tblparticipant p
                               LEFT JOIN tblactivity a ON p.activity_id = a.id
                               WHERE p.agency = '$filter'
                               " . (!empty($project) ? " AND p.project = '$project'" : "") . "
                               ORDER BY a.start DESC, p.fullname ASC";
                    } else {
                        // If activity_id doesn't exist, just query the participant table without the join
                        $sql = "SELECT p.*
                               FROM tblparticipant p
                               WHERE p.agency = '$filter'
                               " . (!empty($project) ? " AND p.project = '$project'" : "") . "
                               ORDER BY p.fullname ASC";
                    }
                    break;

                // eLGU BPLS Monitoring Statistics
                case 'total_lgus':
                    // Get LGUs by province
                    $sql = "SELECT * FROM tblbpls WHERE province = '$filter' ORDER BY lgu ASC";
                    break;

                case 'dict_lgus':
                    // Get LGUs using DICT systems by province with system type column
                    $sql = "SELECT *,
                           CASE
                               WHEN systemtype = 'DICT (eLGU BPLS)' THEN 'DICT (eLGU BPLS)'
                               WHEN systemtype = 'DICT (eLGU)' THEN 'DICT (eLGU)'
                               ELSE 'Other'
                           END AS system_category
                           FROM tblbpls
                           WHERE province = '$filter'
                           AND (systemtype = 'DICT (eLGU BPLS)' OR systemtype = 'DICT (eLGU)')
                           ORDER BY system_category, lgu ASC";
                    break;

                case 'implementation_rate':
                    // Get all LGUs for implementation rate by province with calculation info
                    $sql = "SELECT *,
                           CASE
                               WHEN systemtype = 'DICT (eLGU BPLS)' OR systemtype = 'DICT (eLGU)' THEN 'DICT System'
                               ELSE 'Non-DICT System'
                           END AS implementation_status
                           FROM tblbpls
                           WHERE province = '$filter'
                           ORDER BY implementation_status DESC, lgu ASC";
                    break;

                case 'engagement_rate':
                    // Get all LGUs for engagement rate by province with calculation info
                    $sql = "SELECT *,
                           CASE
                               WHEN systemtype IN ('DICT (eLGU BPLS)', 'DICT (eLGU)', 'For Training', 'With own system/manual') THEN 'Engaged with DICT'
                               ELSE 'Not Engaged'
                           END AS engagement_status
                           FROM tblbpls
                           WHERE province = '$filter'
                           ORDER BY engagement_status DESC, lgu ASC";
                    break;

                default:
                    throw new Exception("Unknown statistic key: $stat_key");
            }

            // For most activity stats, use this standard query
            if (empty($sql) && !empty($filter_field)) {
                // Special case for activities with month filter
                if ($stat_key === 'activities' && !empty($year) && strpos($filter, ' ') !== false) {
                    // Extract month and year from filter (e.g., "January 2023")
                    $parts = explode(' ', $filter);
                    if (count($parts) === 2) {
                        $month_name = $parts[0];
                        $year_value = $parts[1];
                        $sql = "SELECT * FROM tblactivity
                                WHERE MONTHNAME(start) = '$month_name'
                                AND YEAR(start) = '$year_value'
                                $project_filter
                                ORDER BY start DESC";
                    } else {
                        $sql = "SELECT * FROM tblactivity WHERE $filter_field = '$filter' $project_filter $year_filter ORDER BY start DESC";
                    }
                } else {
                    $sql = "SELECT * FROM tblactivity WHERE $filter_field = '$filter' $project_filter $year_filter ORDER BY start DESC";
                }
            }

            if (empty($sql)) {
                throw new Exception("Could not determine query for statistic key: $stat_key");
            }

            $result = mysqli_query($conn, $sql);
            if (!$result) {
                throw new Exception("Database error: " . mysqli_error($conn));
            }

            // Check if this is a monitoring statistic
            $is_monitoring_stat = in_array($stat_key, ['total_lgus', 'dict_lgus', 'implementation_rate', 'engagement_rate']);

            if ($is_monitoring_stat) {
                $lgus = [];
                while ($row = mysqli_fetch_assoc($result)) {
                    $lgus[] = $row;
                }

                mysqli_free_result($result);

                $data['lgus'] = $lgus;
                $data['count'] = count($lgus);
                $data['filter'] = $filter;
                $data['stat_key'] = $stat_key;
            } else {
                $activities = [];
                while ($row = mysqli_fetch_assoc($result)) {
                    $activities[] = $row;
                }

                mysqli_free_result($result);

                $data['activities'] = $activities;
                $data['count'] = count($activities);
                $data['filter'] = $filter;
                $data['stat_key'] = $stat_key;
            }

            send_json_response(true, $data, 'Detailed statistics retrieved successfully.');

        } catch (Exception $e) {
            error_log("Statistics Details Error: " . $e->getMessage());
            send_json_response(false, [], 'Error retrieving detailed statistics: ' . $e->getMessage());
        }
        break;

    case 'getActivityYears':
        // Get available years for activities
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            send_json_response(false, [], 'Invalid request method.');
        }

        $project = isset($_POST['project']) ? sanitize_input($conn, $_POST['project']) : '';
        $project_filter = !empty($project) ? " AND project = '$project'" : "";

        try {
            // Query to get distinct years from activity start dates
            $sql = "SELECT DISTINCT YEAR(start) as year
                   FROM tblactivity
                   WHERE start IS NOT NULL $project_filter
                   ORDER BY year DESC";

            $result = mysqli_query($conn, $sql);
            if (!$result) {
                throw new Exception("Database error: " . mysqli_error($conn));
            }

            $years = [];
            while ($row = mysqli_fetch_assoc($result)) {
                $years[] = $row['year'];
            }

            mysqli_free_result($result);

            send_json_response(true, $years, 'Activity years retrieved successfully.');

        } catch (Exception $e) {
            error_log("Activity Years Error: " . $e->getMessage());
            send_json_response(false, [], 'Error retrieving activity years: ' . $e->getMessage());
        }
        break;

    case 'getStatBreakdown':
        // Get detailed breakdown for a specific statistic
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            send_json_response(false, [], 'Invalid request method.');
        }

        $stat_key = isset($_POST['stat_key']) ? sanitize_input($conn, $_POST['stat_key']) : '';
        $project = isset($_POST['project']) ? sanitize_input($conn, $_POST['project']) : '';
        $year = isset($_POST['year']) ? sanitize_input($conn, $_POST['year']) : '';

        if (empty($stat_key)) {
            send_json_response(false, [], 'Missing statistic key.');
        }

        $data = [];
        $error_message = '';
        $project_filter = !empty($project) ? " AND project = '$project'" : "";
        $year_filter = !empty($year) ? " AND YEAR(start) = '$year'" : "";

        try {
            switch ($stat_key) {
                case 'sectors':
                    // Get breakdown of sectors
                    $sql = "SELECT sector as name, COUNT(*) as count
                           FROM tblactivity
                           WHERE sector IS NOT NULL AND sector != ''
                           $project_filter
                           GROUP BY sector
                           ORDER BY count DESC, name ASC";
                    break;

                case 'agencies':
                    // Get breakdown of agencies
                    $sql = "SELECT agency as name, COUNT(*) as count
                           FROM tblactivity
                           WHERE agency IS NOT NULL AND agency != ''
                           $project_filter
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                case 'municipalities':
                    // Get breakdown of municipalities
                    $sql = "SELECT municipality as name, COUNT(*) as count
                           FROM tblactivity
                           WHERE municipality IS NOT NULL AND municipality != ''
                           $project_filter
                           GROUP BY municipality
                           ORDER BY count DESC, name ASC";
                    break;

                case 'barangays':
                    // Get breakdown of barangays
                    $sql = "SELECT barangay as name, COUNT(*) as count
                           FROM tblactivity
                           WHERE barangay IS NOT NULL AND barangay != ''
                           $project_filter
                           GROUP BY barangay
                           ORDER BY count DESC, name ASC";
                    break;

                case 'activities':
                    // If we have a year filter, group by month
                    if (!empty($year)) {
                        // Create a temporary table with all months
                        $months = [
                            'January', 'February', 'March', 'April', 'May', 'June',
                            'July', 'August', 'September', 'October', 'November', 'December'
                        ];

                        // Get activities by month for the selected year
                        $sql = "SELECT
                                MONTHNAME(start) as month_name,
                                COUNT(*) as count
                               FROM tblactivity
                               WHERE start IS NOT NULL
                               $project_filter
                               $year_filter
                               GROUP BY MONTH(start), MONTHNAME(start)
                               ORDER BY MONTH(start)";

                        $result = mysqli_query($conn, $sql);
                        if (!$result) {
                            throw new Exception("Database error: " . mysqli_error($conn));
                        }

                        // Create an array with all months initialized to 0
                        $monthly_data = [];
                        foreach ($months as $month) {
                            $monthly_data[$month] = 0;
                        }

                        // Fill in the actual counts
                        while ($row = mysqli_fetch_assoc($result)) {
                            $monthly_data[$row['month_name']] = (int)$row['count'];
                        }

                        mysqli_free_result($result);

                        // Convert to the format expected by the frontend
                        $data = [];
                        foreach ($monthly_data as $month => $count) {
                            $data[] = [
                                'name' => $month,
                                'count' => $count
                            ];
                        }

                        // Send the response directly
                        send_json_response(true, $data, 'Monthly activities retrieved successfully.');
                        return; // Exit the function
                    } else {
                        // Default behavior - group by indicator
                        $sql = "SELECT indicator as name, COUNT(*) as count
                               FROM tblactivity
                               WHERE indicator IS NOT NULL AND indicator != ''
                               $project_filter
                               GROUP BY indicator
                               ORDER BY count DESC, name ASC";
                    }
                    break;

                case 'participants':
                    // Get breakdown of participants by agency
                    $sql = "SELECT agency as name, SUM(participants) as count
                           FROM tblactivity
                           WHERE agency IS NOT NULL AND agency != ''
                           $project_filter
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                case 'participants_gender':
                    // Get breakdown of participants by gender
                    $sql = "SELECT 'Male' as name, SUM(male) as count
                           FROM tblactivity
                           WHERE male > 0
                           $project_filter
                           UNION
                           SELECT 'Female' as name, SUM(female) as count
                           FROM tblactivity
                           WHERE female > 0
                           $project_filter
                           ORDER BY name ASC";
                    break;

                case 'participants_mode':
                    // Get breakdown of participants by mode of implementation
                    $sql = "SELECT mode as name, SUM(participants) as count
                           FROM tblactivity
                           WHERE mode IS NOT NULL AND mode != ''
                           $project_filter
                           GROUP BY mode
                           ORDER BY count DESC, name ASC";
                    break;

                case 'district1':
                    // Get breakdown of District 1 activities by municipality
                    $sql = "SELECT municipality as name, COUNT(*) as count
                           FROM tblactivity
                           WHERE district = 'District 1 (Siargao Island)'
                           AND municipality IS NOT NULL AND municipality != ''
                           $project_filter
                           GROUP BY municipality
                           ORDER BY count DESC, name ASC";
                    break;

                case 'district2':
                    // Get breakdown of District 2 activities by municipality
                    $sql = "SELECT municipality as name, COUNT(*) as count
                           FROM tblactivity
                           WHERE district = 'District 2 (Mainland)'
                           AND municipality IS NOT NULL AND municipality != ''
                           $project_filter
                           GROUP BY municipality
                           ORDER BY count DESC, name ASC";
                    break;

                // Add cases for participant stats
                case 'p_total':
                    // Get breakdown of total participants by agency
                    $sql = "SELECT agency as name, COUNT(*) as count
                           FROM tblparticipant
                           WHERE agency IS NOT NULL AND agency != ''
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                // FreeWifi4All specific statistics
                case 'lgu_penetration':
                    // Get breakdown of LGU penetration by locality
                    $sql = "SELECT locality as name, COUNT(*) as count
                           FROM tblfwfa
                           WHERE locality IS NOT NULL AND locality != ''
                           GROUP BY locality
                           ORDER BY count DESC, name ASC";
                    break;

                case 'barangay_penetration':
                    // Get breakdown of barangay penetration by locality
                    $sql = "SELECT locality as name, COUNT(DISTINCT CONCAT(locality, ' ', barangay)) as count
                           FROM tblfwfa
                           WHERE locality IS NOT NULL AND locality != ''
                           AND barangay IS NOT NULL AND barangay != ''
                           GROUP BY locality
                           ORDER BY count DESC, name ASC";
                    break;

                case 'active_ap':
                    // Get breakdown of active access points by locality
                    $sql = "SELECT locality as name, COUNT(*) as count
                           FROM tblfwfa
                           WHERE status = 'Active'
                           AND locality IS NOT NULL AND locality != ''
                           GROUP BY locality
                           ORDER BY count DESC, name ASC";
                    break;

                case 'inactive_ap':
                    // Get breakdown of inactive access points by locality
                    $sql = "SELECT locality as name, COUNT(*) as count
                           FROM tblfwfa
                           WHERE status = 'Inactive'
                           AND locality IS NOT NULL AND locality != ''
                           GROUP BY locality
                           ORDER BY count DESC, name ASC";
                    break;

                // Letter requests statistics
                case 'l_localities':
                    // Get breakdown of letter requests by locality
                    $sql = "SELECT locality as name, COUNT(*) as count
                           FROM locationrequests
                           WHERE locality IS NOT NULL AND locality != ''
                           GROUP BY locality
                           ORDER BY count DESC, name ASC";
                    break;

                case 'l_barangays':
                    // Get breakdown of letter requests by barangay
                    $sql = "SELECT barangay as name, COUNT(*) as count
                           FROM locationrequests
                           WHERE barangay IS NOT NULL AND barangay != ''
                           GROUP BY barangay
                           ORDER BY count DESC, name ASC";
                    break;

                case 'l_provisions':
                    // Get breakdown of provisions by locality
                    $sql = "SELECT locality as name, COUNT(*) as count
                           FROM locationrequests
                           WHERE type = 'Provision'
                           AND locality IS NOT NULL AND locality != ''
                           GROUP BY locality
                           ORDER BY count DESC, name ASC";
                    break;

                case 'l_requests':
                    // Get breakdown of requests by locality
                    $sql = "SELECT locality as name, COUNT(*) as count
                           FROM locationrequests
                           WHERE type = 'Request'
                           AND locality IS NOT NULL AND locality != ''
                           GROUP BY locality
                           ORDER BY count DESC, name ASC";
                    break;

                case 'p_male':
                    // Get list of male participants by name
                    $sql = "SELECT fullname as name, 1 as count
                           FROM tblparticipant
                           WHERE sex = 'Male'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           ORDER BY name ASC";
                    break;

                case 'p_female':
                    // Get list of female participants by name
                    $sql = "SELECT fullname as name, 1 as count
                           FROM tblparticipant
                           WHERE sex = 'Female'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           ORDER BY name ASC";
                    break;

                case 'p_agencies':
                    // Get breakdown of unique agencies
                    $sql = "SELECT agency as name, COUNT(*) as count
                           FROM tblparticipant
                           WHERE agency IS NOT NULL AND agency != ''
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                case 'p_f2f':
                    // Get list of face-to-face participants by name
                    $sql = "SELECT fullname as name, 1 as count
                           FROM tblparticipant
                           WHERE mode = 'Face-to-Face'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           ORDER BY name ASC";
                    break;

                case 'p_virtual':
                    // Get list of virtual participants by name
                    $sql = "SELECT fullname as name, 1 as count
                           FROM tblparticipant
                           WHERE mode = 'Virtual'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           ORDER BY name ASC";
                    break;

                case 'p_training':
                    // Get list of training participants by name
                    $sql = "SELECT fullname as name, 1 as count
                           FROM tblparticipant
                           WHERE indicator = 'Training'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           ORDER BY name ASC";
                    break;

                case 'p_awareness':
                    // Get list of awareness participants by name
                    $sql = "SELECT fullname as name, 1 as count
                           FROM tblparticipant
                           WHERE indicator = 'Awareness'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           ORDER BY name ASC";
                    break;

                // Agency-based queries for chart view
                case 'p_male_agency':
                    // Get breakdown of male participants by agency
                    $sql = "SELECT agency as name, COUNT(*) as count
                           FROM tblparticipant
                           WHERE sex = 'Male'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                case 'p_female_agency':
                    // Get breakdown of female participants by agency
                    $sql = "SELECT agency as name, COUNT(*) as count
                           FROM tblparticipant
                           WHERE sex = 'Female'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                case 'p_f2f_agency':
                    // Get breakdown of face-to-face participants by agency
                    $sql = "SELECT agency as name, COUNT(*) as count
                           FROM tblparticipant
                           WHERE mode = 'Face-to-Face'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                case 'p_virtual_agency':
                    // Get breakdown of virtual participants by agency
                    $sql = "SELECT agency as name, COUNT(*) as count
                           FROM tblparticipant
                           WHERE mode = 'Virtual'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                case 'p_training_agency':
                    // Get breakdown of training participants by agency
                    $sql = "SELECT agency as name, COUNT(*) as count
                           FROM tblparticipant
                           WHERE indicator = 'Training'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                case 'p_awareness_agency':
                    // Get breakdown of awareness participants by agency
                    $sql = "SELECT agency as name, COUNT(*) as count
                           FROM tblparticipant
                           WHERE indicator = 'Awareness'
                           " . (!empty($project) ? " AND project = '$project'" : "") . "
                           GROUP BY agency
                           ORDER BY count DESC, name ASC";
                    break;

                // eLGU BPLS Monitoring Statistics - Return individual LGU records for detailed modal display
                case 'total_lgus':
                    // Get all LGUs for detailed display
                    $sql = "SELECT * FROM tblbpls ORDER BY lgu ASC";
                    // Special handling for LGU data - return as lgus array
                    $result = mysqli_query($conn, $sql);
                    if (!$result) {
                        throw new Exception("Database error: " . mysqli_error($conn));
                    }

                    $lgus = [];
                    while ($row = mysqli_fetch_assoc($result)) {
                        $lgus[] = $row;
                    }
                    mysqli_free_result($result);

                    // Return in format expected by frontend
                    send_json_response(true, ['lgus' => $lgus], 'Total LGUs retrieved successfully.');
                    return;

                case 'dict_lgus':
                    // Get LGUs using DICT systems for detailed display
                    $sql = "SELECT * FROM tblbpls
                           WHERE (systemtype = 'DICT (eLGU BPLS)' OR systemtype = 'DICT (eLGU)')
                           ORDER BY lgu ASC";
                    // Special handling for LGU data - return as lgus array
                    $result = mysqli_query($conn, $sql);
                    if (!$result) {
                        throw new Exception("Database error: " . mysqli_error($conn));
                    }

                    $lgus = [];
                    while ($row = mysqli_fetch_assoc($result)) {
                        $lgus[] = $row;
                    }
                    mysqli_free_result($result);

                    // Return in format expected by frontend
                    send_json_response(true, ['lgus' => $lgus], 'DICT LGUs retrieved successfully.');
                    return;

                case 'implementation_rate':
                    // Get all LGUs for implementation rate display
                    $sql = "SELECT *,
                           CASE
                               WHEN systemtype = 'DICT (eLGU BPLS)' OR systemtype = 'DICT (eLGU)' THEN 'DICT System'
                               ELSE 'Non-DICT System'
                           END AS implementation_status
                           FROM tblbpls
                           ORDER BY implementation_status DESC, lgu ASC";
                    // Special handling for LGU data - return as lgus array
                    $result = mysqli_query($conn, $sql);
                    if (!$result) {
                        throw new Exception("Database error: " . mysqli_error($conn));
                    }

                    $lgus = [];
                    while ($row = mysqli_fetch_assoc($result)) {
                        $lgus[] = $row;
                    }
                    mysqli_free_result($result);

                    // Return in format expected by frontend
                    send_json_response(true, ['lgus' => $lgus], 'Implementation rate LGUs retrieved successfully.');
                    return;

                case 'engagement_rate':
                    // Get all LGUs for engagement rate display
                    $sql = "SELECT *,
                           CASE
                               WHEN systemtype IN ('DICT (eLGU BPLS)', 'DICT (eLGU)', 'For Training', 'With own system/manual') THEN 'Engaged with DICT'
                               ELSE 'Not Engaged'
                           END AS engagement_status
                           FROM tblbpls
                           ORDER BY engagement_status DESC, lgu ASC";
                    // Special handling for LGU data - return as lgus array
                    $result = mysqli_query($conn, $sql);
                    if (!$result) {
                        throw new Exception("Database error: " . mysqli_error($conn));
                    }

                    $lgus = [];
                    while ($row = mysqli_fetch_assoc($result)) {
                        $lgus[] = $row;
                    }
                    mysqli_free_result($result);

                    // Return in format expected by frontend
                    send_json_response(true, ['lgus' => $lgus], 'Engagement rate LGUs retrieved successfully.');
                    return;

                default:
                    throw new Exception("Unknown statistic key: $stat_key");
            }

            $result = mysqli_query($conn, $sql);
            if (!$result) {
                throw new Exception("Database error: " . mysqli_error($conn));
            }

            while ($row = mysqli_fetch_assoc($result)) {
                // Ensure count is numeric
                $row['count'] = (int)$row['count'];
                $data[] = $row;
            }

            mysqli_free_result($result);

            // If no data found, return a default "No data" entry
            if (empty($data)) {
                $data[] = ['name' => 'No data available', 'count' => 0];
            }

            send_json_response(true, $data, 'Statistics breakdown retrieved successfully.');

        } catch (Exception $e) {
            error_log("Statistics Breakdown Error: " . $e->getMessage());
            send_json_response(false, [], 'Error retrieving statistics breakdown: ' . $e->getMessage());
        }
        break;

    case 'addTech4ed':
        // Add a new Tech4ED DTC
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            send_json_response(false, [], 'Invalid request method.');
        }

        // Sanitize all input fields
        $region = isset($_POST['region']) ? sanitize_input($conn, $_POST['region']) : '';
        $province = isset($_POST['province']) ? sanitize_input($conn, $_POST['province']) : '';
        $district = isset($_POST['district']) ? sanitize_input($conn, $_POST['district']) : '';
        $municipality = isset($_POST['municipality']) ? sanitize_input($conn, $_POST['municipality']) : '';
        $barangay = isset($_POST['barangay']) ? sanitize_input($conn, $_POST['barangay']) : '';
        $street = isset($_POST['street']) ? sanitize_input($conn, $_POST['street']) : '';
        $location = isset($_POST['location']) ? sanitize_input($conn, $_POST['location']) : '';
        $cname = isset($_POST['cname']) ? sanitize_input($conn, $_POST['cname']) : '';
        $host = isset($_POST['host']) ? sanitize_input($conn, $_POST['host']) : '';
        $category = isset($_POST['category']) ? sanitize_input($conn, $_POST['category']) : '';
        $longitude = isset($_POST['longitude']) ? sanitize_input($conn, $_POST['longitude']) : '';
        $latitude = isset($_POST['latitude']) ? sanitize_input($conn, $_POST['latitude']) : '';
        $cmanager = isset($_POST['cmanager']) ? sanitize_input($conn, $_POST['cmanager']) : '';
        $cemail = isset($_POST['cemail']) ? sanitize_input($conn, $_POST['cemail']) : '';
        $cmobile = isset($_POST['cmobile']) ? sanitize_input($conn, $_POST['cmobile']) : '';
        $clandline = isset($_POST['clandline']) ? sanitize_input($conn, $_POST['clandline']) : '';
        $cgender = isset($_POST['cgender']) ? sanitize_input($conn, $_POST['cgender']) : '';
        $amanager = isset($_POST['amanager']) ? sanitize_input($conn, $_POST['amanager']) : '';
        $aemail = isset($_POST['aemail']) ? sanitize_input($conn, $_POST['aemail']) : '';
        $amobile = isset($_POST['amobile']) ? sanitize_input($conn, $_POST['amobile']) : '';
        $alandline = isset($_POST['alandline']) ? sanitize_input($conn, $_POST['alandline']) : '';
        $agender = isset($_POST['agender']) ? sanitize_input($conn, $_POST['agender']) : '';
        $launch = isset($_POST['launch']) && !empty($_POST['launch']) ? sanitize_input($conn, $_POST['launch']) : null;
        $registration = isset($_POST['registration']) && !empty($_POST['registration']) ? sanitize_input($conn, $_POST['registration']) : null;
        $operation = isset($_POST['operation']) && !empty($_POST['operation']) ? sanitize_input($conn, $_POST['operation']) : null;
        $visited = isset($_POST['visited']) && !empty($_POST['visited']) ? sanitize_input($conn, $_POST['visited']) : null;
        $desktop = isset($_POST['desktop']) ? intval($_POST['desktop']) : 0;
        $laptop = isset($_POST['laptop']) ? intval($_POST['laptop']) : 0;
        $printer = isset($_POST['printer']) ? intval($_POST['printer']) : 0;
        $scanner = isset($_POST['scanner']) ? intval($_POST['scanner']) : 0;
        $status = isset($_POST['status']) ? sanitize_input($conn, $_POST['status']) : '';
        $network = isset($_POST['network']) ? sanitize_input($conn, $_POST['network']) : '';
        $connectivity = isset($_POST['connectivity']) ? sanitize_input($conn, $_POST['connectivity']) : '';
        $speed = isset($_POST['speed']) ? sanitize_input($conn, $_POST['speed']) : '';
        $cmtmale = isset($_POST['cmtmale']) ? intval($_POST['cmtmale']) : 0;
        $cmtfemale = isset($_POST['cmtfemale']) ? intval($_POST['cmtfemale']) : 0;
        $straining = isset($_POST['straining']) ? sanitize_input($conn, $_POST['straining']) : '';
        $etraining = isset($_POST['etraining']) ? sanitize_input($conn, $_POST['etraining']) : '';
        $signing = isset($_POST['signing']) ? sanitize_input($conn, $_POST['signing']) : '';
        $partner = isset($_POST['partner']) ? sanitize_input($conn, $_POST['partner']) : '';
        $expiration = isset($_POST['expiration']) && !empty($_POST['expiration']) ? sanitize_input($conn, $_POST['expiration']) : null;
        $donation = isset($_POST['donation']) ? sanitize_input($conn, $_POST['donation']) : '';
        $datedonation = isset($_POST['datedonation']) && !empty($_POST['datedonation']) ? sanitize_input($conn, $_POST['datedonation']) : null;
        $tcms = isset($_POST['tcms']) ? sanitize_input($conn, $_POST['tcms']) : '';
        $key_one = isset($_POST['key_one']) ? sanitize_input($conn, $_POST['key_one']) : '';
        $identifier = isset($_POST['identifier']) ? sanitize_input($conn, $_POST['identifier']) : '';

        // Validate required fields
        if (empty($cname)) {
            send_json_response(false, [], 'Center Name is required.');
        }

        // Prepare SQL statement
        $sql = "INSERT INTO tbltech4ed (
            region, province, district, municipality, barangay, street, location,
            cname, host, category, longitude, latitude,
            cmanager, cemail, cmobile, clandline, cgender,
            amanager, aemail, amobile, alandline, agender,
            launch, registration, operation, visited,
            desktop, laptop, printer, scanner,
            status, network, connectivity, speed,
            cmtmale, cmtfemale, straining, etraining,
            signing, partner, expiration, donation, datedonation,
            tcms, key_one, identifier
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?,
            ?, ?, ?, ?,
            ?, ?, ?, ?,
            ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?, ?
        )";

        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . mysqli_error($conn));
            send_json_response(false, [], 'Database error: ' . mysqli_error($conn));
        }

        // Count parameters to ensure they match the binding string
        $param_count = 44; // 44 fields for insert
        $binding_string = "ssssssssssssssssssssssssssiiiisisssiisssssssss";

        if (strlen($binding_string) !== $param_count) {
            error_log("Add Tech4ED Parameter Count Mismatch: Expected $param_count, got " . strlen($binding_string));
        }

        // Bind parameters
        mysqli_stmt_bind_param($stmt,
            $binding_string,
            $region, $province, $district, $municipality, $barangay, $street, $location,
            $cname, $host, $category, $longitude, $latitude,
            $cmanager, $cemail, $cmobile, $clandline, $cgender,
            $amanager, $aemail, $amobile, $alandline, $agender,
            $launch, $registration, $operation, $visited,
            $desktop, $laptop, $printer, $scanner,
            $status, $network, $connectivity, $speed,
            $cmtmale, $cmtfemale, $straining, $etraining,
            $signing, $partner, $expiration, $donation, $datedonation,
            $tcms, $key_one, $identifier
        );

        // Execute statement
        if (mysqli_stmt_execute($stmt)) {
            $tech4ed_id = mysqli_insert_id($conn);
            mysqli_stmt_close($stmt);

            // Log the action
            if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Added Tech4ED DTC",
                    "add",
                    $tech4ed_id,
                    "tech4ed",
                    "Tech4ED DTC ID: " . $tech4ed_id . ", Name: " . $cname
                );
            }

            send_json_response(true, ['id' => $tech4ed_id], 'Tech4ED DTC added successfully.');
        } else {
            error_log("Error executing statement: " . mysqli_stmt_error($stmt));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'Error adding Tech4ED DTC: ' . mysqli_error($conn));
        }
        break;

    case 'getTech4ed':
        // Get Tech4ED DTC data for editing
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) {
            send_json_response(false, [], 'Invalid or missing Tech4ED DTC ID.');
        }

        $sql = "SELECT * FROM tbltech4ed WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);

        if (!$stmt) {
            error_log("Get Tech4ED DTC Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB error preparing query.');
        }

        mysqli_stmt_bind_param($stmt, "i", $id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $data = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);

        if ($data) {
            send_json_response(true, $data);
        } else {
            send_json_response(false, [], 'Tech4ED DTC not found.');
        }
        break;

    case 'updateTech4ed':
        // Update Tech4ED DTC
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            send_json_response(false, [], 'Invalid request method.');
        }

        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) {
            send_json_response(false, [], 'Invalid or missing Tech4ED DTC ID.');
        }

        // Sanitize all input fields
        $region = isset($_POST['region']) ? sanitize_input($conn, $_POST['region']) : '';
        $province = isset($_POST['province']) ? sanitize_input($conn, $_POST['province']) : '';
        $district = isset($_POST['district']) ? sanitize_input($conn, $_POST['district']) : '';
        $municipality = isset($_POST['municipality']) ? sanitize_input($conn, $_POST['municipality']) : '';
        $barangay = isset($_POST['barangay']) ? sanitize_input($conn, $_POST['barangay']) : '';
        $street = isset($_POST['street']) ? sanitize_input($conn, $_POST['street']) : '';
        $location = isset($_POST['location']) ? sanitize_input($conn, $_POST['location']) : '';
        $cname = isset($_POST['cname']) ? sanitize_input($conn, $_POST['cname']) : '';
        $host = isset($_POST['host']) ? sanitize_input($conn, $_POST['host']) : '';
        $category = isset($_POST['category']) ? sanitize_input($conn, $_POST['category']) : '';
        $longitude = isset($_POST['longitude']) ? sanitize_input($conn, $_POST['longitude']) : '';
        $latitude = isset($_POST['latitude']) ? sanitize_input($conn, $_POST['latitude']) : '';
        $cmanager = isset($_POST['cmanager']) ? sanitize_input($conn, $_POST['cmanager']) : '';
        $cemail = isset($_POST['cemail']) ? sanitize_input($conn, $_POST['cemail']) : '';
        $cmobile = isset($_POST['cmobile']) ? sanitize_input($conn, $_POST['cmobile']) : '';
        $clandline = isset($_POST['clandline']) ? sanitize_input($conn, $_POST['clandline']) : '';
        $cgender = isset($_POST['cgender']) ? sanitize_input($conn, $_POST['cgender']) : '';
        $amanager = isset($_POST['amanager']) ? sanitize_input($conn, $_POST['amanager']) : '';
        $aemail = isset($_POST['aemail']) ? sanitize_input($conn, $_POST['aemail']) : '';
        $amobile = isset($_POST['amobile']) ? sanitize_input($conn, $_POST['amobile']) : '';
        $alandline = isset($_POST['alandline']) ? sanitize_input($conn, $_POST['alandline']) : '';
        $agender = isset($_POST['agender']) ? sanitize_input($conn, $_POST['agender']) : '';
        $launch = isset($_POST['launch']) && !empty($_POST['launch']) ? sanitize_input($conn, $_POST['launch']) : null;
        $registration = isset($_POST['registration']) && !empty($_POST['registration']) ? sanitize_input($conn, $_POST['registration']) : null;
        $operation = isset($_POST['operation']) && !empty($_POST['operation']) ? sanitize_input($conn, $_POST['operation']) : null;
        $visited = isset($_POST['visited']) && !empty($_POST['visited']) ? sanitize_input($conn, $_POST['visited']) : null;
        $desktop = isset($_POST['desktop']) ? intval($_POST['desktop']) : 0;
        $laptop = isset($_POST['laptop']) ? intval($_POST['laptop']) : 0;
        $printer = isset($_POST['printer']) ? intval($_POST['printer']) : 0;
        $scanner = isset($_POST['scanner']) ? intval($_POST['scanner']) : 0;
        $status = isset($_POST['status']) ? sanitize_input($conn, $_POST['status']) : '';
        $network = isset($_POST['network']) ? sanitize_input($conn, $_POST['network']) : '';
        $connectivity = isset($_POST['connectivity']) ? sanitize_input($conn, $_POST['connectivity']) : '';
        $speed = isset($_POST['speed']) ? sanitize_input($conn, $_POST['speed']) : '';
        $cmtmale = isset($_POST['cmtmale']) ? intval($_POST['cmtmale']) : 0;
        $cmtfemale = isset($_POST['cmtfemale']) ? intval($_POST['cmtfemale']) : 0;
        $straining = isset($_POST['straining']) ? sanitize_input($conn, $_POST['straining']) : '';
        $etraining = isset($_POST['etraining']) ? sanitize_input($conn, $_POST['etraining']) : '';
        $signing = isset($_POST['signing']) ? sanitize_input($conn, $_POST['signing']) : '';
        $partner = isset($_POST['partner']) ? sanitize_input($conn, $_POST['partner']) : '';
        $expiration = isset($_POST['expiration']) && !empty($_POST['expiration']) ? sanitize_input($conn, $_POST['expiration']) : null;
        $donation = isset($_POST['donation']) ? sanitize_input($conn, $_POST['donation']) : '';
        $datedonation = isset($_POST['datedonation']) && !empty($_POST['datedonation']) ? sanitize_input($conn, $_POST['datedonation']) : null;
        $tcms = isset($_POST['tcms']) ? sanitize_input($conn, $_POST['tcms']) : '';
        $key_one = isset($_POST['key_one']) ? sanitize_input($conn, $_POST['key_one']) : '';
        $identifier = isset($_POST['identifier']) ? sanitize_input($conn, $_POST['identifier']) : '';

        // Validate required fields
        if (empty($cname)) {
            send_json_response(false, [], 'Center Name is required.');
        }

        // Prepare SQL statement
        $sql = "UPDATE tbltech4ed SET
            region = ?, province = ?, district = ?, municipality = ?, barangay = ?, street = ?, location = ?,
            cname = ?, host = ?, category = ?, longitude = ?, latitude = ?,
            cmanager = ?, cemail = ?, cmobile = ?, clandline = ?, cgender = ?,
            amanager = ?, aemail = ?, amobile = ?, alandline = ?, agender = ?,
            launch = ?, registration = ?, operation = ?, visited = ?,
            desktop = ?, laptop = ?, printer = ?, scanner = ?,
            status = ?, network = ?, connectivity = ?, speed = ?,
            cmtmale = ?, cmtfemale = ?, straining = ?, etraining = ?,
            signing = ?, partner = ?, expiration = ?, donation = ?, datedonation = ?,
            tcms = ?, key_one = ?, identifier = ?
            WHERE id = ?";

        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Error preparing update statement: " . mysqli_error($conn));
            send_json_response(false, [], 'Database error: ' . mysqli_error($conn));
        }

        // Count parameters to ensure they match the binding string
        $param_count = 45; // 44 fields + 1 ID
        $binding_string = "ssssssssssssssssssssssssssiiiisisssiisssssssssi";

        if (strlen($binding_string) !== $param_count) {
            error_log("Update Tech4ED Parameter Count Mismatch: Expected $param_count, got " . strlen($binding_string));
        }

        // Bind parameters
        mysqli_stmt_bind_param($stmt,
            $binding_string,
            $region, $province, $district, $municipality, $barangay, $street, $location,
            $cname, $host, $category, $longitude, $latitude,
            $cmanager, $cemail, $cmobile, $clandline, $cgender,
            $amanager, $aemail, $amobile, $alandline, $agender,
            $launch, $registration, $operation, $visited,
            $desktop, $laptop, $printer, $scanner,
            $status, $network, $connectivity, $speed,
            $cmtmale, $cmtfemale, $straining, $etraining,
            $signing, $partner, $expiration, $donation, $datedonation,
            $tcms, $key_one, $identifier,
            $id
        );

        // Execute statement
        if (mysqli_stmt_execute($stmt)) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);

            // Log the action
            if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Updated Tech4ED DTC",
                    "edit",
                    $id,
                    "tech4ed",
                    "Tech4ED DTC ID: " . $id . ", Name: " . $cname
                );
            }

            if ($affected_rows > 0) {
                send_json_response(true, ['id' => $id], 'Tech4ED DTC updated successfully.');
            } else {
                // Check if the Tech4ED DTC exists
                $check_sql = "SELECT id FROM tbltech4ed WHERE id = ?";
                $check_stmt = mysqli_prepare($conn, $check_sql);
                mysqli_stmt_bind_param($check_stmt, "i", $id);
                mysqli_stmt_execute($check_stmt);
                mysqli_stmt_store_result($check_stmt);
                $exists = mysqli_stmt_num_rows($check_stmt) > 0;
                mysqli_stmt_close($check_stmt);

                if ($exists) {
                    send_json_response(true, ['id' => $id], 'No changes were made to the Tech4ED DTC.');
                } else {
                    send_json_response(false, [], 'Tech4ED DTC not found.');
                }
            }
        } else {
            error_log("Error executing update statement: " . mysqli_stmt_error($stmt));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'Error updating Tech4ED DTC: ' . mysqli_error($conn));
        }
        break;

    case 'getActivity':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing Activity ID.'); }
        $sql = "SELECT * FROM tblactivity WHERE id = ?"; $stmt = mysqli_prepare($conn, $sql); mysqli_stmt_bind_param($stmt, "i", $id); mysqli_stmt_execute($stmt); $result = mysqli_stmt_get_result($stmt); $data = mysqli_fetch_assoc($result); mysqli_stmt_close($stmt);
        if ($data) { send_json_response(true, $data); } else { send_json_response(false, [], 'Activity not found.'); }
        break;
    case 'getInventory':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing Inventory ID.'); }
        $sql = "SELECT `id`, `project`, `item`, `classification`, `quantity`, `unit`, `description`, `received`, `property`, `ics`, `serial`, `date`, `officer`, `cost`, `life`, `transferred`, `remarks` FROM `inventory` WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $data = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        if ($data) { send_json_response(true, $data); } else { send_json_response(false, [], 'Inventory item not found.'); }
        break;
    case 'addActivity':
        $allowed_fields = [ 'start' => 's', 'end' => 's', 'project' => 's', 'subproject' => 's', 'activity' => 's', 'indicator' => 's', 'training' => 's', 'municipality' => 's', 'district' => 's', 'barangay' => 's', 'agency' => 's', 'mode' => 's', 'sector' => 's', 'person' => 's', 'resource' => 's', 'participants' => 'i', 'completers' => 'i', 'male' => 'i', 'female' => 'i', 'approved' => 's', 'mov' => 's', 'remarks' => 's' ];
        if (!isset($_POST['activity']) || trim($_POST['activity']) === '') { send_json_response(false, [], 'Activity name is required.'); }
        $insert_fields = []; $insert_values = []; $params = []; $types = "";
        foreach ($allowed_fields as $field => $type) {
            if (isset($_POST[$field])) {
                $value = trim($_POST[$field]);
                if (($field === 'start' || $field === 'end') && $value === '') { $value = null; }
                elseif (($type === 'i' || $type === 'd') && $value === '') { $value = 0; }
                elseif ($value === '') { $value = null; }
                $insert_fields[] = "`" . $field . "`";
                $insert_values[] = "?";
                $types .= $type;
                if ($value === null) { $params[] = null; }
                elseif ($type === 'i') $params[] = (int)$value;
                elseif ($type === 'd') $params[] = (float)$value;
                else $params[] = sanitize_input($conn, $value);
            }
        }
        if (empty($insert_fields)) { send_json_response(false, [], 'No valid data provided.'); }
        $sql = "INSERT INTO tblactivity (" . implode(', ', $insert_fields) . ") VALUES (" . implode(', ', $insert_values) . ")"; $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Add Activity DB Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing insert.'); }
        $bind_params_ref = []; foreach ($params as $key => $value) { $bind_params_ref[$key] = &$params[$key]; }
        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);
        if (mysqli_stmt_execute($stmt)) {
            $new_id = mysqli_insert_id($conn);
            mysqli_stmt_close($stmt);
            if ($new_id) {
                // Log the activity addition
                if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                    add_log_entry(
                        $_SESSION['user_id'],
                        $_SESSION['username'],
                        "Added new activity",
                        "add",
                        $new_id,
                        "activity",
                        "Activity ID: " . $new_id . ", Name: " . ($_POST['activity'] ?? 'Unknown')
                    );
                }
                send_json_response(true, ['id' => $new_id], 'Activity added successfully.');
            } else {
                send_json_response(false, [], 'Failed to get ID of new activity.');
            }
        }
        else { $db_error = mysqli_stmt_error($stmt); mysqli_stmt_close($stmt); error_log("Add Activity DB Execute Error: " . $db_error); send_json_response(false, [], 'DB Error executing insert.'); }
        break;
    case 'updateActivity':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing Activity ID.'); }
        $allowed_fields = [ 'start' => 's', 'end' => 's', 'project' => 's', 'subproject' => 's', 'activity' => 's', 'indicator' => 's', 'training' => 's', 'municipality' => 's', 'district' => 's', 'barangay' => 's', 'agency' => 's', 'mode' => 's', 'sector' => 's', 'person' => 's', 'resource' => 's', 'participants' => 'i', 'completers' => 'i', 'male' => 'i', 'female' => 'i', 'approved' => 's', 'mov' => 's', 'remarks' => 's' ];
        $params = []; $types = ""; $sql_parts = [];
        foreach ($allowed_fields as $field => $type) {
            if (array_key_exists($field, $_POST)) {
                $value = trim($_POST[$field]);
                if (($field === 'start' || $field === 'end') && $value === '') { $value = null; }
                elseif (($type === 'i' || $type === 'd') && $value === '') { $value = 0; }
                elseif ($value === '') { $value = null; }
                $sql_parts[] = "`" . $field . "` = ?";
                $types .= $type;
                if ($value === null) { $params[] = null; }
                elseif ($type === 'i') { $params[] = (int)$value; }
                elseif ($type === 'd') { $params[] = (float)$value; }
                else { $params[] = sanitize_input($conn, $value); }
            }
        }
        if (empty($sql_parts)) { send_json_response(false, [], 'No valid data provided for update.'); }
        $sql = "UPDATE tblactivity SET " . implode(', ', $sql_parts) . " WHERE id = ?"; $types .= 'i'; $params[] = $id; $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Update Activity DB Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing update.'); }
        if (count($params) !== strlen($types)) { error_log("Update Activity Param/Type mismatch: P=" . count($params) . " T=" . strlen($types)); mysqli_stmt_close($stmt); send_json_response(false, [], 'Internal error during update preparation.'); }
        $bind_params_ref = []; foreach ($params as $key => $value) { $bind_params_ref[$key] = &$params[$key]; }
        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);
        if (mysqli_stmt_execute($stmt)) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);

            if ($affected_rows > 0) {
                // Log the activity update
                if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                    add_log_entry(
                        $_SESSION['user_id'],
                        $_SESSION['username'],
                        "Updated activity",
                        "edit",
                        $id,
                        "activity",
                        "Activity ID: " . $id . ", Name: " . ($_POST['activity'] ?? 'Unknown')
                    );
                }
                send_json_response(true, [], 'Activity updated successfully.');
            }
            else {
                $check_stmt = mysqli_prepare($conn, "SELECT id FROM tblactivity WHERE id = ?");
                mysqli_stmt_bind_param($check_stmt, "i", $id);
                mysqli_stmt_execute($check_stmt);
                mysqli_stmt_store_result($check_stmt);
                $exists = mysqli_stmt_num_rows($check_stmt) > 0;
                mysqli_stmt_close($check_stmt);
                if ($exists) {
                    send_json_response(true, [], 'No changes were made.');
                } else {
                    send_json_response(false, [], 'Activity not found for update.');
                }
            }
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Update Activity DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error executing update.');
        }
        break;
    case 'updateInventory':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing Inventory ID.'); }
        $allowed_fields = [ 'project' => 's', 'item' => 's', 'classification' => 's', 'quantity' => 'i', 'unit' => 's', 'description' => 's', 'received' => 's', 'property' => 's', 'ics' => 's', 'serial' => 's', 'date' => 's', 'officer' => 's', 'cost' => 'd', 'life' => 's', 'transferred' => 's', 'remarks' => 's' ];
        $params = []; $types = ""; $sql_parts = [];
        foreach ($allowed_fields as $field => $type) {
            if (array_key_exists($field, $_POST)) {
                $value = trim($_POST[$field]);
                if ($field === 'date' && $value === '') { $value = null; }
                elseif (($type === 'i' || $type === 'd') && $value === '') { $value = 0; }
                elseif ($value === '') { $value = null; }
                $sql_parts[] = "`" . $field . "` = ?";
                $types .= $type;
                if ($value === null) { $params[] = null; }
                elseif ($type === 'i') { $params[] = (int)$value; }
                elseif ($type === 'd') { $params[] = (float)$value; }
                else { $params[] = sanitize_input($conn, $value); }
            }
        }
        if (empty($sql_parts)) { send_json_response(false, [], 'No valid data provided for update.'); }
        $sql = "UPDATE inventory SET " . implode(', ', $sql_parts) . " WHERE id = ?"; $types .= 'i'; $params[] = $id; $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Update Inventory DB Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing update.'); }
        if (count($params) !== strlen($types)) { error_log("Update Inventory Param/Type mismatch: P=" . count($params) . " T=" . strlen($types)); mysqli_stmt_close($stmt); send_json_response(false, [], 'Internal error during update preparation.'); }
        $bind_params_ref = []; foreach ($params as $key => $value) { $bind_params_ref[$key] = &$params[$key]; }
        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);
        if (mysqli_stmt_execute($stmt)) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);

            if ($affected_rows > 0) {
                // Log the inventory update
                if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                    add_log_entry(
                        $_SESSION['user_id'],
                        $_SESSION['username'],
                        "Updated inventory item",
                        "edit",
                        $id,
                        "inventory",
                        "Inventory ID: " . $id . ", Item: " . ($_POST['item'] ?? 'Unknown')
                    );
                }
                send_json_response(true, [], 'Inventory item updated successfully.');
            }
            else {
                $check_stmt = mysqli_prepare($conn, "SELECT id FROM inventory WHERE id = ?");
                mysqli_stmt_bind_param($check_stmt, "i", $id);
                mysqli_stmt_execute($check_stmt);
                mysqli_stmt_store_result($check_stmt);
                $exists = mysqli_stmt_num_rows($check_stmt) > 0;
                mysqli_stmt_close($check_stmt);
                if ($exists) {
                    send_json_response(true, [], 'No changes were made.');
                } else {
                    send_json_response(false, [], 'Inventory item not found for update.');
                }
            }
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Update Inventory DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error executing update.');
        }
        break;
    case 'deleteActivities':
        $ids = $_POST['ids'] ?? [];
        if (empty($ids) || !is_array($ids)) { send_json_response(false, [], 'No Activity IDs provided.'); }
        $sanitized_ids = []; foreach ($ids as $id) { if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) $sanitized_ids[] = $validated_id; }
        if (empty($sanitized_ids)) { send_json_response(false, [], 'Invalid Activity IDs provided.'); }
        $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?')); $types = str_repeat('i', count($sanitized_ids));
        // --- File Deletion ---
        $sql_get_files = "SELECT file_id, filepath FROM tblactivity_files WHERE activity_id IN ($placeholders)"; $stmt_get_files = mysqli_prepare($conn, $sql_get_files);
        if ($stmt_get_files) {
            $bind_params_files_ref = []; foreach ($sanitized_ids as $key => $value) { $bind_params_files_ref[$key] = &$sanitized_ids[$key]; }
            mysqli_stmt_bind_param($stmt_get_files, $types, ...$bind_params_files_ref);
            mysqli_stmt_execute($stmt_get_files);
            $result_files = mysqli_stmt_get_result($stmt_get_files);
            $files_to_delete = mysqli_fetch_all($result_files, MYSQLI_ASSOC);
            mysqli_stmt_close($stmt_get_files);
            $file_ids_to_delete_db = [];
            foreach ($files_to_delete as $file) {
                 $real_filepath = realpath($file['filepath'] ?? '');
                if ($file && !empty($file['filepath']) && $real_filepath && file_exists($real_filepath)) {
                    if (!@unlink($real_filepath)) { error_log("Delete Activities: Failed file unlink: " . $real_filepath); }
                    else { $file_ids_to_delete_db[] = $file['file_id']; }
                } elseif ($file && !empty($file['file_id'])) { $file_ids_to_delete_db[] = $file['file_id']; }
            }
            if (!empty($file_ids_to_delete_db)) {
                 $unique_file_ids = array_unique($file_ids_to_delete_db);
                $file_placeholders = implode(',', array_fill(0, count($unique_file_ids), '?'));
                $file_types = str_repeat('i', count($unique_file_ids));
                $sql_del_files = "DELETE FROM tblactivity_files WHERE file_id IN ($file_placeholders)";
                $stmt_del_files = mysqli_prepare($conn, $sql_del_files);
                if ($stmt_del_files) {
                    $bind_params_del_files_ref = []; $i=0; foreach($unique_file_ids as $fid){$bind_params_del_files_ref[$i++]=$fid;}
                    mysqli_stmt_bind_param($stmt_del_files, $file_types, ...$bind_params_del_files_ref);
                    if (!mysqli_stmt_execute($stmt_del_files)) { error_log("Delete Activities: Failed DB file delete: " . mysqli_stmt_error($stmt_del_files)); }
                    mysqli_stmt_close($stmt_del_files);
                } else { error_log("Delete Activities: Failed prepare DB file delete: " . mysqli_error($conn)); }
            }
        } else { error_log("Delete Activities: Failed prepare fetch files: " . mysqli_error($conn)); }
        // --- Delete Activities ---
        $sql_act = "DELETE FROM tblactivity WHERE id IN ($placeholders)"; $stmt_act = mysqli_prepare($conn, $sql_act);
        if (!$stmt_act) { error_log("Delete Activities: Failed prepare activity delete: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing activity deletion.'); }
        $bind_params_act_ref = []; foreach ($sanitized_ids as $key => $value) { $bind_params_act_ref[$key] = &$sanitized_ids[$key]; }
        mysqli_stmt_bind_param($stmt_act, $types, ...$bind_params_act_ref);
        if (mysqli_stmt_execute($stmt_act)) {
            $deleted_count = mysqli_stmt_affected_rows($stmt_act);
            mysqli_stmt_close($stmt_act);

            // Log the activity deletion
            if (isset($_SESSION['user_id']) && isset($_SESSION['username']) && $deleted_count > 0) {
                $ids_str = implode(', ', $sanitized_ids);
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Deleted activities",
                    "delete",
                    null,
                    "activity",
                    "Deleted $deleted_count activities. IDs: $ids_str"
                );
            }

            send_json_response(true, [], "$deleted_count Activity(ies) deleted successfully.");
        }
        else {
            $db_error = mysqli_stmt_error($stmt_act);
            mysqli_stmt_close($stmt_act);
            error_log("Delete Activities: DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error deleting activities.');
        }
        break;
    case 'deleteInventory':
        $ids = $_POST['ids'] ?? [];
        if (empty($ids) || !is_array($ids)) { send_json_response(false, [], 'No Inventory IDs provided.'); }
        $sanitized_ids = []; foreach ($ids as $id) { if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) $sanitized_ids[] = $validated_id; }
        if (empty($sanitized_ids)) { send_json_response(false, [], 'Invalid Inventory IDs provided.'); }
        $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?')); $types = str_repeat('i', count($sanitized_ids));

        // --- Delete Inventory Items ---
        $sql_inv = "DELETE FROM inventory WHERE id IN ($placeholders)"; $stmt_inv = mysqli_prepare($conn, $sql_inv);
        if (!$stmt_inv) { error_log("Delete Inventory: Failed prepare inventory delete: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing inventory deletion.'); }
        $bind_params_inv_ref = []; foreach ($sanitized_ids as $key => $value) { $bind_params_inv_ref[$key] = &$sanitized_ids[$key]; }
        mysqli_stmt_bind_param($stmt_inv, $types, ...$bind_params_inv_ref);
        if (mysqli_stmt_execute($stmt_inv)) {
            $deleted_count = mysqli_stmt_affected_rows($stmt_inv);
            mysqli_stmt_close($stmt_inv);

            // Log the inventory deletion
            if (isset($_SESSION['user_id']) && isset($_SESSION['username']) && $deleted_count > 0) {
                $ids_str = implode(', ', $sanitized_ids);
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Deleted inventory items",
                    "delete",
                    null,
                    "inventory",
                    "Deleted $deleted_count inventory items. IDs: $ids_str"
                );
            }

            send_json_response(true, [], "$deleted_count Inventory item(s) deleted successfully.");
        }
        else {
            $db_error = mysqli_stmt_error($stmt_inv);
            mysqli_stmt_close($stmt_inv);
            error_log("Delete Inventory: DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error deleting inventory items.');
        }
        break;
    case 'getParticipant':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing ID.'); }

        // Check if the referrer contains freewifi4all.php
        $is_fw4a_context = false;
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
            $is_fw4a_context = true;
        }

        if ($is_fw4a_context) {
            // Fetch from tblfwfa for FW4A context
            $sql = "SELECT `id`, `locality`, `barangay`, `district`, `locations`, `type`, `code`, `strategy`, `status`, `reason`, `remarks` FROM `tblfwfa` WHERE id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "i", $id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $data = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            if ($data) { send_json_response(true, $data); }
            else { send_json_response(false, [], 'LGU entry not found in FW4A table.'); }
        } else {
            // Default: Fetch from tblparticipant
            $sql = "SELECT `id`, `start`, `end`, `activity`, `indicator`, `fullname`, `sex`, `contact`, `email`, `mode`, `agency`, `sector`, `project`, `person`, `remarks` FROM `tblparticipant` WHERE id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "i", $id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $data = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            if ($data) { send_json_response(true, $data); }
            else { send_json_response(false, [], 'Participant not found.'); }
        }
        break;

    case 'getLetter':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing Letter ID.'); }
        $sql = "SELECT `id`, `locality`, `barangay`, `district`, `location`, `date`, `year`, `type`, `status`, `accomplished`, `remarks` FROM `locationrequests` WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Get Letter DB Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing query.'); }
        mysqli_stmt_bind_param($stmt, "i", $id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $data = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        if ($data) { send_json_response(true, $data); } else { send_json_response(false, [], 'Letter not found.'); }
        break;
    case 'getAllLGUs':
        // Fetch all LGUs for the status table and reports
        // Fetch all columns to support dynamic module detection
        $sql = "SELECT * FROM `tblbpls` ORDER BY `lgu` ASC";
        $stmt = mysqli_prepare($conn, $sql);

        if (!$stmt) {
            error_log("Get All LGUs Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB error preparing query.');
        }

        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $lgus = mysqli_fetch_all($result, MYSQLI_ASSOC);
        mysqli_stmt_close($stmt);

        send_json_response(true, $lgus);
        break;

    case 'getLGU':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing LGU ID.'); }
        $sql = "SELECT * FROM `tblbpls` WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $data = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        if ($data) {
            send_json_response(true, $data);
        } else {
            send_json_response(false, [], 'LGU not found.');
        }
        break;
    case 'addParticipant':
        // Check if the referrer contains freewifi4all.php
        $is_fw4a_context = false;
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
            $is_fw4a_context = true;
        }

        if ($is_fw4a_context) {
            // FW4A context: Add LGU to tblfwfa
            $allowed_fields = [
                'locality' => 's', 'barangay' => 's', 'district' => 's', 'locations' => 's',
                'type' => 's', 'code' => 's', 'strategy' => 's', 'status' => 's',
                'reason' => 's', 'remarks' => 's'
            ];

            // Validate required field for FW4A
            if (!isset($_POST['locality']) || trim($_POST['locality']) === '') {
                send_json_response(false, [], 'Locality is required.');
            }

            $table_name = 'tblfwfa';
            $success_message = 'LGU added successfully.';
            $error_prefix = 'Add LGU';
        } else {
            // Regular context: Add participant to tblparticipant
            $allowed_fields = [
                'start' => 's', 'end' => 's', 'activity' => 's', 'indicator' => 's',
                'fullname' => 's', 'sex' => 's', 'contact' => 's', 'email' => 's',
                'mode' => 's', 'agency' => 's', 'sector' => 's', 'project' => 's',
                'person' => 's', 'remarks' => 's'
            ];

            // Validate required field for participant
            if (!isset($_POST['fullname']) || trim($_POST['fullname']) === '') {
                send_json_response(false, [], 'Participant name is required.');
            }

            $table_name = 'tblparticipant';
            $success_message = 'Participant added successfully.';
            $error_prefix = 'Add Participant';
        }

        $insert_fields = []; $insert_values = []; $params = []; $types = "";

        foreach ($allowed_fields as $field => $type) {
            if (array_key_exists($field, $_POST)) {
                $value = trim($_POST[$field]);

                // Handle empty values
                if (($field === 'start' || $field === 'end') && $value === '') {
                    $value = null;
                } elseif ($value === '') {
                    $value = null;
                }

                $insert_fields[] = "`" . $field . "`";
                $insert_values[] = "?";
                $types .= $type;

                if ($value === null) {
                    $params[] = null;
                } else {
                    $params[] = sanitize_input($conn, $value);
                }
            }
        }

        if (empty($insert_fields)) {
            send_json_response(false, [], 'No valid data provided.');
        }

        $sql = "INSERT INTO {$table_name} (" . implode(', ', $insert_fields) . ") VALUES (" . implode(', ', $insert_values) . ")";
        $stmt = mysqli_prepare($conn, $sql);

        if (!$stmt) {
            error_log("{$error_prefix} DB Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB Error preparing insert: ' . mysqli_error($conn));
        }

        if (count($params) !== strlen($types)) {
            error_log("{$error_prefix} Param/Type mismatch: P=" . count($params) . " T=" . strlen($types));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'Internal error during insert preparation.');
        }

        $bind_params_ref = [];
        foreach ($params as $key => $value) {
            $bind_params_ref[$key] = &$params[$key];
        }

        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);

        if (mysqli_stmt_execute($stmt)) {
            $new_id = mysqli_insert_id($conn);
            mysqli_stmt_close($stmt);

            if ($new_id) {
                send_json_response(true, ['id' => $new_id], $success_message);
            } else {
                send_json_response(false, [], 'Failed to get ID of new record.');
            }
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("{$error_prefix} DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error executing insert: ' . $db_error);
        }
        break;

    case 'addLetter':
        $allowed_fields = [ 'locality' => 's', 'barangay' => 's', 'district' => 's', 'location' => 's', 'date' => 's', 'year' => 's', 'type' => 's', 'status' => 's', 'accomplished' => 's', 'remarks' => 's' ];
        if (!isset($_POST['locality']) || trim($_POST['locality']) === '') { send_json_response(false, [], 'Locality is required.'); }
        $insert_fields = []; $insert_values = []; $params = []; $types = "";
        foreach ($allowed_fields as $field => $type) {
            if (array_key_exists($field, $_POST)) {
                $value = trim($_POST[$field]);
                if ($value === '') { $value = null; }
                $insert_fields[] = "`" . $field . "`";
                $insert_values[] = "?";
                $types .= $type;
                if ($value === null) { $params[] = null; }
                else { $params[] = sanitize_input($conn, $value); }
            }
        }
        if (empty($insert_fields)) { send_json_response(false, [], 'No valid data provided.'); }
        $sql = "INSERT INTO locationrequests (" . implode(', ', $insert_fields) . ") VALUES (" . implode(', ', $insert_values) . ")"; $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Add Letter DB Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing insert.'); }
        if (count($params) !== strlen($types)) { error_log("Add Letter Param/Type mismatch: P=" . count($params) . " T=" . strlen($types)); mysqli_stmt_close($stmt); send_json_response(false, [], 'Internal error during insert preparation.'); }
        $bind_params_ref = []; foreach ($params as $key => $value) { $bind_params_ref[$key] = &$params[$key]; }
        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);
        if (mysqli_stmt_execute($stmt)) { $new_id = mysqli_insert_id($conn); mysqli_stmt_close($stmt); if ($new_id) { send_json_response(true, ['id' => $new_id], 'Letter request added successfully.'); } else { send_json_response(false, [], 'Failed to get ID of new letter request.'); } }
        else { $db_error = mysqli_stmt_error($stmt); mysqli_stmt_close($stmt); error_log("Add Letter DB Execute Error: " . $db_error); send_json_response(false, [], 'DB Error executing insert: ' . $db_error); }
        break;
    case 'addLGU':
        $allowed_fields = [
            'province' => 's', 'district' => 's', 'municipality' => 's', 'lgu' => 's', 'class' => 's',
            'systemtype' => 's', 'system' => 's', 'actiontype' => 's',
            'businessyn' => 's', 'businessstatus' => 's',
            'barangayyn' => 's', 'barangaystatus' => 's',
            'buildingyn' => 's', 'buildingstatus' => 's',
            'workingyn' => 's', 'workingstatus' => 's',
            'bfpyn' => 's', 'bplyn' => 's', 'bplstatus' => 's',
            'ecedulayn' => 's', 'ecedulastatus' => 's',
            'elcryn' => 's', 'elcrstatus' => 's',
            'enewsyn' => 's', 'enewsstatus' => 's',
            'remark' => 's'
        ];

        // Validate required fields
        if (!isset($_POST['province']) || trim($_POST['province']) === '') {
            send_json_response(false, [], 'Province is required.');
        }
        if (!isset($_POST['district']) || trim($_POST['district']) === '') {
            send_json_response(false, [], 'District is required.');
        }
        if (!isset($_POST['municipality']) || trim($_POST['municipality']) === '') {
            send_json_response(false, [], 'Municipality is required.');
        }
        if (!isset($_POST['lgu']) || trim($_POST['lgu']) === '') {
            send_json_response(false, [], 'LGU is required.');
        }
        if (!isset($_POST['class']) || trim($_POST['class']) === '') {
            send_json_response(false, [], 'Class is required.');
        }
        if (!isset($_POST['systemtype']) || trim($_POST['systemtype']) === '') {
            send_json_response(false, [], 'System Type is required.');
        }

        $insert_fields = []; $insert_values = []; $params = []; $types = "";
        foreach ($allowed_fields as $field => $type) {
            if (array_key_exists($field, $_POST)) {
                $value = trim($_POST[$field]);
                if ($value === '') { $value = null; }
                $insert_fields[] = "`" . $field . "`";
                $insert_values[] = "?";
                $types .= $type;
                if ($value === null) { $params[] = null; }
                else { $params[] = sanitize_input($conn, $value); }
            }
        }

        if (empty($insert_fields)) { send_json_response(false, [], 'No valid data provided.'); }

        $sql = "INSERT INTO tblbpls (" . implode(', ', $insert_fields) . ") VALUES (" . implode(', ', $insert_values) . ")";
        $stmt = mysqli_prepare($conn, $sql);

        if (!$stmt) {
            error_log("Add LGU DB Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB Error preparing insert: ' . mysqli_error($conn));
        }

        if (count($params) !== strlen($types)) {
            error_log("Add LGU Param/Type mismatch: P=" . count($params) . " T=" . strlen($types));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'Internal error during insert preparation.');
        }

        $bind_params_ref = [];
        foreach ($params as $key => $value) {
            $bind_params_ref[$key] = &$params[$key];
        }

        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);

        if (mysqli_stmt_execute($stmt)) {
            $new_id = mysqli_insert_id($conn);
            mysqli_stmt_close($stmt);
            if ($new_id) {
                send_json_response(true, ['id' => $new_id], 'LGU added successfully.');
            } else {
                send_json_response(false, [], 'Failed to get ID of new LGU.');
            }
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Add LGU DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error executing insert: ' . $db_error);
        }
        break;
    case 'updateParticipant':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing ID.'); }

        // Check if the referrer contains freewifi4all.php
        $is_fw4a_context = false;
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
            $is_fw4a_context = true;
        }

        $params = []; $types = ""; $sql_parts = [];

        if ($is_fw4a_context) {
            // Logic for updating tblfwfa (FW4A LGU data)
            $allowed_fields = [
                'locality' => 's', 'barangay' => 's', 'district' => 's', 'locations' => 's',
                'type' => 's', 'code' => 's', 'strategy' => 's', 'status' => 's',
                'reason' => 's', 'remarks' => 's'
            ];
            $table_name = 'tblfwfa';
            $item_name_singular = 'LGU entry';
            $item_name_plural = 'LGU entries';

            foreach ($allowed_fields as $field => $type) {
                if (array_key_exists($field, $_POST)) {
                    $value = trim($_POST[$field]);
                    if ($value === '') { $value = null; } // Treat empty strings as NULL for FW4A fields
                    $sql_parts[] = "`" . $field . "` = ?";
                    $types .= $type;
                    if ($value === null) { $params[] = null; }
                    else { $params[] = sanitize_input($conn, $value); }
                }
            }
        } else {
            // Default logic for updating tblparticipant
            $allowed_fields = [
                'start' => 's', 'end' => 's', 'activity' => 's', 'indicator' => 's',
                'fullname' => 's', 'sex' => 's', 'contact' => 's', 'email' => 's',
                'mode' => 's', 'agency' => 's', 'sector' => 's', 'project' => 's',
                'person' => 's', 'remarks' => 's'
            ];
            $table_name = 'tblparticipant';
            $item_name_singular = 'Participant';
            $item_name_plural = 'Participants';

            foreach ($allowed_fields as $field => $type) {
                if (array_key_exists($field, $_POST)) {
                    $value = trim($_POST[$field]);
                    if (($field === 'start' || $field === 'end') && $value === '') { $value = null; } // Dates can be null
                    elseif ($value === '') { $value = null; } // Other strings can be null
                    $sql_parts[] = "`" . $field . "` = ?";
                    $types .= $type;
                    if ($value === null) { $params[] = null; }
                    else { $params[] = sanitize_input($conn, $value); }
                }
            }
        }

        if (empty($sql_parts)) { send_json_response(false, [], 'No valid data provided for update.'); }

        $sql = "UPDATE " . $table_name . " SET " . implode(', ', $sql_parts) . " WHERE id = ?";
        $types .= 'i';
        $params[] = $id;
        $stmt = mysqli_prepare($conn, $sql);

        if (!$stmt) {
            error_log("Update {$item_name_singular} DB Prepare Error: " . mysqli_error($conn) . " SQL: " . $sql);
            send_json_response(false, [], 'DB Error preparing update for ' . strtolower($item_name_singular) . '.');
        }

        if (count($params) !== strlen($types)) {
            error_log("Update {$item_name_singular} Param/Type mismatch: P=" . count($params) . " T=" . strlen($types));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'Internal error during ' . strtolower($item_name_singular) . ' update preparation.');
        }

        $bind_params_ref = []; foreach ($params as $key => $value) { $bind_params_ref[$key] = &$params[$key]; }
        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);

        if (mysqli_stmt_execute($stmt)) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);
            if ($affected_rows > 0) {
                send_json_response(true, [], ucfirst($item_name_singular) . ' updated successfully.');
            } else {
                $check_stmt = mysqli_prepare($conn, "SELECT id FROM " . $table_name . " WHERE id = ?");
                mysqli_stmt_bind_param($check_stmt, "i", $id);
                mysqli_stmt_execute($check_stmt);
                mysqli_stmt_store_result($check_stmt);
                $exists = mysqli_stmt_num_rows($check_stmt) > 0;
                mysqli_stmt_close($check_stmt);
                if ($exists) {
                    send_json_response(true, [], 'No changes were made.');
                } else {
                    send_json_response(false, [], ucfirst($item_name_singular) . ' not found for update.');
                }
            }
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Update {$item_name_singular} DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error executing update for ' . strtolower($item_name_singular) . '.');
        }
        break;
    case 'updateLetter':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing Letter ID.'); }
        $allowed_fields = [ 'locality' => 's', 'barangay' => 's', 'district' => 's', 'location' => 's', 'date' => 's', 'year' => 's', 'type' => 's', 'status' => 's', 'accomplished' => 's', 'remarks' => 's' ];
        $params = []; $types = ""; $sql_parts = [];
        foreach ($allowed_fields as $field => $type) {
            if (array_key_exists($field, $_POST)) {
                $value = trim($_POST[$field]);
                if ($value === '') { $value = null; }
                $sql_parts[] = "`" . $field . "` = ?";
                $types .= $type;
                if ($value === null) { $params[] = null; }
                else { $params[] = sanitize_input($conn, $value); }
            }
        }
        if (empty($sql_parts)) { send_json_response(false, [], 'No valid data provided for update.'); }
        $sql = "UPDATE locationrequests SET " . implode(', ', $sql_parts) . " WHERE id = ?"; $types .= 'i'; $params[] = $id; $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Update Letter DB Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing update.'); }
        if (count($params) !== strlen($types)) { error_log("Update Letter Param/Type mismatch: P=" . count($params) . " T=" . strlen($types)); mysqli_stmt_close($stmt); send_json_response(false, [], 'Internal error during update preparation.'); }
        $bind_params_ref = []; foreach ($params as $key => $value) { $bind_params_ref[$key] = &$params[$key]; }
        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);
        if (mysqli_stmt_execute($stmt)) { $affected_rows = mysqli_stmt_affected_rows($stmt); mysqli_stmt_close($stmt);
            if ($affected_rows > 0) { send_json_response(true, [], 'Letter request updated successfully.'); }
            else { $check_stmt = mysqli_prepare($conn, "SELECT id FROM locationrequests WHERE id = ?"); mysqli_stmt_bind_param($check_stmt, "i", $id); mysqli_stmt_execute($check_stmt); mysqli_stmt_store_result($check_stmt); $exists = mysqli_stmt_num_rows($check_stmt) > 0; mysqli_stmt_close($check_stmt); if ($exists) { send_json_response(true, [], 'No changes were made.'); } else { send_json_response(false, [], 'Letter request not found for update.'); } }
        } else { $db_error = mysqli_stmt_error($stmt); mysqli_stmt_close($stmt); error_log("Update Letter DB Execute Error: " . $db_error); send_json_response(false, [], 'DB Error executing update.'); }
        break;
    case 'updateLGU':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid or missing LGU ID.'); }

        $allowed_fields = [
            'province' => 's', 'district' => 's', 'municipality' => 's', 'lgu' => 's', 'class' => 's',
            'systemtype' => 's', 'actiontype' => 's',
            'businessyn' => 's', 'businessstatus' => 's',
            'barangayyn' => 's', 'barangaystatus' => 's',
            'buildingyn' => 's', 'buildingstatus' => 's',
            'workingyn' => 's', 'workingstatus' => 's',
            'bfpyn' => 's', 'bplyn' => 's', 'bplstatus' => 's',
            'ecedulayn' => 's', 'ecedulastatus' => 's',
            'elcryn' => 's', 'elcrstatus' => 's',
            'enewsyn' => 's', 'enewsstatus' => 's',
            'remark' => 's'
        ];

        $params = []; $types = ""; $sql_parts = [];

        foreach ($allowed_fields as $field => $type) {
            if (array_key_exists($field, $_POST)) {
                $value = trim($_POST[$field]);
                if ($value === '') { $value = null; }
                $sql_parts[] = "`" . $field . "` = ?";
                $types .= $type;
                if ($value === null) { $params[] = null; }
                else { $params[] = sanitize_input($conn, $value); }
            }
        }

        if (empty($sql_parts)) { send_json_response(false, [], 'No valid data provided for update.'); }

        $sql = "UPDATE tblbpls SET " . implode(', ', $sql_parts) . " WHERE id = ?";
        $types .= 'i';
        $params[] = $id;
        $stmt = mysqli_prepare($conn, $sql);

        if (!$stmt) {
            error_log("Update LGU DB Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB Error preparing update: ' . mysqli_error($conn));
        }

        if (count($params) !== strlen($types)) {
            error_log("Update LGU Param/Type mismatch: P=" . count($params) . " T=" . strlen($types));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'Internal error during update preparation.');
        }

        $bind_params_ref = [];
        foreach ($params as $key => $value) {
            $bind_params_ref[$key] = &$params[$key];
        }

        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);

        if (mysqli_stmt_execute($stmt)) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);

            if ($affected_rows > 0) {
                send_json_response(true, [], 'LGU updated successfully.');
            } else {
                $check_stmt = mysqli_prepare($conn, "SELECT id FROM tblbpls WHERE id = ?");
                mysqli_stmt_bind_param($check_stmt, "i", $id);
                mysqli_stmt_execute($check_stmt);
                mysqli_stmt_store_result($check_stmt);
                $exists = mysqli_stmt_num_rows($check_stmt) > 0;
                mysqli_stmt_close($check_stmt);

                if ($exists) {
                    send_json_response(true, [], 'No changes were made.');
                } else {
                    send_json_response(false, [], 'LGU not found for update.');
                }
            }
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Update LGU DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error executing update: ' . $db_error);
        }
        break;
    case 'deleteParticipants':
        $ids = $_POST['ids'] ?? [];
        if (empty($ids) || !is_array($ids)) { send_json_response(false, [], 'No IDs provided.'); }
        $sanitized_ids = []; foreach ($ids as $id) { if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) $sanitized_ids[] = $validated_id; }
        if (empty($sanitized_ids)) { send_json_response(false, [], 'Invalid IDs provided.'); }

        $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
        $types = str_repeat('i', count($sanitized_ids));

        // Check if the referrer contains freewifi4all.php
        $is_fw4a_context = false;
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
            $is_fw4a_context = true;
        }

        $files_table_name = $is_fw4a_context ? 'tblfw4a_files' : 'tblparticipant_files';
        $main_table_name = $is_fw4a_context ? 'tblfwfa' : 'tblparticipant';
        $id_column_in_files_table = $is_fw4a_context ? 'activity_id' : 'participant_id'; // In tblfw4a_files, it's activity_id linking to tblfwfa.id
        $item_name_singular = $is_fw4a_context ? 'LGU entry' : 'Participant';
        $item_name_plural = $is_fw4a_context ? 'LGU entries' : 'Participants';
        $log_prefix = $is_fw4a_context ? 'Delete FW4A Entries' : 'Delete Participants';

        // --- File Deletion ---
        $sql_get_files = "SELECT file_id, filepath FROM {$files_table_name} WHERE {$id_column_in_files_table} IN ($placeholders)";
        $stmt_get_files = mysqli_prepare($conn, $sql_get_files);

        if ($stmt_get_files) {
            $bind_params_files_ref = []; foreach ($sanitized_ids as $key => $value) { $bind_params_files_ref[$key] = &$sanitized_ids[$key]; }
            mysqli_stmt_bind_param($stmt_get_files, $types, ...$bind_params_files_ref);
            mysqli_stmt_execute($stmt_get_files);
            $result_files = mysqli_stmt_get_result($stmt_get_files);
            $files_to_delete = mysqli_fetch_all($result_files, MYSQLI_ASSOC);
            mysqli_stmt_close($stmt_get_files);

            $file_ids_to_delete_db = [];
            foreach ($files_to_delete as $file) {
                 $real_filepath = realpath($file['filepath'] ?? '');
                if ($file && !empty($file['filepath']) && $real_filepath && file_exists($real_filepath)) {
                    if (!@unlink($real_filepath)) { error_log("{$log_prefix}: Failed file unlink: " . $real_filepath); }
                    else { $file_ids_to_delete_db[] = $file['file_id']; }
                } elseif ($file && !empty($file['file_id'])) {
                    // If file path is missing or file doesn't exist on server, still attempt to remove DB record.
                    $file_ids_to_delete_db[] = $file['file_id'];
                }
            }

            if (!empty($file_ids_to_delete_db)) {
                 $unique_file_ids = array_unique($file_ids_to_delete_db);
                $file_placeholders = implode(',', array_fill(0, count($unique_file_ids), '?'));
                $file_types_for_delete = str_repeat('i', count($unique_file_ids));
                $sql_del_files = "DELETE FROM {$files_table_name} WHERE file_id IN ($file_placeholders)";
                $stmt_del_files = mysqli_prepare($conn, $sql_del_files);
                if ($stmt_del_files) {
                    // Rebuild bind params for unique file IDs
                    $bind_params_del_files_ref = [];
                    $temp_unique_ids = array_values($unique_file_ids); // Ensure numeric keys for spread operator
                    foreach ($temp_unique_ids as $key => $value) { $bind_params_del_files_ref[$key] = &$temp_unique_ids[$key]; }

                    mysqli_stmt_bind_param($stmt_del_files, $file_types_for_delete, ...$bind_params_del_files_ref);
                    if (!mysqli_stmt_execute($stmt_del_files)) { error_log("{$log_prefix}: Failed DB file delete: " . mysqli_stmt_error($stmt_del_files)); }
                    mysqli_stmt_close($stmt_del_files);
                } else { error_log("{$log_prefix}: Failed prepare DB file delete: " . mysqli_error($conn)); }
            }
        } else { error_log("{$log_prefix}: Failed prepare fetch files: " . mysqli_error($conn)); }

        // --- Delete Main Records (Participants or FW4A entries) ---
        $sql_main_del = "DELETE FROM {$main_table_name} WHERE id IN ($placeholders)";
        $stmt_main_del = mysqli_prepare($conn, $sql_main_del);

        if (!$stmt_main_del) {
            error_log("{$log_prefix}: Failed prepare statement for main records: " . mysqli_error($conn));
            send_json_response(false, [], "DB Error preparing {$item_name_singular} deletion.");
        }

        $bind_params_main_ref = []; foreach ($sanitized_ids as $key => $value) { $bind_params_main_ref[$key] = &$sanitized_ids[$key]; }
        mysqli_stmt_bind_param($stmt_main_del, $types, ...$bind_params_main_ref);

        if (mysqli_stmt_execute($stmt_main_del)) {
            $deleted_count = mysqli_stmt_affected_rows($stmt_main_del);
            mysqli_stmt_close($stmt_main_del);
            send_json_response(true, [], "$deleted_count {$item_name_plural} deleted successfully.");
        } else {
            $db_error = mysqli_stmt_error($stmt_main_del);
            mysqli_stmt_close($stmt_main_del);
            error_log("{$log_prefix}: DB Execute Error for main records: " . $db_error);
            send_json_response(false, [], "DB Error deleting {$item_name_plural}.");
        }
        break;
    case 'deleteLetters':
        $ids = $_POST['ids'] ?? [];
        if (empty($ids) || !is_array($ids)) { send_json_response(false, [], 'No Letter IDs provided.'); }
        $sanitized_ids = []; foreach ($ids as $id) { if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) $sanitized_ids[] = $validated_id; }
        if (empty($sanitized_ids)) { send_json_response(false, [], 'Invalid Letter IDs provided.'); }
        $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?')); $types = str_repeat('i', count($sanitized_ids));

        // --- Delete the Letters ---
        $sql_del_letters = "DELETE FROM locationrequests WHERE id IN ($placeholders)";
        $stmt_del_letters = mysqli_prepare($conn, $sql_del_letters);
        if (!$stmt_del_letters) { error_log("Delete Letters: DB Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing delete.'); }
        $bind_params_letters_ref = []; foreach ($sanitized_ids as $key => $value) { $bind_params_letters_ref[$key] = &$sanitized_ids[$key]; }
        mysqli_stmt_bind_param($stmt_del_letters, $types, ...$bind_params_letters_ref);
        if (mysqli_stmt_execute($stmt_del_letters)) {
            $deleted_count = mysqli_stmt_affected_rows($stmt_del_letters);
            mysqli_stmt_close($stmt_del_letters);
            send_json_response(true, [], "$deleted_count Letter(s) deleted successfully.");
        }
        else {
            $db_error = mysqli_stmt_error($stmt_del_letters);
            mysqli_stmt_close($stmt_del_letters);
            error_log("Delete Letters: DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error deleting Letters.');
        }
        break;
    case 'deleteLGUs':
        $ids = $_POST['ids'] ?? [];
        if (empty($ids) || !is_array($ids)) { send_json_response(false, [], 'No LGU IDs provided.'); }
        $sanitized_ids = []; foreach ($ids as $id) { if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) $sanitized_ids[] = $validated_id; }
        if (empty($sanitized_ids)) { send_json_response(false, [], 'Invalid LGU IDs provided.'); }
        $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?')); $types = str_repeat('i', count($sanitized_ids));

        // --- LGU File Deletion ---
        // Check if the tblbpls_files table exists
        $table_check_sql = "SHOW TABLES LIKE 'tblbpls_files'";
        $table_check_result = mysqli_query($conn, $table_check_sql);

        if ($table_check_result && mysqli_num_rows($table_check_result) > 0) {
            // Table exists, proceed with file deletion
            $sql_get_lgu_files = "SELECT file_id, filepath FROM tblbpls_files WHERE lgu_id IN ($placeholders)";
            $stmt_get_lgu_files = mysqli_prepare($conn, $sql_get_lgu_files);

            if ($stmt_get_lgu_files) {
                $bind_params_lgu_files_ref = [];
                foreach ($sanitized_ids as $key => $value) {
                    $bind_params_lgu_files_ref[$key] = &$sanitized_ids[$key];
                }

                mysqli_stmt_bind_param($stmt_get_lgu_files, $types, ...$bind_params_lgu_files_ref);
                mysqli_stmt_execute($stmt_get_lgu_files);
                $result_lgu_files = mysqli_stmt_get_result($stmt_get_lgu_files);
                $lgu_files_to_delete = mysqli_fetch_all($result_lgu_files, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt_get_lgu_files);

                $lgu_file_ids_to_delete_db = [];
                foreach ($lgu_files_to_delete as $file) {
                    $real_filepath = realpath($file['filepath'] ?? '');
                    if ($file && !empty($file['filepath']) && $real_filepath && file_exists($real_filepath)) {
                        if (!@unlink($real_filepath)) {
                            error_log("Delete LGUs: Failed LGU file unlink: " . $real_filepath);
                        } else {
                            $lgu_file_ids_to_delete_db[] = $file['file_id'];
                        }
                    } elseif ($file && !empty($file['file_id'])) {
                        $lgu_file_ids_to_delete_db[] = $file['file_id'];
                    }
                }

                if (!empty($lgu_file_ids_to_delete_db)) {
                    $unique_lgu_file_ids = array_unique($lgu_file_ids_to_delete_db);
                    $lgu_file_placeholders = implode(',', array_fill(0, count($unique_lgu_file_ids), '?'));
                    $lgu_file_types = str_repeat('i', count($unique_lgu_file_ids));
                    $sql_del_lgu_files = "DELETE FROM tblbpls_files WHERE file_id IN ($lgu_file_placeholders)";
                    $stmt_del_lgu_files = mysqli_prepare($conn, $sql_del_lgu_files);

                    if ($stmt_del_lgu_files) {
                        $bind_params_del_lgu_files_ref = [];
                        $i=0;
                        foreach($unique_lgu_file_ids as $fid) {
                            $bind_params_del_lgu_files_ref[$i++]=$fid;
                        }

                        mysqli_stmt_bind_param($stmt_del_lgu_files, $lgu_file_types, ...$bind_params_del_lgu_files_ref);
                        if (!mysqli_stmt_execute($stmt_del_lgu_files)) {
                            error_log("Delete LGUs: Failed DB LGU file delete: " . mysqli_stmt_error($stmt_del_lgu_files));
                        }
                        mysqli_stmt_close($stmt_del_lgu_files);
                    } else {
                        error_log("Delete LGUs: Failed prepare DB LGU file delete: " . mysqli_error($conn));
                    }
                }
            } else {
                error_log("Delete LGUs: Failed prepare fetch LGU files: " . mysqli_error($conn));
            }
        }

        // --- Delete LGUs from tblbpls ---
        $sql_lgu = "DELETE FROM tblbpls WHERE id IN ($placeholders)";
        $stmt_lgu = mysqli_prepare($conn, $sql_lgu);

        if (!$stmt_lgu) {
            error_log("Delete LGUs: Failed prepare statement: " . mysqli_error($conn));
            send_json_response(false, [], 'DB Error preparing LGU deletion.');
        }

        $bind_params_ref = [];
        foreach ($sanitized_ids as $key => $value) {
            $bind_params_ref[$key] = &$sanitized_ids[$key];
        }

        mysqli_stmt_bind_param($stmt_lgu, $types, ...$bind_params_ref);

        if (mysqli_stmt_execute($stmt_lgu)) {
            $deleted_count = mysqli_stmt_affected_rows($stmt_lgu);
            mysqli_stmt_close($stmt_lgu);
            send_json_response(true, [], "$deleted_count LGU(s) deleted successfully.");
        } else {
            $db_error = mysqli_stmt_error($stmt_lgu);
            mysqli_stmt_close($stmt_lgu);
            error_log("Delete LGUs: DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error deleting LGUs: ' . $db_error);
        }
        break;
    case 'getFiles':
         $ids = $_POST['ids'] ?? []; if (empty($ids) || !is_array($ids)) { send_json_response(false, [], 'No Activity IDs provided.'); } $sanitized_ids = []; foreach ($ids as $id) { if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) $sanitized_ids[] = $validated_id; } if (empty($sanitized_ids)) { send_json_response(false, [], 'Invalid Activity IDs provided.'); }
         $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?')); $types = str_repeat('i', count($sanitized_ids));
         $sql = "SELECT f.file_id, f.activity_id, f.original_filename, f.stored_filename, f.filepath, f.filesize, f.filetype, f.uploaded_at, a.activity AS parent_item_name FROM tblactivity_files f JOIN tblactivity a ON f.activity_id = a.id WHERE f.activity_id IN ($placeholders) ORDER BY a.activity, f.original_filename";
         $stmt = mysqli_prepare($conn, $sql); if (!$stmt) { error_log("Get Files Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB Error preparing file list.'); }
         $bind_params_ref = []; foreach ($sanitized_ids as $key => $value) { $bind_params_ref[$key] = &$sanitized_ids[$key]; } mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref); mysqli_stmt_execute($stmt); $result = mysqli_stmt_get_result($stmt); $files_raw = mysqli_fetch_all($result, MYSQLI_ASSOC); mysqli_stmt_close($stmt);
         $grouped_files = []; $total_files = 0; foreach ($files_raw as $file) { $group_key = $file['parent_item_name'] ?: 'Unknown Activity'; $file['formatted_filesize'] = formatBytes($file['filesize'] ?? 0); $file['formatted_uploaded_at'] = !empty($file['uploaded_at']) ? date('n/j/Y, g:i:s A', strtotime($file['uploaded_at'])) : 'N/A'; if (!isset($grouped_files[$group_key])) { $grouped_files[$group_key] = []; } $grouped_files[$group_key][] = $file; $total_files++; }
         send_json_response(true, [ 'groupedFiles' => $grouped_files, 'totalFiles' => $total_files, 'parentItemCount' => count($sanitized_ids) ]);
         break;
    case 'getParticipantFiles':
        $ids = $_POST['ids'] ?? [];
        if (empty($ids) || !is_array($ids)) {
            send_json_response(false, [], 'No IDs provided.');
        }
        $sanitized_ids = [];
        foreach ($ids as $id) {
            if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                $sanitized_ids[] = $validated_id;
            }
        }
        if (empty($sanitized_ids)) {
            send_json_response(false, [], 'Invalid IDs provided.');
        }

        $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
        $types = str_repeat('i', count($sanitized_ids));

        // Check if the referrer contains freewifi4all.php
        $is_fw4a_context = false;
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
            $is_fw4a_context = true;
        }

        if ($is_fw4a_context) {
            // FW4A context: query tblfw4a_files and tblfwfa
            // Note: In tblfw4a_files, activity_id links to tblfwfa.id
            $sql = "SELECT f.file_id, f.activity_id AS parent_id, f.original_filename, f.stored_filename, f.filepath, f.filesize, f.filetype, f.uploaded_at, p.locality AS parent_item_name
                    FROM tblfw4a_files f
                    JOIN tblfwfa p ON f.activity_id = p.id
                    WHERE f.activity_id IN ($placeholders)
                    ORDER BY p.locality, f.original_filename";
            $log_prefix = "Get FW4A Files";
            $unknown_parent_name = "Unknown FW4A Entry";
        } else {
            // Default context: query tblparticipant_files and tblparticipant
            $sql = "SELECT f.file_id, f.participant_id AS parent_id, f.original_filename, f.stored_filename, f.filepath, f.filesize, f.filetype, f.uploaded_at, p.fullname AS parent_item_name
                    FROM tblparticipant_files f
                    JOIN tblparticipant p ON f.participant_id = p.id
                    WHERE f.participant_id IN ($placeholders)
                    ORDER BY p.fullname, f.original_filename";
            $log_prefix = "Get Participant Files";
            $unknown_parent_name = "Unknown Participant";
        }

        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("{$log_prefix} Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB Error preparing file list.');
        }

        $bind_params_ref = [];
        foreach ($sanitized_ids as $key => $value) { $bind_params_ref[$key] = &$sanitized_ids[$key]; }
        mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);

        if (!mysqli_stmt_execute($stmt)) {
            error_log("{$log_prefix} Execute Error: " . mysqli_stmt_error($stmt));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'DB Error executing file query.');
        }

        $result = mysqli_stmt_get_result($stmt);
        $files_raw = mysqli_fetch_all($result, MYSQLI_ASSOC);
        mysqli_stmt_close($stmt);

        $grouped_files = [];
        $total_files = 0;
        foreach ($files_raw as $file) {
            $group_key = $file['parent_item_name'] ?: $unknown_parent_name;
            $file['formatted_filesize'] = formatBytes($file['filesize'] ?? 0);
            $file['formatted_uploaded_at'] = !empty($file['uploaded_at']) ? date('n/j/Y, g:i:s A', strtotime($file['uploaded_at'])) : 'N/A';
            if (!isset($grouped_files[$group_key])) { $grouped_files[$group_key] = []; }
            $grouped_files[$group_key][] = $file;
            $total_files++;
        }
        send_json_response(true, [ 'groupedFiles' => $grouped_files, 'totalFiles' => $total_files, 'parentItemCount' => count($sanitized_ids) ]);
        break;
    case 'getFW4AFiles':
         $ids = $_POST['ids'] ?? [];
         if (empty($ids) || !is_array($ids)) {
             send_json_response(false, [], 'No FW4A IDs provided.');
         }
         $sanitized_ids = [];
         foreach ($ids as $id) {
             if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                 $sanitized_ids[] = $validated_id;
             }
         }
         if (empty($sanitized_ids)) {
             send_json_response(false, [], 'Invalid FW4A IDs provided.');
         }
         $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
         $types = str_repeat('i', count($sanitized_ids));
         $sql = "SELECT f.file_id, f.activity_id, f.original_filename, f.stored_filename, f.filepath, f.filesize, f.filetype, f.uploaded_at, p.locality AS parent_item_name FROM tblfw4a_files f JOIN tblfwfa p ON f.activity_id = p.id WHERE f.activity_id IN ($placeholders) ORDER BY p.locality, f.original_filename";
         $stmt = mysqli_prepare($conn, $sql);
         if (!$stmt) {
             error_log("Get FW4A Files Prepare Error: " . mysqli_error($conn));
             send_json_response(false, [], 'DB Error preparing file list.');
         }
         $bind_params_ref = [];
         foreach ($sanitized_ids as $key => $value) {
             $bind_params_ref[$key] = &$sanitized_ids[$key];
         }
         mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);
         if (!mysqli_stmt_execute($stmt)) {
             error_log("Get FW4A Files Execute Error: " . mysqli_stmt_error($stmt));
             mysqli_stmt_close($stmt);
             send_json_response(false, [], 'DB Error executing file query.');
         }
         $result = mysqli_stmt_get_result($stmt);
         $files_raw = mysqli_fetch_all($result, MYSQLI_ASSOC);
         mysqli_stmt_close($stmt);
         $grouped_files = [];
         $total_files = 0;
         foreach ($files_raw as $file) {
             $group_key = $file['parent_item_name'] ?: 'Unknown FW4A';
             $file['formatted_filesize'] = formatBytes($file['filesize'] ?? 0);
             $file['formatted_uploaded_at'] = !empty($file['uploaded_at']) ? date('n/j/Y, g:i:s A', strtotime($file['uploaded_at'])) : 'N/A';
             if (!isset($grouped_files[$group_key])) {
                 $grouped_files[$group_key] = [];
             }
             $grouped_files[$group_key][] = $file;
             $total_files++;
         }
         send_json_response(true, [
             'groupedFiles' => $grouped_files,
             'totalFiles' => $total_files,
             'parentItemCount' => count($sanitized_ids)
         ]);
         break;
    case 'getLetterFiles':
         $ids = $_POST['ids'] ?? [];
         if (empty($ids) || !is_array($ids)) {
             send_json_response(false, [], 'No Letter IDs provided.');
         }
         $sanitized_ids = [];
         foreach ($ids as $id) {
             if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                 $sanitized_ids[] = $validated_id;
             }
         }
         if (empty($sanitized_ids)) {
             send_json_response(false, [], 'Invalid Letter IDs provided.');
         }
         $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
         $types = str_repeat('i', count($sanitized_ids));
         $sql = "SELECT f.file_id, f.activity_id, f.original_filename, f.stored_filename, f.filepath, f.filesize, f.filetype, f.uploaded_at, l.locality AS parent_item_name FROM tblfw4a_files f JOIN locationrequests l ON f.activity_id = l.id WHERE f.activity_id IN ($placeholders) ORDER BY l.locality, f.original_filename";
         $stmt = mysqli_prepare($conn, $sql);
         if (!$stmt) {
             error_log("Get Letter Files Prepare Error: " . mysqli_error($conn));
             send_json_response(false, [], 'DB Error preparing file list.');
         }
         $bind_params_ref = [];
         foreach ($sanitized_ids as $key => $value) {
             $bind_params_ref[$key] = &$sanitized_ids[$key];
         }
         mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);

         if (!mysqli_stmt_execute($stmt)) {
             error_log("Get Letter Files Execute Error: " . mysqli_stmt_error($stmt));
             mysqli_stmt_close($stmt);
             send_json_response(false, [], 'DB Error executing file query.');
         }

         $result = mysqli_stmt_get_result($stmt);
         $files_raw = mysqli_fetch_all($result, MYSQLI_ASSOC);
         mysqli_stmt_close($stmt);

         // Process file paths for letter files
         foreach ($files_raw as &$file) {
             // If the filepath is just a filename, construct the full path
             if (!empty($file['filepath']) && !file_exists($file['filepath'])) {
                 $filename = basename($file['filepath']);
                 $letter_filepath = __DIR__ . '/uploads_letters/' . $filename;
                 if (file_exists($letter_filepath)) {
                     // Update the filepath for display purposes
                     $file['filepath'] = $letter_filepath;
                     error_log("Get Letter Files: Updated filepath to: " . $file['filepath']);
                 }
             }
         }
         unset($file); // Break the reference
         $grouped_files = [];
         $total_files = 0;
         foreach ($files_raw as $file) {
             $group_key = $file['parent_item_name'] ?: 'Unknown Letter';
             $file['formatted_filesize'] = formatBytes($file['filesize'] ?? 0);
             $file['formatted_uploaded_at'] = !empty($file['uploaded_at']) ? date('n/j/Y, g:i:s A', strtotime($file['uploaded_at'])) : 'N/A';
             if (!isset($grouped_files[$group_key])) {
                 $grouped_files[$group_key] = [];
             }
             $grouped_files[$group_key][] = $file;
             $total_files++;
         }
         send_json_response(true, [
             'groupedFiles' => $grouped_files,
             'totalFiles' => $total_files,
             'parentItemCount' => count($sanitized_ids)
         ]);
         break;
    case 'getLGUFiles':
         $ids = $_POST['ids'] ?? [];
         if (empty($ids) || !is_array($ids)) {
             send_json_response(false, [], 'No LGU IDs provided.');
         }

         $sanitized_ids = [];
         foreach ($ids as $id) {
             if ($validated_id = filter_var($id, FILTER_VALIDATE_INT))
                 $sanitized_ids[] = $validated_id;
         }

         if (empty($sanitized_ids)) {
             send_json_response(false, [], 'Invalid LGU IDs provided.');
         }

         // Check if the tblbpls_files table exists
         $table_check_sql = "SHOW TABLES LIKE 'tblbpls_files'";
         $table_check_result = mysqli_query($conn, $table_check_sql);

         if (!$table_check_result || mysqli_num_rows($table_check_result) == 0) {
             // Table doesn't exist, create it
             $create_table_sql = "CREATE TABLE IF NOT EXISTS tblbpls_files (
                 file_id INT(11) NOT NULL AUTO_INCREMENT,
                 lgu_id INT(11) NOT NULL,
                 original_filename VARCHAR(255) NOT NULL,
                 stored_filename VARCHAR(255) NOT NULL,
                 filepath VARCHAR(512) NOT NULL,
                 filesize INT(11) NOT NULL,
                 filetype VARCHAR(100) NOT NULL,
                 uploaded_at DATETIME NOT NULL,
                 PRIMARY KEY (file_id),
                 KEY lgu_id (lgu_id)
             ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

             if (!mysqli_query($conn, $create_table_sql)) {
                 error_log("Get LGU Files: Failed to create tblbpls_files table: " . mysqli_error($conn));
                 send_json_response(false, [], 'Server error: Failed to create file storage table.');
             }

             // Return empty result since the table was just created
             send_json_response(true, [ 'groupedFiles' => [], 'totalFiles' => 0, 'parentItemCount' => count($sanitized_ids) ]);
             break;
         }

         $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
         $types = str_repeat('i', count($sanitized_ids));

         $sql = "SELECT f.file_id, f.lgu_id, f.original_filename, f.stored_filename, f.filepath, f.filesize, f.filetype, f.uploaded_at, b.lgu AS parent_item_name
                FROM tblbpls_files f
                JOIN tblbpls b ON f.lgu_id = b.id
                WHERE f.lgu_id IN ($placeholders)
                ORDER BY b.lgu, f.original_filename";

         $stmt = mysqli_prepare($conn, $sql);

         if (!$stmt) {
             error_log("Get LGU Files Prepare Error: " . mysqli_error($conn));
             send_json_response(false, [], 'DB Error preparing file list: ' . mysqli_error($conn));
         }

         $bind_params_ref = [];
         foreach ($sanitized_ids as $key => $value) {
             $bind_params_ref[$key] = &$sanitized_ids[$key];
         }

         mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);
         mysqli_stmt_execute($stmt);
         $result = mysqli_stmt_get_result($stmt);
         $files_raw = mysqli_fetch_all($result, MYSQLI_ASSOC);
         mysqli_stmt_close($stmt);

         $grouped_files = [];
         $total_files = 0;

         foreach ($files_raw as $file) {
             $group_key = $file['parent_item_name'] ?: 'Unknown LGU';
             $file['formatted_filesize'] = formatBytes($file['filesize'] ?? 0);
             $file['formatted_uploaded_at'] = !empty($file['uploaded_at']) ? date('n/j/Y, g:i:s A', strtotime($file['uploaded_at'])) : 'N/A';

             if (!isset($grouped_files[$group_key])) {
                 $grouped_files[$group_key] = [];
             }

             $grouped_files[$group_key][] = $file;
             $total_files++;
         }

         send_json_response(true, [
             'groupedFiles' => $grouped_files,
             'totalFiles' => $total_files,
             'parentItemCount' => count($sanitized_ids)
         ]);
         break;
    case 'getTech4edFiles':
         $ids = $_POST['ids'] ?? [];
         if (empty($ids) || !is_array($ids)) {
             send_json_response(false, [], 'No Tech4ED DTC IDs provided.');
         }
         $sanitized_ids = [];
         foreach ($ids as $id) {
             if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                 $sanitized_ids[] = $validated_id;
             }
         }
         if (empty($sanitized_ids)) {
             send_json_response(false, [], 'Invalid Tech4ED DTC IDs provided.');
         }

         // Check if the tbltech4ed_files table exists
         $table_check_sql = "SHOW TABLES LIKE 'tbltech4ed_files'";
         $table_check_result = mysqli_query($conn, $table_check_sql);

         if (!$table_check_result || mysqli_num_rows($table_check_result) === 0) {
             // Table doesn't exist, create it
             $create_table_sql = "CREATE TABLE IF NOT EXISTS `tbltech4ed_files` (
                 `file_id` int(11) NOT NULL AUTO_INCREMENT,
                 `tech4ed_id` int(11) NOT NULL,
                 `original_filename` varchar(255) NOT NULL,
                 `stored_filename` varchar(255) NOT NULL,
                 `filepath` varchar(512) NOT NULL,
                 `filesize` int(11) NOT NULL,
                 `filetype` varchar(100) NOT NULL,
                 `uploaded_at` datetime NOT NULL,
                 PRIMARY KEY (`file_id`),
                 KEY `tech4ed_id` (`tech4ed_id`)
             ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

             if (!mysqli_query($conn, $create_table_sql)) {
                 error_log("Get Tech4ED Files: Failed to create tbltech4ed_files table: " . mysqli_error($conn));
                 send_json_response(false, [], 'Database error creating files table.');
             }

             // Return empty result since the table was just created
             send_json_response(true, [ 'groupedFiles' => [], 'totalFiles' => 0, 'parentItemCount' => count($sanitized_ids) ]);
             break;
         }

         $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
         $types = str_repeat('i', count($sanitized_ids));
         $sql = "SELECT f.file_id, f.tech4ed_id, f.original_filename, f.stored_filename, f.filepath, f.filesize, f.filetype, f.uploaded_at, t.cname AS parent_item_name
                FROM tbltech4ed_files f
                JOIN tbltech4ed t ON f.tech4ed_id = t.id
                WHERE f.tech4ed_id IN ($placeholders)
                ORDER BY t.cname, f.original_filename";
         $stmt = mysqli_prepare($conn, $sql);
         if (!$stmt) {
             error_log("Get Tech4ED Files Prepare Error: " . mysqli_error($conn));
             send_json_response(false, [], 'DB Error preparing file list.');
         }
         $bind_params_ref = [];
         foreach ($sanitized_ids as $key => $value) {
             $bind_params_ref[$key] = &$sanitized_ids[$key];
         }
         mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);
         mysqli_stmt_execute($stmt);
         $result = mysqli_stmt_get_result($stmt);
         $files_raw = mysqli_fetch_all($result, MYSQLI_ASSOC);
         mysqli_stmt_close($stmt);
         $grouped_files = [];
         $total_files = 0;
         foreach ($files_raw as $file) {
             $group_key = $file['parent_item_name'] ?: 'Unknown Tech4ED DTC';
             $file['formatted_filesize'] = formatBytes($file['filesize'] ?? 0);
             $file['formatted_uploaded_at'] = !empty($file['uploaded_at']) ? date('n/j/Y, g:i:s A', strtotime($file['uploaded_at'])) : 'N/A';
             if (!isset($grouped_files[$group_key])) {
                 $grouped_files[$group_key] = [];
             }
             $grouped_files[$group_key][] = $file;
             $total_files++;
         }
         send_json_response(true, [
             'groupedFiles' => $grouped_files,
             'totalFiles' => $total_files,
             'parentItemCount' => count($sanitized_ids)
         ]);
         break;
    case 'uploadFiles':
        $result = handle_upload($conn, $_POST['ids'] ?? [], $_FILES['files'] ?? []);

        // Log the file upload
        if ($result['success'] && isset($result['uploaded_count']) && $result['uploaded_count'] > 0) {
            if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                $ids = $_POST['ids'] ?? [];
                $ids_str = is_array($ids) ? implode(', ', $ids) : $ids;
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Uploaded files",
                    "upload",
                    null,
                    "activity_files",
                    "Uploaded {$result['uploaded_count']} files for activities: $ids_str"
                );
            }
        }

        send_json_response($result['success'], $result, $result['message']);
        break;
    case 'uploadParticipantFiles':
        // Check if the referrer contains freewifi4all.php
        $is_fw4a_context = false;
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
            $is_fw4a_context = true;
        }

        if ($is_fw4a_context) {
            // Handle FW4A file uploads (Monitoring Status tab)
            $result = handle_fw4a_upload($conn, $_POST['ids'] ?? [], $_FILES['files'] ?? []);
        } else {
            // Handle regular participant file uploads
            $result = handle_participant_upload($conn, $_POST['ids'] ?? [], $_FILES['files'] ?? []);
        }
        send_json_response($result['success'], $result, $result['message']);
        break;
    case 'uploadLGUFiles':
        $result = handle_lgu_upload($conn, $_POST['ids'] ?? [], $_FILES['files'] ?? []);
        send_json_response($result['success'], $result, $result['message']);
        break;
    case 'uploadFW4AFiles':
        $result = handle_fw4a_upload($conn, $_POST['ids'] ?? [], $_FILES['files'] ?? []);
        send_json_response($result['success'], $result, $result['message']);
        break;
    case 'uploadLetterFiles':
        $result = handle_letter_upload($conn, $_POST['ids'] ?? [], $_FILES['files'] ?? []);
        send_json_response($result['success'], $result, $result['message']);
        break;
    case 'uploadTech4edFiles':
        $result = handle_tech4ed_upload($conn, $_POST['ids'] ?? [], $_FILES['files'] ?? []);

        // Log the file upload
        if ($result['success'] && isset($result['uploaded_count']) && $result['uploaded_count'] > 0) {
            if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                $ids = $_POST['ids'] ?? [];
                $ids_str = is_array($ids) ? implode(', ', $ids) : $ids;
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Uploaded files",
                    "upload",
                    null,
                    "tech4ed_files",
                    "Uploaded {$result['uploaded_count']} files for Tech4ED DTCs: $ids_str"
                );
            }
        }

        send_json_response($result['success'], $result, $result['message']);
        break;
    case 'deleteMultipleFiles':
         $file_ids = $_POST['file_ids'] ?? []; $file_type = $_POST['type'] ?? 'activity';
         if (empty($file_ids) || !is_array($file_ids)) { send_json_response(false, [], 'No File IDs provided.'); } $sanitized_file_ids = []; foreach ($file_ids as $id) { if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) $sanitized_file_ids[] = $validated_id; } if (empty($sanitized_file_ids)) { send_json_response(false, [], 'Invalid File IDs provided.'); }

         // Log the sanitized file IDs for debugging
         error_log("Delete Multiple Files: Attempting to delete file IDs: " . implode(', ', $sanitized_file_ids) . " of type $file_type");

         // Check if the referrer contains freewifi4all.php
         $is_fw4a_context = false;
         if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
             $is_fw4a_context = true;
             error_log("Delete Multiple Files: Detected FW4A context from referrer");

             // Check if we're in the activities tab of freewifi4all.php
             $is_activities_tab = false;
             if (isset($_SERVER['HTTP_REFERER']) && (
                 strpos($_SERVER['HTTP_REFERER'], 'tab=activities') !== false ||
                 strpos($_SERVER['HTTP_REFERER'], 'tab=') === false)) { // Default tab is activities
                 $is_activities_tab = true;
                 error_log("Delete Multiple Files: Detected activities tab in FW4A context");
             }

             // Only override file_type to fw4a if it's not already fw4a or letter
             // and we're not in the activities tab
             if ($file_type !== 'fw4a' && $file_type !== 'letter' && !$is_activities_tab) {
                 error_log("Delete Multiple Files: Overriding file_type from $file_type to fw4a based on referrer");
                 $file_type = 'fw4a';
             }
         }

         if ($file_type === 'participant') {
             $table = 'tblparticipant_files';
             $folder_path = __DIR__ . '/uploads_participants/';
             $file_id_column = 'file_id';
         } elseif ($file_type === 'lgu') {
             $table = 'tblbpls_files';
             $folder_path = __DIR__ . '/uploads_lgu/';
             $file_id_column = 'file_id';
         } elseif ($file_type === 'fw4a') {
             $table = 'tblfw4a_files';
             $folder_path = __DIR__ . '/uploads_fw4a/';
             $file_id_column = 'file_id';
         } elseif ($file_type === 'letter') {
             $table = 'tblfw4a_files';
             $folder_path = __DIR__ . '/uploads_letters/';
             $file_id_column = 'file_id';
         } elseif ($file_type === 'tech4ed') {
             $table = 'tbltech4ed_files';
             $folder_path = __DIR__ . '/uploads_tech4ed/';
             $file_id_column = 'file_id';
         } elseif ($file_type === 'inventory') {
             $table = 'inventory_files';
             $folder_path = __DIR__ . '/uploads_inventory/';
             $file_id_column = 'file_id';
         } else {
             $table = 'tblactivity_files';
             $folder_path = __DIR__ . '/uploads/';
             $file_id_column = 'file_id';
         }

         error_log("Delete Multiple Files: Using table $table and folder $folder_path");

         $placeholders = implode(',', array_fill(0, count($sanitized_file_ids), '?')); $types = str_repeat('i', count($sanitized_file_ids));
         $sql_get = "SELECT $file_id_column, filepath FROM $table WHERE $file_id_column IN ($placeholders)";
         $stmt_get = mysqli_prepare($conn, $sql_get);
         if (!$stmt_get) {
             error_log("Delete Multiple Files: DB error preparing to find files: " . mysqli_error($conn));
             send_json_response(false, [], 'DB error preparing to find files.');
         }

         // Create an array of references for binding
         $bind_params_get_ref = [];
         foreach ($sanitized_file_ids as $key => $value) {
             $bind_params_get_ref[$key] = &$sanitized_file_ids[$key];
         }

         mysqli_stmt_bind_param($stmt_get, $types, ...$bind_params_get_ref);

         if (!mysqli_stmt_execute($stmt_get)) {
             error_log("Delete Multiple Files: DB error executing query: " . mysqli_stmt_error($stmt_get));
             mysqli_stmt_close($stmt_get);
             send_json_response(false, [], 'DB error executing query to find files.');
         }

         $result_get = mysqli_stmt_get_result($stmt_get);
         $files_to_delete = mysqli_fetch_all($result_get, MYSQLI_ASSOC);
         mysqli_stmt_close($stmt_get);

         // Log the number of files found for debugging
         error_log("Delete Multiple Files: Found " . count($files_to_delete) . " files to delete from $table");

         if (empty($files_to_delete)) {
             error_log("Delete Multiple Files: No files found in database for IDs: " . implode(', ', $sanitized_file_ids) . " in table $table");
             send_json_response(false, [], "No files found with the provided IDs in the $table table. They might have been already removed or you may need to refresh the page.");
         }

         $deleted_physically_count = 0; $deletion_errors = []; $successfully_deleted_ids = []; $real_folder_path = realpath($folder_path);
         if (!$real_folder_path) {
             error_log("Delete Multiple Files: Folder path not found: $folder_path");
             $deletion_errors[] = "Server folder not found: " . basename($folder_path);
         }

         foreach ($files_to_delete as $file) {
             if ($file && !empty($file['filepath'])) {
                 $filepath = $file['filepath'];
                 $real_filepath = realpath($filepath);

                 // Special handling for letter files
                 if ($file_type === 'letter') {
                     // For letter files, check if the file exists in the uploads_letters directory
                     $filename = basename($filepath);
                     $letter_filepath = $folder_path . $filename;

                     if (file_exists($letter_filepath) && is_file($letter_filepath)) {
                         // Use the letter_filepath instead of the stored filepath
                         if (@unlink($letter_filepath)) {
                             $deleted_physically_count++;
                             $successfully_deleted_ids[] = $file[$file_id_column];
                             error_log("Delete Multiple Files: Successfully deleted letter file: " . $letter_filepath);
                         } else {
                             $deletion_errors[] = "Failed to delete file: " . basename($filepath);
                             error_log("Delete Multiple Files: Failed to delete letter file: " . $letter_filepath);
                             // Still add file ID for DB deletion even if physical file couldn't be deleted
                             $successfully_deleted_ids[] = $file[$file_id_column];
                         }
                         continue;
                     }
                 }

                 // Standard path checking for other file types
                 if (!$real_folder_path || !$real_filepath || strpos($real_filepath, $real_folder_path) !== 0) {
                     error_log("Delete Multiple Files: Path Mismatch/Security Risk: " . $filepath);
                     $deletion_errors[] = "Access denied for file: " . basename($filepath);
                     // Still add file ID for DB deletion even if physical file can't be accessed
                     $successfully_deleted_ids[] = $file[$file_id_column];
                     continue;
                 }

                 if (file_exists($real_filepath) && is_file($real_filepath)) {
                     if (@unlink($real_filepath)) {
                         $deleted_physically_count++;
                         $successfully_deleted_ids[] = $file[$file_id_column];
                     } else {
                         $deletion_errors[] = "Failed to delete file: " . basename($filepath);
                         error_log("Delete Multiple Files: Failed server delete: " . $real_filepath);
                         // Still add file ID for DB deletion even if physical file couldn't be deleted
                         $successfully_deleted_ids[] = $file[$file_id_column];
                     }
                 } else {
                     error_log("Delete Multiple Files: File not found on disk: " . $real_filepath);
                     $successfully_deleted_ids[] = $file[$file_id_column];
                 }
             } elseif ($file && !empty($file[$file_id_column])) {
                 $successfully_deleted_ids[] = $file[$file_id_column];
             }
         }

         $deleted_db_count = 0;
         if (!empty($successfully_deleted_ids)) {
             $unique_deleted_ids = array_unique($successfully_deleted_ids);
             error_log("Delete Multiple Files: Attempting to delete " . count($unique_deleted_ids) . " records from database");

             $placeholders_del = implode(',', array_fill(0, count($unique_deleted_ids), '?'));
             $types_del = str_repeat('i', count($unique_deleted_ids));
             $sql_del = "DELETE FROM $table WHERE $file_id_column IN ($placeholders_del)";
             $stmt_del = mysqli_prepare($conn, $sql_del);
             if ($stmt_del) {
                 // Create an array of references for binding
                 $bind_params_del_ref = [];
                 foreach($unique_deleted_ids as $key => $value) {
                     $bind_params_del_ref[$key] = &$unique_deleted_ids[$key];
                 }

                 mysqli_stmt_bind_param($stmt_del, $types_del, ...$bind_params_del_ref);

                 if (mysqli_stmt_execute($stmt_del)) {
                     $deleted_db_count = mysqli_stmt_affected_rows($stmt_del);
                     error_log("Delete Multiple Files: Successfully deleted $deleted_db_count records from $table");
                 } else {
                     $deletion_errors[] = "Database error deleting records: " . mysqli_stmt_error($stmt_del);
                     error_log("Delete Multiple Files: DB error deleting records ($table): " . mysqli_stmt_error($stmt_del));
                 }
                 mysqli_stmt_close($stmt_del);
             }
             else { $deletion_errors[] = "Database error preparing delete statement."; error_log("Delete Multiple Files: Failed prepare delete ($table): " . mysqli_error($conn)); }
         } else {
             error_log("Delete Multiple Files: No file IDs collected for database deletion");
         }

         if ($deleted_db_count > 0 && empty($deletion_errors)) {
             send_json_response(true, ['deleted_count' => $deleted_db_count], "$deleted_db_count file record(s) deleted successfully.");
         } elseif ($deleted_db_count > 0) {
             send_json_response(true, ['deleted_count' => $deleted_db_count], "$deleted_db_count file record(s) deleted, with some issues: " . implode('; ', $deletion_errors));
         } elseif (!empty($files_to_delete) && $deleted_db_count == 0) {
             error_log("Delete Multiple Files: Files found but none were deleted from database");
             send_json_response(false, [], 'Files were found but could not be deleted from database. Please try again.');
         } elseif (empty($deletion_errors)) {
             send_json_response(false, [], 'No files were deleted. They might have been already removed.');
         } else {
             send_json_response(false, [], 'File deletion failed: ' . implode('; ', $deletion_errors));
         }
         break;
    case 'getStatDetails':
        // This case remains unchanged from before
        $stat_key = $_POST['stat_key'] ?? '';
        $project = $_POST['project'] ?? 'CYBERSECURITY';

        if (empty($stat_key)) {
            send_json_response(false, [], 'No statistic key provided.');
        }

        $data = [];
        $title = '';
        $chart_data = [];
        $table_data = [];

        // Sanitize inputs
        $project = mysqli_real_escape_string($conn, $project);

        switch ($stat_key) {
            case 'sectors':
                $title = 'Sectors Breakdown';
                $sql = "SELECT sector, COUNT(*) as count FROM tblactivity WHERE project = ? AND sector IS NOT NULL AND sector != '' GROUP BY sector ORDER BY count DESC, sector ASC";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $project);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $table_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt);

                // Prepare chart data
                foreach ($table_data as $row) {
                    $chart_data[] = [
                        'label' => $row['sector'],
                        'value' => (int)$row['count']
                    ];
                }
                break;

            case 'agencies':
                $title = 'Agencies Breakdown';
                $sql = "SELECT agency, COUNT(*) as count FROM tblactivity WHERE project = ? AND agency IS NOT NULL AND agency != '' GROUP BY agency ORDER BY count DESC, agency ASC";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $project);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $table_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt);

                // Prepare chart data
                foreach ($table_data as $row) {
                    $chart_data[] = [
                        'label' => $row['agency'],
                        'value' => (int)$row['count']
                    ];
                }
                break;

            case 'district1':
                $title = 'District 1 Municipalities';
                $sql = "SELECT municipality, COUNT(*) as count FROM tblactivity WHERE project = ? AND district = 'District 1 (Siargao Island)' AND municipality IS NOT NULL AND municipality != '' GROUP BY municipality ORDER BY count DESC, municipality ASC";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $project);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $table_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt);

                // Prepare chart data
                foreach ($table_data as $row) {
                    $chart_data[] = [
                        'label' => $row['municipality'],
                        'value' => (int)$row['count']
                    ];
                }
                break;

            case 'district2':
                $title = 'District 2 Municipalities';
                $sql = "SELECT municipality, COUNT(*) as count FROM tblactivity WHERE project = ? AND district = 'District 2 (Mainland)' AND municipality IS NOT NULL AND municipality != '' GROUP BY municipality ORDER BY count DESC, municipality ASC";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $project);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $table_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt);

                // Prepare chart data
                foreach ($table_data as $row) {
                    $chart_data[] = [
                        'label' => $row['municipality'],
                        'value' => (int)$row['count']
                    ];
                }
                break;

            case 'municipalities':
                $title = 'All Municipalities';
                $sql = "SELECT municipality, COUNT(*) as count FROM tblactivity WHERE project = ? AND municipality IS NOT NULL AND municipality != '' GROUP BY municipality ORDER BY count DESC, municipality ASC";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $project);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $table_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt);

                // Prepare chart data
                foreach ($table_data as $row) {
                    $chart_data[] = [
                        'label' => $row['municipality'],
                        'value' => (int)$row['count']
                    ];
                }
                break;

            case 'barangays':
                $title = 'Barangays';
                $sql = "SELECT barangay, municipality, COUNT(*) as count FROM tblactivity WHERE project = ? AND barangay IS NOT NULL AND barangay != '' GROUP BY barangay, municipality ORDER BY count DESC, municipality ASC, barangay ASC";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $project);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $table_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt);

                // Prepare chart data (limited to top 15 for chart readability)
                $top_barangays = array_slice($table_data, 0, 15);
                foreach ($top_barangays as $row) {
                    $chart_data[] = [
                        'label' => $row['barangay'] . ' (' . $row['municipality'] . ')',
                        'value' => (int)$row['count']
                    ];
                }
                break;

            case 'activities':
                $title = 'Activities by Indicator';
                $sql = "SELECT indicator, COUNT(*) as count FROM tblactivity WHERE project = ? AND indicator IS NOT NULL AND indicator != '' GROUP BY indicator ORDER BY count DESC, indicator ASC";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $project);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $table_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt);

                // Prepare chart data
                foreach ($table_data as $row) {
                    $chart_data[] = [
                        'label' => $row['indicator'],
                        'value' => (int)$row['count']
                    ];
                }
                break;

            case 'p_total':
            case 'participants':
                $title = 'Participants by Indicator';
                $sql = "SELECT indicator, COUNT(*) as count FROM tblparticipant WHERE project = ? AND indicator IS NOT NULL AND indicator != '' GROUP BY indicator ORDER BY count DESC, indicator ASC";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $project);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $table_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt);

                // Prepare chart data
                foreach ($table_data as $row) {
                    $chart_data[] = [
                        'label' => $row['indicator'],
                        'value' => (int)$row['count']
                    ];
                }
                break;

            case 'p_agencies':
                $title = 'Participant Agencies';
                $sql = "SELECT agency, COUNT(*) as count FROM tblparticipant WHERE project = ? AND agency IS NOT NULL AND agency != '' GROUP BY agency ORDER BY count DESC, agency ASC";
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, "s", $project);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                $table_data = mysqli_fetch_all($result, MYSQLI_ASSOC);
                mysqli_stmt_close($stmt);

                // Prepare chart data (limited to top 15 for chart readability)
                $top_agencies = array_slice($table_data, 0, 15);
                foreach ($top_agencies as $row) {
                    $chart_data[] = [
                        'label' => $row['agency'],
                        'value' => (int)$row['count']
                    ];
                }
                break;

            default:
                send_json_response(false, [], 'Invalid statistic key provided.');
                break;
        }

        $data = [
            'title' => $title,
            'table_data' => $table_data,
            'chart_data' => $chart_data
        ];

        send_json_response(true, $data);
        break;

        case 'downloadFile':
            // Close any existing connection *before* potentially sending non-JSON headers
            global $conn;
            if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
                mysqli_close($conn);
                unset($conn);
            }
            // Clean output buffer *before* sending headers
            if (ob_get_level() > 0) {
                ob_end_clean();
            }

            // Reconnect for this specific action - use direct connection parameters
            $dbHost = 'localhost';
            $dbUser = 'root';
            $dbPass = '';
            $dbName = 'dict_sdn_db';

            $conn = mysqli_connect($dbHost, $dbUser, $dbPass, $dbName);
            if (!$conn) {
                error_log("Download File: DB connection failed: " . mysqli_connect_error());
                http_response_code(500);
                die("Server error: Cannot connect to DB for download.");
            }
            mysqli_set_charset($conn, "utf8mb4");

            $file_id = filter_input(INPUT_GET, 'file_id', FILTER_VALIDATE_INT);
            if (!$file_id) { http_response_code(400); die("Invalid File ID."); }
            // error_log("Download File: Received file_id: " . $file_id); // DEBUG LOG

            $file_type = $_GET['type'] ?? 'activity';

            if ($file_type === 'participant') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblparticipant_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_participants');
            } elseif ($file_type === 'lgu') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblbpls_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_lgu');
            } elseif ($file_type === 'fw4a') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblfw4a_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_fw4a');
            } elseif ($file_type === 'letter') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblfw4a_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_letters');
            } else {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblactivity_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads');
            }

            $stmt_get = mysqli_prepare($conn, $sql_get);
            if (!$stmt_get) { http_response_code(500); die("Server error preparing download query."); }

            mysqli_stmt_bind_param($stmt_get, "i", $file_id);
            mysqli_stmt_execute($stmt_get);
            $result_get = mysqli_stmt_get_result($stmt_get);
            $file_data = mysqli_fetch_assoc($result_get);
            mysqli_stmt_close($stmt_get);
            mysqli_close($conn); // Close DB connection after fetching data

            if (!$file_data || empty($file_data['filepath'])) {
                // error_log("Download File: File record not found for ID: " . $file_id); // DEBUG LOG
                http_response_code(404); die("File record not found.");
            }
            $filepath = $file_data['filepath'];

            // Special handling for letter files
            if ($file_type === 'letter') {
                // For letter files, the filepath might be just the filename
                if (!file_exists($filepath) || !is_file($filepath)) {
                    // Try to construct the full path
                    $letter_filepath = rtrim($base_upload_path, '/') . '/' . basename($filepath);
                    if (file_exists($letter_filepath) && is_file($letter_filepath)) {
                        $real_filepath = $letter_filepath;
                        error_log("Download File: Using constructed letter filepath: " . $real_filepath);
                    } else {
                        error_log("Download File: Letter file not found: " . $letter_filepath);
                        http_response_code(404); die("Letter file not found.");
                    }
                } else {
                    $real_filepath = realpath($filepath);
                }
            } else {
                $real_filepath = realpath($filepath);
            }

            error_log("Download File: DB Path: " . $filepath); // DEBUG LOG
            error_log("Download File: Real Path: " . $real_filepath); // DEBUG LOG
            error_log("Download File: Base Path: " . $base_upload_path); // DEBUG LOG

            // Security Check: Ensure file is within the designated upload directory
            if ($file_type === 'letter') {
                // For letter files, we've already verified the file exists
                if (!$real_filepath || !file_exists($real_filepath)) {
                    error_log("Download File Security Violation or Not Found: ID $file_id, Path: ".$filepath.", Real Path: ".$real_filepath); // DEBUG LOG
                    http_response_code(404); die("File not found or access denied.");
                }
            } else {
                // Standard security check for other file types
                if (!$base_upload_path || !$real_filepath || !file_exists($real_filepath) || strpos($real_filepath, $base_upload_path) !== 0) {
                    error_log("Download File Security Violation or Not Found: ID $file_id, Path: ".$filepath.", Real Path: ".$real_filepath); // DEBUG LOG
                    http_response_code(404); die("File not found or access denied.");
                }
            }

            if (!is_readable($real_filepath)) {
                error_log("Download File Error: File not readable: ".$real_filepath); // DEBUG LOG
                http_response_code(403); die("File not readable.");
            }

            $filename = $file_data['original_filename'] ?: 'downloaded_file';
            $filetype = $file_data['filetype'] ?: 'application/octet-stream';

            // Set headers for download
            header('Content-Description: File Transfer');
            header('Content-Type: ' . $filetype);
            header('Content-Disposition: attachment; filename="' . basename($filename) . '"'); // Use basename for security
            header('Expires: 0');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');
            header('Content-Length: ' . filesize($real_filepath));
            // Disable output buffering completely
            while (ob_get_level() > 0) {
                ob_end_clean();
            }
            flush(); // Flush system output buffer

            // Read the file and send its content to the output buffer
            if (readfile($real_filepath) === false) {
                error_log("Download File Error: readfile() failed for " . $real_filepath); // DEBUG LOG
                // Don't output error message here, headers are already sent
            }
            exit; // Important to stop script execution
            break; // Technically unreachable after exit

       case 'previewFile':
            // Close any existing connection *before* potentially sending non-JSON headers
            global $conn;
            if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
                mysqli_close($conn);
                unset($conn);
            }
            // Clean output buffer *before* sending headers
            if (ob_get_level() > 0) {
                ob_end_clean();
            }

            // Reconnect for this specific action - use direct connection parameters
            $dbHost = 'localhost';
            $dbUser = 'root';
            $dbPass = '';
            $dbName = 'dict_sdn_db';

            $conn = mysqli_connect($dbHost, $dbUser, $dbPass, $dbName);
            if (!$conn) {
                error_log("Preview File: DB connection failed: " . mysqli_connect_error());
                http_response_code(500);
                die("Server error: Cannot connect to DB for file preview.");
            }
            mysqli_set_charset($conn, "utf8mb4");

            $file_id = filter_input(INPUT_GET, 'file_id', FILTER_VALIDATE_INT);
            if (!$file_id) { http_response_code(400); die("Invalid File ID."); }

            $file_type = $_GET['type'] ?? 'activity';

            if ($file_type === 'participant') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblparticipant_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_participants');
            } elseif ($file_type === 'lgu') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblbpls_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_lgu');
            } elseif ($file_type === 'letter') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblfw4a_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_letters');
            } elseif ($file_type === 'fw4a') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblfw4a_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_fw4a');
            } elseif ($file_type === 'tech4ed') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tbltech4ed_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_tech4ed');
            } elseif ($file_type === 'inventory') {
                $sql_get = "SELECT filepath, original_filename, filetype FROM inventory_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads_inventory');
            } else {
                $sql_get = "SELECT filepath, original_filename, filetype FROM tblactivity_files WHERE file_id = ?";
                $base_upload_path = realpath(__DIR__ . '/uploads');
            }

            $stmt_get = mysqli_prepare($conn, $sql_get);
            if (!$stmt_get) { http_response_code(500); die("Server error preparing file query."); }

            mysqli_stmt_bind_param($stmt_get, "i", $file_id);
            mysqli_stmt_execute($stmt_get);
            $result_get = mysqli_stmt_get_result($stmt_get);
            $file_data = mysqli_fetch_assoc($result_get);
            mysqli_stmt_close($stmt_get);
            mysqli_close($conn); // Close DB connection after fetching data

            if (!$file_data || empty($file_data['filepath'])) {
                http_response_code(404); die("File record not found.");
            }

            $filepath = $file_data['filepath'];

            // Special handling for letter files
            if ($file_type === 'letter') {
                // For letter files, the filepath might be just the filename
                if (!file_exists($filepath) || !is_file($filepath)) {
                    // Try to construct the full path
                    $letter_filepath = rtrim($base_upload_path, '/') . '/' . basename($filepath);
                    if (file_exists($letter_filepath) && is_file($letter_filepath)) {
                        $real_filepath = $letter_filepath;
                        error_log("Preview File: Using constructed letter filepath: " . $real_filepath);
                    } else {
                        error_log("Preview File: Letter file not found: " . $letter_filepath);
                        http_response_code(404); die("Letter file not found.");
                    }
                } else {
                    $real_filepath = realpath($filepath);
                }
            } else {
                $real_filepath = realpath($filepath);
            }

            error_log("Preview File: DB Path: " . $filepath);
            error_log("Preview File: Real Path: " . $real_filepath);
            error_log("Preview File: Base Path: " . $base_upload_path);

            // Security Check: Ensure file is within the designated upload directory
            if ($file_type === 'letter') {
                // For letter files, we've already verified the file exists
                if (!$real_filepath || !file_exists($real_filepath)) {
                    error_log("Preview File Security Violation or Not Found: ID $file_id, Path: ".$filepath.", Real Path: ".$real_filepath);
                    http_response_code(404); die("File not found or access denied.");
                }
            } else {
                // Standard security check for other file types
                if (!$base_upload_path || !$real_filepath || !file_exists($real_filepath) || strpos($real_filepath, $base_upload_path) !== 0) {
                    error_log("Preview File Security Violation or Not Found: ID $file_id, Path: ".$filepath.", Real Path: ".$real_filepath);
                    http_response_code(404); die("File not found or access denied.");
                }
            }

            if (!is_readable($real_filepath)) {
                error_log("Preview File Error: File not readable: ".$real_filepath);
                http_response_code(403); die("File not readable.");
            }

            $filename = $file_data['original_filename'] ?: 'file';
            $filetype = $file_data['filetype'] ?: 'application/octet-stream';

            // Set headers for inline display (preview)
            header('Content-Description: File Transfer');
            header('Content-Type: ' . $filetype);
            header('Content-Disposition: inline; filename="' . basename($filename) . '"'); // Use inline for preview
            header('Content-Length: ' . filesize($real_filepath));
            header('Cache-Control: public, max-age=3600'); // Allow caching for 1 hour
            header('Pragma: public');
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');

            // Disable output buffering completely
            while (ob_get_level() > 0) {
                ob_end_clean();
            }
            flush(); // Flush system output buffer

            // Read the file and send its content
            if (readfile($real_filepath) === false) {
                error_log("Preview File Error: readfile() failed for " . $real_filepath);
            }
            exit; // Important
            break; // Unreachable

       case 'downloadFW4AFile':
            // Close any existing connection *before* potentially sending non-JSON headers
            global $conn;
            if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
                mysqli_close($conn);
                unset($conn);
            }
            // Clean output buffer *before* sending headers
            if (ob_get_level() > 0) {
                ob_end_clean();
            }

            // Reconnect for this specific action - use direct connection parameters
            $dbHost = 'localhost';
            $dbUser = 'root';
            $dbPass = '';
            $dbName = 'dict_sdn_db';

            $conn = mysqli_connect($dbHost, $dbUser, $dbPass, $dbName);
            if (!$conn) {
                error_log("Download FW4A File: DB connection failed: " . mysqli_connect_error());
                http_response_code(500);
                die("Server error: Cannot connect to DB for FW4A download.");
            }
            mysqli_set_charset($conn, "utf8mb4");

            $file_id = filter_input(INPUT_GET, 'file_id', FILTER_VALIDATE_INT);
            if (!$file_id) { http_response_code(400); die('Invalid FW4A File ID.'); }

            $sql = "SELECT original_filename, filepath, filesize, filetype FROM tblfw4a_files WHERE file_id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            if (!$stmt) { http_response_code(500); die('DB error preparing FW4A download.'); }

            mysqli_stmt_bind_param($stmt, "i", $file_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $file_data = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);
            mysqli_close($conn); // Close DB connection after fetching data

            if (!$file_data || empty($file_data['filepath'])) {
                http_response_code(404); die("FW4A file record not found.");
            }
            $filepath = $file_data['filepath'];
            $base_upload_path = realpath(__DIR__ . '/uploads_fw4a'); // Check correct folder
            $real_filepath = realpath($filepath);

            // Security Check
            if (!$base_upload_path || !$real_filepath || !file_exists($real_filepath) || strpos($real_filepath, $base_upload_path) !== 0) {
                error_log("Download FW4A File Security Violation or Not Found: ID $file_id, Path: ".$filepath.", Real Path: ".$real_filepath);
                http_response_code(404); die("File not found or access denied.");
            }
            if (!is_readable($real_filepath)) {
                error_log("Download FW4A File Error: File not readable: ".$real_filepath);
                http_response_code(403); die("File not readable.");
            }

            $filename = $file_data['original_filename'] ?: 'downloaded_fw4a_file';
            $filetype = $file_data['filetype'] ?: 'application/octet-stream';

            // Set headers for download
            header('Content-Description: File Transfer');
            header('Content-Type: ' . $filetype);
            header('Content-Disposition: attachment; filename="' . basename($filename) . '"'); // Use basename
            header('Content-Length: ' . filesize($real_filepath));
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');
            header('Expires: 0');
            // Disable output buffering completely
            while (ob_get_level() > 0) {
                ob_end_clean();
            }
            flush(); // Flush system output buffer

            // Read the file and send its content
            if (readfile($real_filepath) === false) {
                error_log("Download FW4A File Error: readfile() failed for " . $real_filepath);
            }
            exit; // Important
            break; // Unreachable

       case 'downloadParticipantFile':
            // Close any existing connection *before* potentially sending non-JSON headers
            global $conn;
            if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
                mysqli_close($conn);
                unset($conn);
            }
            // Clean output buffer *before* sending headers
            if (ob_get_level() > 0) {
                ob_end_clean();
            }

            // Reconnect for this specific action - use direct connection parameters
            $dbHost = 'localhost';
            $dbUser = 'root';
            $dbPass = '';
            $dbName = 'dict_sdn_db';

            $conn = mysqli_connect($dbHost, $dbUser, $dbPass, $dbName);
            if (!$conn) {
                error_log("Download Participant File: DB connection failed: " . mysqli_connect_error());
                http_response_code(500);
                die("Server error: Cannot connect to DB for participant download.");
            }
            mysqli_set_charset($conn, "utf8mb4");

             $file_id = filter_input(INPUT_GET, 'file_id', FILTER_VALIDATE_INT);
             if (!$file_id) { http_response_code(400); die('Invalid Participant File ID.'); }
             // error_log("Download Participant File: Received file_id: " . $file_id); // DEBUG LOG

             $sql = "SELECT original_filename, filepath, filesize, filetype FROM tblparticipant_files WHERE file_id = ?";
             $stmt = mysqli_prepare($conn, $sql);
             if (!$stmt) { http_response_code(500); die('DB error preparing participant download.'); }

             mysqli_stmt_bind_param($stmt, "i", $file_id);
             mysqli_stmt_execute($stmt);
             $result = mysqli_stmt_get_result($stmt);
             $file_data = mysqli_fetch_assoc($result);
             mysqli_stmt_close($stmt);
             mysqli_close($conn); // Close DB connection after fetching data

             if (!$file_data || empty($file_data['filepath'])) {
                 // error_log("Download Participant File: File record not found for ID: " . $file_id); // DEBUG LOG
                 http_response_code(404); die("Participant file record not found.");
             }
             $filepath = $file_data['filepath'];
             $base_upload_path = realpath(__DIR__ . '/uploads_participants'); // Check correct folder
             $real_filepath = realpath($filepath);
             // error_log("Download Participant File: DB Path: " . $filepath); // DEBUG LOG
             // error_log("Download Participant File: Real Path: " . $real_filepath); // DEBUG LOG
             // error_log("Download Participant File: Base Path: " . $base_upload_path); // DEBUG LOG

             // Security Check
             if (!$base_upload_path || !$real_filepath || !file_exists($real_filepath) || strpos($real_filepath, $base_upload_path) !== 0) {
                 error_log("Download Participant File Security Violation or Not Found: ID $file_id, Path: ".$filepath.", Real Path: ".$real_filepath); // DEBUG LOG
                 http_response_code(404); die("File not found or access denied.");
             }
             if (!is_readable($real_filepath)) {
                 error_log("Download Participant File Error: File not readable: ".$real_filepath); // DEBUG LOG
                 http_response_code(403); die("File not readable.");
             }

             $filename = $file_data['original_filename'] ?: 'downloaded_participant_file';
             $filetype = $file_data['filetype'] ?: 'application/octet-stream';

             // Set headers for download
             header('Content-Description: File Transfer');
             header('Content-Type: ' . $filetype);
             header('Content-Disposition: attachment; filename="' . basename($filename) . '"'); // Use basename
             header('Content-Length: ' . filesize($real_filepath));
             header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
             header('Pragma: public');
             header('Expires: 0');
             // Disable output buffering completely
             while (ob_get_level() > 0) {
                 ob_end_clean();
             }
             flush(); // Flush system output buffer

             // Read the file and send its content
             if (readfile($real_filepath) === false) {
                 error_log("Download Participant File Error: readfile() failed for " . $real_filepath); // DEBUG LOG
             }
             exit; // Important
             break; // Unreachable

       case 'downloadTech4edFile':
            // Close any existing connection *before* potentially sending non-JSON headers
            global $conn;
            if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
                mysqli_close($conn);
                unset($conn);
            }
            // Clean output buffer *before* sending headers
            if (ob_get_level() > 0) {
                ob_end_clean();
            }

            // Reconnect for this specific action - use direct connection parameters
            $dbHost = 'localhost';
            $dbUser = 'root';
            $dbPass = '';
            $dbName = 'dict_sdn_db';

            $conn = mysqli_connect($dbHost, $dbUser, $dbPass, $dbName);
            if (!$conn) {
                error_log("Download Tech4ED File: DB connection failed: " . mysqli_connect_error());
                http_response_code(500);
                die("Server error: Cannot connect to DB for Tech4ED download.");
            }
            mysqli_set_charset($conn, "utf8mb4");

             $file_id = filter_input(INPUT_GET, 'file_id', FILTER_VALIDATE_INT);
             if (!$file_id) { http_response_code(400); die('Invalid Tech4ED File ID.'); }

             $sql = "SELECT original_filename, filepath, filesize, filetype FROM tbltech4ed_files WHERE file_id = ?";
             $stmt = mysqli_prepare($conn, $sql);
             if (!$stmt) { http_response_code(500); die('DB error preparing Tech4ED download.'); }

             mysqli_stmt_bind_param($stmt, "i", $file_id);
             mysqli_stmt_execute($stmt);
             $result = mysqli_stmt_get_result($stmt);
             $file_data = mysqli_fetch_assoc($result);
             mysqli_stmt_close($stmt);
             mysqli_close($conn); // Close DB connection after fetching data

             if (!$file_data || empty($file_data['filepath'])) {
                 http_response_code(404); die("Tech4ED file record not found.");
             }
             $filepath = $file_data['filepath'];
             $base_upload_path = realpath(__DIR__ . '/uploads_tech4ed'); // Check correct folder
             $real_filepath = realpath($filepath);

             // Security Check
             if (!$base_upload_path || !$real_filepath || !file_exists($real_filepath) || strpos($real_filepath, $base_upload_path) !== 0) {
                 error_log("Download Tech4ED File Security Violation or Not Found: ID $file_id, Path: ".$filepath.", Real Path: ".$real_filepath);
                 http_response_code(404); die("File not found or access denied.");
             }
             if (!is_readable($real_filepath)) {
                 error_log("Download Tech4ED File Error: File not readable: ".$real_filepath);
                 http_response_code(403); die("File not readable.");
             }

             $filename = $file_data['original_filename'] ?: 'downloaded_tech4ed_file';
             $filetype = $file_data['filetype'] ?: 'application/octet-stream';

             // Set headers for download
             header('Content-Description: File Transfer');
             header('Content-Type: ' . $filetype);
             header('Content-Disposition: attachment; filename="' . basename($filename) . '"'); // Use basename
             header('Content-Length: ' . filesize($real_filepath));
             header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
             header('Pragma: public');
             header('Expires: 0');
             // Disable output buffering completely
             while (ob_get_level() > 0) {
                 ob_end_clean();
             }
             flush(); // Flush system output buffer

             // Read the file and send its content
             if (readfile($real_filepath) === false) {
                 error_log("Download Tech4ED File Error: readfile() failed for " . $real_filepath);
             }
             exit; // Important
             break; // Unreachable

    // --- Import/Export Actions ---
     case 'importActivities':
        $activities_json = $_POST['activities'] ?? null;
        if ($activities_json === null) {
            send_json_response(false, [], 'No data received.');
        }
        $activities_data = json_decode($activities_json, true);
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($activities_data)) {
            error_log("Import Activities JSON decode error: " . json_last_error_msg());
            send_json_response(false, [], 'Invalid data format.');
        }
        $imported_count = 0; $errors = []; $row_number = 1;

        // Check if this is an inventory import (from inventory.php)
        $is_inventory_import = false;
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'inventory.php') !== false) {
            $is_inventory_import = true;
        }
        if ($is_inventory_import) {
            // CSV column map for inventory data
            $csv_column_map = [
                'Project' => 'project',
                'Item' => 'item',
                'Classification' => 'classification',
                'Quantity' => 'quantity',
                'Unit' => 'unit',
                'Description' => 'description',
                'Received' => 'received',
                'Property' => 'property',
                'ICS' => 'ics',
                'Serial' => 'serial',
                'Date' => 'date',
                'Officer' => 'officer',
                'Cost' => 'cost',
                'Life' => 'life',
                'Transferred' => 'transferred',
                'Remarks' => 'remarks'
            ];

            // Database fields for inventory
            $db_fields = [
                'project' => 's',
                'item' => 's',
                'classification' => 's',
                'quantity' => 'i',
                'unit' => 's',
                'description' => 's',
                'received' => 's',
                'property' => 's',
                'ics' => 's',
                'serial' => 's',
                'date' => 's',
                'officer' => 's',
                'cost' => 'd',
                'life' => 's',
                'transferred' => 's',
                'remarks' => 's'
            ];
        } else {
            // Original CSV column map for activities
            $csv_column_map = [ 'Start Date' => 'start', 'End Date' => 'end', 'Bureau' => 'project', 'Project' => 'subproject', 'Activity Name' => 'activity', 'Indicator' => 'indicator', 'Training Venue' => 'training', 'Municipality/City' => 'municipality', 'District' => 'district', 'Barangay' => 'barangay', 'Requesting Agency' => 'agency', 'Mode of Implementation' => 'mode', 'Target Sector' => 'sector', 'Responsible Person' => 'person', 'Resource Person' => 'resource', 'Participants' => 'participants', 'Completers' => 'completers', 'Male' => 'male', 'Female' => 'female', 'Approved Activity Design' => 'approved', 'Link to MOVs' => 'mov', 'Remarks' => 'remarks' ];

            // Database fields for activities
            $db_fields = [ 'start' => 's', 'end' => 's', 'project' => 's', 'subproject' => 's', 'activity' => 's', 'indicator' => 's', 'training' => 's', 'municipality' => 's', 'district' => 's', 'barangay' => 's', 'agency' => 's', 'mode' => 's', 'sector' => 's', 'person' => 's', 'resource' => 's', 'participants' => 'i', 'completers' => 'i', 'male' => 'i', 'female' => 'i', 'approved' => 's', 'mov' => 's', 'remarks' => 's' ];
        }

        // Check if the referrer contains freewifi4all.php to determine if this is a FW4A import
        $is_fw4a_import = false;
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
            $is_fw4a_import = true;
        }

        // Get the current project from the page or use FW4A if from freewifi4all.php
        $current_project = $is_fw4a_import ? 'FW4A' : ($_POST['project'] ?? 'CYBERSECURITY');

        // Skip project validation for inventory imports
        if (!$is_inventory_import) {
            // Validate that all records are for the current project
            $invalid_project_rows = [];
            foreach ($activities_data as $index => $activity) {
                $row_num = $index + 2; // +2 because index is 0-based and we skip header row

                // Find the Bureau column in the data (which maps to project in the database)
                $project_value = null;
                foreach ($activity as $key => $value) {
                    if (strcasecmp(trim($key), 'Bureau') == 0) {
                        $project_value = trim($value);
                        break;
                    }
                }

                // If project value exists and doesn't match current project, add to invalid rows
                if ($project_value && $project_value !== $current_project) {
                    $invalid_project_rows[] = [
                        'row' => $row_num,
                        'project' => $project_value
                    ];
                }
            }

            // If invalid project rows were found, return error
            if (!empty($invalid_project_rows)) {
                $error_msg = "Import failed: Data contains records for projects other than '$current_project'.\n";
                $error_msg .= "Found " . count($invalid_project_rows) . " row(s) with different project values:\n";

                // Show up to 5 examples
                $examples = array_slice($invalid_project_rows, 0, 5);
                foreach ($examples as $row) {
                    $error_msg .= "- Row {$row['row']}: \"{$row['project']}\"\n";
                }

                if (count($invalid_project_rows) > 5) {
                    $error_msg .= "...and " . (count($invalid_project_rows) - 5) . " more.\n";
                }

                $error_msg .= "\nPlease upload a file containing only '$current_project' data.";
                send_json_response(false, ['errors' => [$error_msg]], $error_msg);
                break;
            }
        }

        $insert_field_names = array_keys($db_fields);
        $table_name = $is_inventory_import ? "inventory" : "tblactivity";
        $sql = "INSERT INTO $table_name (`" . implode('`, `', $insert_field_names) . "`) VALUES (" . implode(', ', array_fill(0, count($db_fields), '?')) . ")";
        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            send_json_response(false, [], 'DB error preparing import.');
        }
        mysqli_begin_transaction($conn);
        try {
             foreach ($activities_data as $activity) {
                 $row_number++; $params = []; $types = ""; $row_has_error = false; $db_data = [];
                 foreach ($csv_column_map as $csv_header => $db_column) {
                    // Find the actual key in the activity data array, ignoring case
                    $actual_key = null;
                    foreach (array_keys($activity) as $a_key) {
                        if (strcasecmp(trim($a_key), trim($csv_header)) == 0) {
                            $actual_key = $a_key;
                            break;
                        }
                    }
                    $db_data[$db_column] = ($actual_key !== null && isset($activity[$actual_key])) ? trim($activity[$actual_key]) : null;
                 }
                 if ($is_inventory_import) {
                     // Validate required fields for inventory
                     if (empty($db_data['item'])) {
                         $errors[] = "Row $row_number: Missing 'Item'.";
                         $row_has_error = true;
                     }
                     if (empty($db_data['classification'])) {
                         $errors[] = "Row $row_number: Missing 'Classification'.";
                         $row_has_error = true;
                     }
                     if (empty($db_data['unit'])) {
                         $errors[] = "Row $row_number: Missing 'Unit'.";
                         $row_has_error = true;
                     }
                     if (empty($db_data['description'])) {
                         $errors[] = "Row $row_number: Missing 'Description'.";
                         $row_has_error = true;
                     }

                     // Set default project if not provided
                     if (empty($db_data['project'])) {
                         $db_data['project'] = 'inventory';
                     }
                 } else {
                     // Original validation for activities
                     if (empty($db_data['activity'])) {
                         $errors[] = "Row $row_number: Missing 'Activity Name'.";
                         $row_has_error = true;
                     }
                     $db_data['project'] = $current_project; // Force project to current project
                 }
                 foreach ($db_fields as $field => $type) { $value = $db_data[$field] ?? null;
                     if ($type === 'i') { if ($value === '' || $value === null) { $params[] = 0; } elseif (!is_numeric(str_replace(',', '', $value))) { $errors[] = "Row $row_number: Invalid number '$field' ($value)."; $row_has_error = true; $params[] = 0; } else { $params[] = (int)str_replace(',', '', $value); } }
                     elseif ($type === 's') {
                         if ($field === 'start' || $field === 'end' || ($is_inventory_import && $field === 'date')) {
                             if (empty($value)) {
                                 $params[] = null;
                             } else {
                                 $timestamp = strtotime(str_replace('/', '-', $value));
                                 if ($timestamp === false) {
                                     $iso_timestamp = strtotime($value);
                                 } else {
                                     $iso_timestamp = $timestamp;
                                 }
                                 if ($iso_timestamp === false) {
                                     $errors[] = "Row $row_number: Invalid date '$field' ($value). Use YYYY-MM-DD or MM/DD/YYYY.";
                                     $row_has_error = true;
                                     $params[] = null;
                                 } else {
                                     $params[] = date('Y-m-d', $iso_timestamp);
                                 }
                             }
                         } else {
                             $params[] = ($value === null || $value === '') ? null : sanitize_input($conn, $value);
                         }
                     } // Treat empty strings as NULL
                     elseif ($type === 'd') {
                         if ($value === '' || $value === null) {
                             $params[] = 0.00;
                         } elseif (!is_numeric(str_replace(',', '', $value))) {
                             $errors[] = "Row $row_number: Invalid decimal '$field' ($value).";
                             $row_has_error = true;
                             $params[] = 0.00;
                         } else {
                             $params[] = floatval(str_replace(',', '', $value));
                         }
                     }
                     else {
                         $params[] = ($value === null || $value === '') ? null : sanitize_input($conn, $value);
                     } // Treat empty strings as NULL
                     $types .= $type;
                 }
                 if ($row_has_error) { continue; }
                 if (count($params) !== strlen($types)) { $errors[] = "Row $row_number: Internal error (param/type mismatch)."; continue; }
                 $bind_params_ref = []; foreach ($params as $key => $val) { $bind_params_ref[$key] = &$params[$key]; }
                 mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);
                 if (mysqli_stmt_execute($stmt)) { $imported_count++; } else { $errors[] = "Row $row_number: DB insert error - " . mysqli_stmt_error($stmt); error_log("Import DB Execute Error row $row_number: " . mysqli_stmt_error($stmt)); }
             }
             mysqli_stmt_close($stmt);
             if (empty($errors)) {
                 mysqli_commit($conn);

                 // Log the successful import
                 if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                     $log_type = $is_inventory_import ? "inventory" : "activity";
                     $log_message = $is_inventory_import
                         ? "Imported $imported_count inventory items"
                         : "Imported $imported_count activities";

                     add_log_entry(
                         $_SESSION['user_id'],
                         $_SESSION['username'],
                         $log_message,
                         "import",
                         null,
                         $log_type,
                         $log_message
                     );
                 }

                 $success_message = $is_inventory_import
                     ? "Successfully imported $imported_count inventory items."
                     : "Successfully imported $imported_count activities.";

                 send_json_response(true, ['imported_count' => $imported_count], $success_message);
             }
             else {
                 mysqli_rollback($conn);

                 $error_message = $is_inventory_import
                     ? "Import finished with errors. $imported_count inventory items imported. See errors for details."
                     : "Import finished with errors. $imported_count activities imported. See errors for details.";

                 send_json_response(false, ['imported_count' => $imported_count, 'errors' => $errors], $error_message);
             }
        } catch (Exception $e) { mysqli_rollback($conn); if (isset($stmt) && $stmt && mysqli_stmt_errno($stmt) === 0) mysqli_stmt_close($stmt); error_log("Import Exception: " . $e->getMessage()); send_json_response(false, [], 'Unexpected error during import: ' . $e->getMessage()); }
        break;

    case 'importLetters':
        $letters_json = $_POST['letters'] ?? null;
        if ($letters_json === null) { send_json_response(false, [], 'No letter request data received.'); }
        $letters_data = json_decode($letters_json, true);
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($letters_data)) { error_log("Import Letters JSON decode error: " . json_last_error_msg()); send_json_response(false, [], 'Invalid letter request data format.'); }
        $imported_count = 0; $errors = []; $row_number = 1;

        // CSV column map for Letter Requests data
        $csv_column_map = [
            'ID' => 'id',
            'Locality' => 'locality',
            'Barangay' => 'barangay',
            'District' => 'district',
            'Location' => 'location',
            'Date' => 'date',
            'Year' => 'year',
            'Type' => 'type',
            'Status' => 'status',
            'Accomplished' => 'accomplished',
            'Remarks' => 'remarks'
        ];

        // Database fields for Letter Requests
        $db_fields = [
            'locality' => 's',
            'barangay' => 's',
            'district' => 's',
            'location' => 's',
            'date' => 's',
            'year' => 's',
            'type' => 's',
            'status' => 's',
            'accomplished' => 's',
            'remarks' => 's'
        ];

        // Table and required field
        $table_name = 'locationrequests';
        $required_field = 'locality';
        $required_field_label = 'Locality';
        $success_message = "Successfully imported %d letter requests.";

        $insert_field_names = array_keys($db_fields);
        $sql = "INSERT INTO " . $table_name . " (`" . implode('`, `', $insert_field_names) . "`) VALUES (" . implode(', ', array_fill(0, count($db_fields), '?')) . ")";
        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Import Letter Requests Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB error preparing import.');
        }

        mysqli_begin_transaction($conn);
        try {
            foreach ($letters_data as $data_row) {
                $row_number++;
                $params = [];
                $types = "";
                $row_has_error = false;
                $db_data = [];

                foreach ($csv_column_map as $csv_header => $db_column) {
                    $actual_key = null;
                    foreach (array_keys($data_row) as $p_key) {
                        if (strcasecmp(trim($p_key), trim($csv_header)) == 0) {
                            $actual_key = $p_key;
                            break;
                        }
                    }
                    $db_data[$db_column] = ($actual_key !== null && isset($data_row[$actual_key])) ? trim($data_row[$actual_key]) : null;
                }

                // Check required field
                if (empty($db_data[$required_field])) {
                    $errors[] = "Row $row_number: Missing '$required_field_label'.";
                    $row_has_error = true;
                }

                foreach ($db_fields as $field => $type) {
                    $value = $db_data[$field] ?? null;

                    // Special handling for date field
                    if ($field === 'date' && !empty($value)) {
                        // Try to parse the date
                        $date = date_create_from_format('Y-m-d', $value);
                        if (!$date) {
                            $date = date_create_from_format('m/d/Y', $value);
                        }
                        if (!$date) {
                            $date = date_create_from_format('d/m/Y', $value);
                        }
                        if ($date) {
                            $value = date_format($date, 'Y-m-d');
                        } else {
                            $errors[] = "Row $row_number: Invalid date format for Date: $value";
                            $row_has_error = true;
                        }
                    }

                    $params[] = ($value === null || $value === '') ? null : sanitize_input($conn, $value); // Treat empty strings as NULL
                    $types .= $type;
                }

                if ($row_has_error) {
                    continue;
                }

                if (count($params) !== strlen($types)) {
                    $errors[] = "Row $row_number: Internal error (param/type mismatch).";
                    continue;
                }

                $bind_params_ref = [];
                foreach ($params as $key => $val) {
                    $bind_params_ref[$key] = &$params[$key];
                }

                mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);

                if (mysqli_stmt_execute($stmt)) {
                    $imported_count++;
                } else {
                    $errors[] = "Row $row_number: DB insert error - " . mysqli_stmt_error($stmt);
                    error_log("Import Letter Requests DB Execute Error row $row_number: " . mysqli_stmt_error($stmt));
                }
            }

            mysqli_stmt_close($stmt);

            if (empty($errors)) {
                mysqli_commit($conn);
                send_json_response(true, ['imported_count' => $imported_count], sprintf($success_message, $imported_count));
            } else {
                mysqli_rollback($conn);
                send_json_response(false, ['imported_count' => $imported_count, 'errors' => $errors], "Import finished with errors. " . $imported_count . " rows imported. See errors for details.");
            }
        } catch (Exception $e) {
            mysqli_rollback($conn);
            if (isset($stmt) && $stmt && mysqli_stmt_errno($stmt) === 0) mysqli_stmt_close($stmt);
            error_log("Import Letter Requests Exception: " . $e->getMessage());
            send_json_response(false, [], 'Unexpected error during import: ' . $e->getMessage());
        }
        break;

    case 'importLGUs':
        $lgus_json = $_POST['lgus'] ?? null;
        if ($lgus_json === null) {
            send_json_response(false, [], 'No LGU data received.');
        }
        $lgus_data = json_decode($lgus_json, true);
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($lgus_data)) {
            error_log("Import LGUs JSON decode error: " . json_last_error_msg());
            send_json_response(false, [], 'Invalid LGU data format.');
        }
        $imported_count = 0; $errors = []; $row_number = 1;

        // Prepare the SQL statement for inserting LGU data
        $sql = "INSERT INTO tblbpls (
            province, district, municipality, lgu, class, systemtype, actiontype,
            businessyn, businessstatus, barangayyn, barangaystatus, buildingyn, buildingstatus,
            workingyn, workingstatus, bfpyn, bplyn, bplstatus, ecedulayn, ecedulastatus,
            elcryn, elcrstatus, enewsyn, enewsstatus, remark
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?
        )";

        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Import LGUs Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'Database error preparing import.');
        }

        // Start transaction
        mysqli_autocommit($conn, false);
        $success_message = "Successfully imported %d LGUs.";

        foreach ($lgus_data as $row) {
            $row_number++;

            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }

            // Map CSV headers to database columns
            $province = $row['Province'] ?? '';
            $district = $row['District'] ?? '';
            $municipality = $row['Municipality'] ?? '';
            $lgu = $row['LGU'] ?? '';
            $class = $row['Class'] ?? '';
            $systemtype = $row['System Type'] ?? '';
            $actiontype = $row['Action Type'] ?? '';
            $businessyn = $row['Business YN'] ?? '';
            $businessstatus = $row['Business Status'] ?? '';
            $barangayyn = $row['Barangay YN'] ?? '';
            $barangaystatus = $row['Barangay Status'] ?? '';
            $buildingyn = $row['Building YN'] ?? '';
            $buildingstatus = $row['Building Status'] ?? '';
            $workingyn = $row['Working YN'] ?? '';
            $workingstatus = $row['Working Status'] ?? '';
            $bfpyn = $row['BFP YN'] ?? '';
            $bplyn = $row['BPL YN'] ?? '';
            $bplstatus = $row['BPL Status'] ?? '';
            $ecedulayn = $row['eCedula YN'] ?? '';
            $ecedulastatus = $row['eCedula Status'] ?? '';
            $elcryn = $row['eLCR YN'] ?? '';
            $elcrstatus = $row['eLCR Status'] ?? '';
            $enewsyn = $row['eNews YN'] ?? '';
            $enewsstatus = $row['eNews Status'] ?? '';
            $remark = $row['Remark'] ?? '';

            // Validate required fields
            if (empty($lgu)) {
                $errors[] = "Row $row_number: LGU name is required.";
                continue;
            }

            // Bind parameters
            mysqli_stmt_bind_param($stmt, "sssssssssssssssssssssssss",
                $province, $district, $municipality, $lgu, $class, $systemtype, $actiontype,
                $businessyn, $businessstatus, $barangayyn, $barangaystatus, $buildingyn, $buildingstatus,
                $workingyn, $workingstatus, $bfpyn, $bplyn, $bplstatus, $ecedulayn, $ecedulastatus,
                $elcryn, $elcrstatus, $enewsyn, $enewsstatus, $remark
            );

            // Execute the statement
            if (!mysqli_stmt_execute($stmt)) {
                $db_error = mysqli_stmt_error($stmt);
                $errors[] = "Row $row_number: Database error: $db_error";
                error_log("Import LGUs Execute Error (Row $row_number): $db_error");
                continue;
            }

            $imported_count++;
        }

        mysqli_stmt_close($stmt);

        // Commit or rollback transaction
        if (empty($errors)) {
            mysqli_commit($conn);

            // Log the successful import
            if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Imported LGUs",
                    "import",
                    null,
                    "lgu",
                    "Successfully imported $imported_count LGUs"
                );
            }

            send_json_response(true, ['imported_count' => $imported_count], sprintf($success_message, $imported_count));
        } else {
            mysqli_rollback($conn);
            send_json_response(false, ['imported_count' => $imported_count, 'errors' => $errors], "Import finished with errors. $imported_count rows imported. See errors for details.");
        }
        break;

    case 'importParticipants':
        $participants_json = $_POST['participants'] ?? null;
        if ($participants_json === null) { send_json_response(false, [], 'No participant data received.'); }
        $participants_data = json_decode($participants_json, true);
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($participants_data)) { error_log("Import Participants JSON decode error: " . json_last_error_msg()); send_json_response(false, [], 'Invalid participant data format.'); }
        $imported_count = 0; $errors = []; $row_number = 1;

        // Check if the referrer contains freewifi4all.php to determine if this is a FW4A import
        $is_fw4a_import = false;
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
            $is_fw4a_import = true;
        }

        // Get the current project from the page or use FW4A if from freewifi4all.php
        $project = $is_fw4a_import ? 'FW4A' : ($_POST['project'] ?? 'CYBERSECURITY');

        if ($is_fw4a_import) {
            // CSV column map for FW4A data
            $csv_column_map = [
                'ID' => 'id',
                'Locality' => 'locality',
                'Barangay' => 'barangay',
                'District' => 'district',
                'Locations' => 'locations',
                'Type' => 'type',
                'Code' => 'code',
                'Strategy' => 'strategy',
                'Status' => 'status',
                'Reason' => 'reason',
                'Remarks' => 'remarks'
            ];
            // Database fields for FW4A
            $db_fields = [
                'locality' => 's',
                'barangay' => 's',
                'district' => 's',
                'locations' => 's',
                'type' => 's',
                'code' => 's',
                'strategy' => 's',
                'status' => 's',
                'reason' => 's',
                'remarks' => 's'
            ];

            // Table and required field
            $table_name = 'tblfwfa';
            $required_field = 'locality';
            $required_field_label = 'Locality';
            $success_message = "Successfully imported %d FW4A entries.";
        } else {
            // Original CSV column map for participants
            $csv_column_map = [
                'Start Date' => 'start',
                'End Date' => 'end',
                'Activity' => 'activity',
                'Indicator' => 'indicator',
                'Full Name' => 'fullname',
                'Sex' => 'sex',
                'Contact' => 'contact',
                'Email' => 'email',
                'Mode' => 'mode',
                'Agency' => 'agency',
                'Sector' => 'sector',
                'Project' => 'project',
                'Person' => 'person',
                'Remarks' => 'remarks'
            ];
            // Original database fields for participants
            $db_fields = [
                'start' => 's',
                'end' => 's',
                'activity' => 's',
                'indicator' => 's',
                'fullname' => 's',
                'sex' => 's',
                'contact' => 's',
                'email' => 's',
                'mode' => 's',
                'agency' => 's',
                'sector' => 's',
                'project' => 's',
                'person' => 's',
                'remarks' => 's'
            ];

            // Table and required field
            $table_name = 'tblparticipant';
            $required_field = 'fullname';
            $required_field_label = 'Full Name';
            $success_message = "Successfully imported %d participants.";
        }

        $insert_field_names = array_keys($db_fields);
        $sql = "INSERT INTO " . $table_name . " (`" . implode('`, `', $insert_field_names) . "`) VALUES (" . implode(', ', array_fill(0, count($db_fields), '?')) . ")";
        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Import " . ($is_fw4a_import ? "FW4A" : "Participants") . " Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB error preparing import.');
        }

        mysqli_begin_transaction($conn);
        try {
            foreach ($participants_data as $data_row) {
                $row_number++;
                $params = [];
                $types = "";
                $row_has_error = false;
                $db_data = [];

                foreach ($csv_column_map as $csv_header => $db_column) {
                    $actual_key = null;
                    foreach (array_keys($data_row) as $p_key) {
                        if (strcasecmp(trim($p_key), trim($csv_header)) == 0) {
                            $actual_key = $p_key;
                            break;
                        }
                    }
                    $db_data[$db_column] = ($actual_key !== null && isset($data_row[$actual_key])) ? trim($data_row[$actual_key]) : null;
                }

                // Check required field
                if (empty($db_data[$required_field])) {
                    $errors[] = "Row $row_number: Missing '$required_field_label'.";
                    $row_has_error = true;
                }

                // Set project for participant table only
                if (!$is_fw4a_import && empty($db_data['project'])) {
                    $db_data['project'] = $project;
                }

                foreach ($db_fields as $field => $type) {
                    $value = $db_data[$field] ?? null;

                    // Special handling for dates in participant table
                    if (!$is_fw4a_import && ($field === 'start' || $field === 'end') && !empty($value)) {
                        // Try to parse the date
                        $date = date_create_from_format('Y-m-d', $value);
                        if (!$date) {
                            $date = date_create_from_format('m/d/Y', $value);
                        }
                        if (!$date) {
                            $date = date_create_from_format('d/m/Y', $value);
                        }
                        if ($date) {
                            $value = date_format($date, 'Y-m-d');
                        } else {
                            $errors[] = "Row $row_number: Invalid date format for " . ($field === 'start' ? 'Start Date' : 'End Date') . ": $value";
                            $row_has_error = true;
                        }
                    }

                    $params[] = ($value === null || $value === '') ? null : sanitize_input($conn, $value); // Treat empty strings as NULL
                    $types .= $type;
                }

                if ($row_has_error) {
                    continue;
                }

                if (count($params) !== strlen($types)) {
                    $errors[] = "Row $row_number: Internal error (param/type mismatch).";
                    continue;
                }

                $bind_params_ref = [];
                foreach ($params as $key => $val) {
                    $bind_params_ref[$key] = &$params[$key];
                }

                mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);

                if (mysqli_stmt_execute($stmt)) {
                    $imported_count++;
                } else {
                    $errors[] = "Row $row_number: DB insert error - " . mysqli_stmt_error($stmt);
                    error_log("Import " . ($is_fw4a_import ? "FW4A" : "Participants") . " DB Execute Error row $row_number: " . mysqli_stmt_error($stmt));
                }
            }

            mysqli_stmt_close($stmt);

            if (empty($errors)) {
                mysqli_commit($conn);

                // Log the successful import
                if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                    add_log_entry(
                        $_SESSION['user_id'],
                        $_SESSION['username'],
                        "Imported " . ($is_fw4a_import ? "FW4A entries" : "participants"),
                        "import",
                        null,
                        $is_fw4a_import ? "fw4a" : "participant",
                        "Successfully imported $imported_count " . ($is_fw4a_import ? "FW4A entries" : "participants")
                    );
                }

                send_json_response(true, ['imported_count' => $imported_count], sprintf($success_message, $imported_count));
            } else {
                mysqli_rollback($conn);
                send_json_response(false, ['imported_count' => $imported_count, 'errors' => $errors], "Import finished with errors. " . $imported_count . " rows imported. See errors for details.");
            }
        } catch (Exception $e) {
            mysqli_rollback($conn);
            if (isset($stmt) && $stmt && mysqli_stmt_errno($stmt) === 0) mysqli_stmt_close($stmt);
            error_log("Import " . ($is_fw4a_import ? "FW4A" : "Participants") . " Exception: " . $e->getMessage());
            send_json_response(false, [], 'Unexpected error during import: ' . $e->getMessage());
        }
        break;

     case 'exportActivities':
         ini_set('memory_limit', '256M'); ini_set('display_errors', 0); error_reporting(0); // Suppress errors for direct output
         if (!isset($conn) || !$conn || !@mysqli_ping($conn)) {
             require_once __DIR__ . '/config/database.php';
             if (!$conn || !@mysqli_ping($conn)) { http_response_code(500); die("DB connection error."); }
             mysqli_set_charset($conn, "utf8mb4");
         }
         if (ob_get_level() > 0) { ob_end_clean(); }

         // Check if this is a special export type
         $is_fw4a_export = false;
         $is_inventory_export = false;

         if (isset($_SERVER['HTTP_REFERER'])) {
             if (strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
                 $is_fw4a_export = true;
             } else if (strpos($_SERVER['HTTP_REFERER'], 'inventory.php') !== false) {
                 $is_inventory_export = true;
             }
         }

         $export_type = $_POST['type'] ?? 'all';
         $export_format = $_POST['format'] ?? 'csv';
         // Use FW4A as project if it's from freewifi4all.php, otherwise use the provided project or default to CYBERSECURITY
         $project = $is_fw4a_export ? 'FW4A' : ($_POST['project'] ?? 'CYBERSECURITY');

         $where_clauses = []; $query_params = []; $query_param_types = '';
         if (!empty($project)) { $where_clauses[] = "project = ?"; $query_params[] = $project; $query_param_types .= 's'; }
         if ($export_type === 'filtered') {
             $search_term = $_POST['search'] ?? '';
             if (!empty($search_term)) {
                 $like_term = '%' . $search_term . '%';
                 $searchable_columns = [ 'subproject', 'activity', 'indicator', 'training', 'municipality', 'district', 'barangay', 'agency', 'mode', 'sector', 'person', 'resource', 'remarks', 'mov' ];
                 $search_conditions = [];
                 foreach ($searchable_columns as $column) { $search_conditions[] = "`" . $column . "` LIKE ?"; $query_params[] = $like_term; $query_param_types .= 's'; }
                 $where_clauses[] = "(" . implode(" OR ", $search_conditions) . ")";
             }
             $filter_columns = ['indicator', 'sector', 'municipality', 'barangay'];
             foreach ($filter_columns as $column) { $filter_value = $_POST[$column] ?? ''; if (!empty($filter_value)) { $where_clauses[] = "`$column` = ?"; $query_params[] = $filter_value; $query_param_types .= 's'; } }
         } elseif ($export_type === 'selected') {
             $ids = $_POST['ids'] ?? []; if (empty($ids) || !is_array($ids)) { http_response_code(400); die("No activities selected."); }
             $sanitized_ids = []; foreach ($ids as $id) { if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) { $sanitized_ids[] = $validated_id; } }
             if (empty($sanitized_ids)) { http_response_code(400); die("Invalid activities selected."); }
             $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
             $where_clauses[] = "id IN ($placeholders)";
             foreach ($sanitized_ids as $id) { $query_params[] = $id; $query_param_types .= 'i'; }
         }
         $sql_where_clause = !empty($where_clauses) ? " WHERE " . implode(" AND ", $where_clauses) : "";

         if ($is_inventory_export) {
             // For inventory export, we need a completely different approach
             // since the inventory table structure is different

             // Reset the where clauses and parameters for inventory
             if ($export_type === 'all') {
                 // For 'all' export type, we don't need any WHERE clauses
                 // Just reset everything to get all inventory items
                 $where_clauses = [];
                 $query_params = [];
                 $query_param_types = '';

                 // Log that we're exporting all inventory items
                 error_log("Export Inventory: Exporting ALL inventory items");
             } else if ($export_type === 'filtered') {
                 $where_clauses = [];
                 $query_params = [];
                 $query_param_types = '';

                 // Add search condition if provided
                 $search_term = $_POST['search'] ?? '';
                 if (!empty($search_term)) {
                     $like_term = '%' . $search_term . '%';
                     $searchable_columns = [
                         'project', 'item', 'classification', 'quantity', 'unit', 'description',
                         'received', 'property', 'ics', 'serial', 'date', 'officer', 'cost', 'life',
                         'transferred', 'remarks'
                     ];
                     $search_conditions = [];
                     foreach ($searchable_columns as $column) {
                         $search_conditions[] = "`" . $column . "` LIKE ?";
                         $query_params[] = $like_term;
                         $query_param_types .= 's';
                     }
                     $where_clauses[] = "(" . implode(" OR ", $search_conditions) . ")";
                 }
             } elseif ($export_type === 'selected') {
                 // Keep the existing selected IDs logic
                 $where_clauses = [];
                 $query_params = [];
                 $query_param_types = '';

                 $ids = $_POST['ids'] ?? [];
                 if (!empty($ids) && is_array($ids)) {
                     $sanitized_ids = [];
                     foreach ($ids as $id) {
                         if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                             $sanitized_ids[] = $validated_id;
                         }
                     }

                     if (!empty($sanitized_ids)) {
                         $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
                         $where_clauses[] = "id IN ($placeholders)";
                         foreach ($sanitized_ids as $id) {
                             $query_params[] = $id;
                             $query_param_types .= 'i';
                         }
                     }
                 }
             }

             // Rebuild the SQL where clause
             $sql_where_clause = !empty($where_clauses) ? " WHERE " . implode(" AND ", $where_clauses) : "";

             $sql = "SELECT `id`, `project`, `item`, `classification`, `quantity`, `unit`, `description`,
                    `received`, `property`, `ics`, `serial`, `date`, `officer`, `cost`, `life`,
                    `transferred`, `remarks` FROM inventory" . $sql_where_clause . " ORDER BY id DESC";

             // Log the SQL query for debugging
             error_log("Export Inventory SQL: " . $sql);
             error_log("Export Inventory Params: " . ($query_param_types ?? 'none') . " - " . json_encode($query_params ?? []));
         } else {
             // SQL for activities
             $sql = "SELECT id, start, end, project AS bureau, subproject AS project, activity, indicator, training,
                    municipality, district, barangay, agency, mode, sector, person, resource, participants,
                    completers, male, female, approved, mov, remarks FROM tblactivity" . $sql_where_clause . "
                    ORDER BY start DESC, id DESC";
         }
         $stmt = mysqli_prepare($conn, $sql);
         if (!$stmt) { error_log("Export Activities Prepare Error: ".mysqli_error($conn)); http_response_code(500); die("Server error preparing query."); }
         if (!empty($query_params)) { try { $bind_params_ref = []; foreach ($query_params as $key => $value) { $bind_params_ref[$key] = &$query_params[$key]; } mysqli_stmt_bind_param($stmt, $query_param_types, ...$bind_params_ref); } catch (Exception $e) { mysqli_stmt_close($stmt); error_log("Export Activities Bind Error: ".$e->getMessage()); http_response_code(500); die("Server error binding parameters."); } }
         if (!mysqli_stmt_execute($stmt)) { $err=mysqli_stmt_error($stmt); mysqli_stmt_close($stmt); error_log("Export Activities Execute Error: ".$err); http_response_code(500); die("Server error executing query."); }
         $result = mysqli_stmt_get_result($stmt);
         if (!$result) { $err=mysqli_stmt_error($stmt); mysqli_stmt_close($stmt); error_log("Export Activities Result Error: ".$err); http_response_code(500); die("Server error retrieving data."); }
         if ($export_format === 'csv') {
             try {
                 // Debug: Log the row count for troubleshooting
                 $row_count = mysqli_num_rows($result);
                 error_log("Export " . ($is_inventory_export ? "Inventory" : "Activities") . ": Found $row_count rows to export");

                 header('Content-Type: text/csv; charset=utf-8');

                 if ($is_inventory_export) {
                     header('Content-Disposition: attachment; filename="inventory_export_' . date('Y-m-d') . '.csv"');
                 } else {
                     header('Content-Disposition: attachment; filename="activities_export_' . date('Y-m-d') . '.csv"');
                 }

                 $output = fopen('php://output', 'w');
                 if (!$output) { mysqli_stmt_close($stmt); http_response_code(500); die("Failed to create export file"); }

                 if ($is_inventory_export) {
                     // Headers for inventory export
                     $headers = [
                         'ID', 'Project', 'Item', 'Classification', 'Quantity', 'Unit', 'Description',
                         'Received', 'Property', 'ICS', 'Serial', 'Date', 'Officer', 'Cost', 'Life',
                         'Transferred', 'Remarks'
                     ];
                 } else {
                     // Headers for activities export
                     $headers = [
                         'ID', 'Start Date', 'End Date', 'Bureau', 'Project', 'Activity Name', 'Indicator',
                         'Training Venue', 'Municipality/City', 'District', 'Barangay', 'Requesting Agency',
                         'Mode of Implementation', 'Target Sector', 'Responsible Person', 'Resource Person',
                         'Participants', 'Completers', 'Male', 'Female', 'Approved Activity Design',
                         'Link to MOVs', 'Remarks'
                     ];
                 }

                 if (fputcsv($output, $headers) === false) {
                     fclose($output);
                     mysqli_stmt_close($stmt);
                     http_response_code(500);
                     die("Failed to write CSV headers");
                 }

                 while ($row_data = mysqli_fetch_assoc($result)) {
                     if ($is_inventory_export) {
                         // Format date for inventory
                         if (!empty($row_data['date'])) {
                             $row_data['date'] = date('Y-m-d', strtotime($row_data['date']));
                         } else {
                             $row_data['date'] = '';
                         }

                         // Row data for inventory
                         $csv_row = [
                             $row_data['id'],
                             $row_data['project'],
                             $row_data['item'],
                             $row_data['classification'],
                             $row_data['quantity'],
                             $row_data['unit'],
                             $row_data['description'],
                             $row_data['received'],
                             $row_data['property'],
                             $row_data['ics'],
                             $row_data['serial'],
                             $row_data['date'],
                             $row_data['officer'],
                             $row_data['cost'],
                             $row_data['life'],
                             $row_data['transferred'],
                             $row_data['remarks']
                         ];
                     } else {
                         // Format dates for activities
                         if (!empty($row_data['start'])) {
                             $row_data['start'] = date('Y-m-d', strtotime($row_data['start']));
                         } else {
                             $row_data['start'] = '';
                         }

                         if (!empty($row_data['end'])) {
                             $row_data['end'] = date('Y-m-d', strtotime($row_data['end']));
                         } else {
                             $row_data['end'] = '';
                         }

                         // Row data for activities
                         $csv_row = [
                             $row_data['id'],
                             $row_data['start'],
                             $row_data['end'],
                             $row_data['bureau'],
                             $row_data['project'],
                             $row_data['activity'],
                             $row_data['indicator'],
                             $row_data['training'],
                             $row_data['municipality'],
                             $row_data['district'],
                             $row_data['barangay'],
                             $row_data['agency'],
                             $row_data['mode'],
                             $row_data['sector'],
                             $row_data['person'],
                             $row_data['resource'],
                             $row_data['participants'],
                             $row_data['completers'],
                             $row_data['male'],
                             $row_data['female'],
                             $row_data['approved'],
                             $row_data['mov'],
                             $row_data['remarks']
                         ];
                     }

                     if (fputcsv($output, $csv_row) === false) {
                         error_log("Export " . ($is_inventory_export ? "Inventory" : "Activities") . ": Failed write row ID " . ($row_data['id'] ?? 'unknown'));
                     }
                 }
                 fclose($output);
                 mysqli_stmt_close($stmt);

                 // Log the export action before exiting
                 if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                     // Create a log entry in a separate connection since we're about to close this one
                     $log_conn = mysqli_connect($dbHost, $dbUser, $dbPass, $dbName);
                     if ($log_conn) {
                         mysqli_set_charset($log_conn, "utf8mb4");

                         // Get count of exported rows
                         $exported_count = mysqli_num_rows($result);
                         $export_type_str = $export_type === 'all' ? 'all' : ($export_type === 'filtered' ? 'filtered' : 'selected');

                         // Create log helper function locally
                         $log_sql = "INSERT INTO tbllogs (user_id, username, action, action_type, item_type, details, ip_address)
                                    VALUES (?, ?, ?, ?, ?, ?, ?)";
                         $log_stmt = mysqli_prepare($log_conn, $log_sql);

                         if ($log_stmt) {
                             $user_id = $_SESSION['user_id'];
                             $username = $_SESSION['username'];

                             if ($is_inventory_export) {
                                 $action = "Exported inventory items";
                                 $item_type = "inventory";
                                 $details = "Exported $exported_count inventory items ($export_type_str)";
                             } else {
                                 $action = "Exported activities";
                                 $item_type = "activity";
                                 $details = "Exported $exported_count activities ($export_type_str)";
                             }

                             $action_type = "export";
                             $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;

                             mysqli_stmt_bind_param($log_stmt, "issssss",
                                 $user_id, $username, $action, $action_type, $item_type, $details, $ip_address);
                             mysqli_stmt_execute($log_stmt);
                             mysqli_stmt_close($log_stmt);
                         }

                         mysqli_close($log_conn);
                     }
                 }

                 if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) { mysqli_close($conn); }
                 exit;
             } catch (Exception $e) {
                 if (isset($stmt) && $stmt) mysqli_stmt_close($stmt); error_log("Export Activities CSV Error: ".$e->getMessage()); http_response_code(500); die("Server error during CSV generation: " . $e->getMessage());
             }
         } else { mysqli_stmt_close($stmt); http_response_code(400); die("Unsupported export format."); }
         break;

     case 'exportLetters':
         ini_set('memory_limit', '256M'); ini_set('display_errors', 0); error_reporting(0);
         if (!isset($conn) || !$conn || !@mysqli_ping($conn)) {
            require_once __DIR__ . '/config/database.php';
             if (!$conn || !@mysqli_ping($conn)) { http_response_code(500); die("DB connection error."); }
             mysqli_set_charset($conn, "utf8mb4");
         }
         if (ob_get_level() > 0) { ob_end_clean(); }

         $export_type = $_POST['type'] ?? 'all';
         $export_format = $_POST['format'] ?? 'csv';

         $where_clauses = []; $query_params = []; $query_param_types = '';

         if ($export_type === 'filtered') {
             $search_term = $_POST['l_search'] ?? '';
             if (!empty($search_term)) {
                 $like_term = '%' . $search_term . '%';
                 // Searchable columns for Letter Requests table
                 $searchable_columns = [ 'locality', 'barangay', 'district', 'location', 'date', 'year', 'type', 'status', 'accomplished', 'remarks' ];
                 $search_conditions = [];
                 foreach ($searchable_columns as $column) {
                     $search_conditions[] = "`" . $column . "` LIKE ?";
                     $query_params[] = $like_term;
                     $query_param_types .= 's';
                 }
                 $where_clauses[] = "(" . implode(" OR ", $search_conditions) . ")";
             }

             // Filter columns for Letter Requests
             $filter_columns = ['locality', 'type', 'status'];
             foreach ($filter_columns as $column) {
                 $filter_value = $_POST['l_'.$column] ?? '';
                 if (!empty($filter_value)) {
                     $where_clauses[] = "`$column` = ?";
                     $query_params[] = $filter_value;
                     $query_param_types .= 's';
                 }
             }
         } elseif ($export_type === 'selected') {
             $ids = $_POST['ids'] ?? [];
             if (empty($ids) || !is_array($ids)) {
                 http_response_code(400);
                 die("No letter requests selected.");
             }

             $sanitized_ids = [];
             foreach ($ids as $id) {
                 if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                     $sanitized_ids[] = $validated_id;
                 }
             }

             if (empty($sanitized_ids)) {
                 http_response_code(400);
                 die("Invalid letter requests selected.");
             }

             $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
             $where_clauses[] = "id IN ($placeholders)";
             foreach ($sanitized_ids as $id) {
                 $query_params[] = $id;
                 $query_param_types .= 'i';
             }
         }

         $sql_where_clause = !empty($where_clauses) ? " WHERE " . implode(" AND ", $where_clauses) : "";

         // SQL for Letter Requests table
         $sql = "SELECT `id`, `locality`, `barangay`, `district`, `location`, `date`, `year`, `type`, `status`, `accomplished`, `remarks` FROM locationrequests" . $sql_where_clause . " ORDER BY id DESC";

         $stmt = mysqli_prepare($conn, $sql);
         if (!$stmt) { error_log("Export Letter Requests Prepare Error: ".mysqli_error($conn)); http_response_code(500); die("Server error preparing query."); }

         if (!empty($query_params)) {
             try {
                 $bind_params_ref = [];
                 foreach ($query_params as $key => $value) {
                     $bind_params_ref[$key] = &$query_params[$key];
                 }
                 mysqli_stmt_bind_param($stmt, $query_param_types, ...$bind_params_ref);
             } catch (Exception $e) {
                 mysqli_stmt_close($stmt);
                 error_log("Export Letter Requests Bind Error: ".$e->getMessage());
                 http_response_code(500);
                 die("Server error binding parameters.");
             }
         }

         if (!mysqli_stmt_execute($stmt)) {
             $err=mysqli_stmt_error($stmt);
             mysqli_stmt_close($stmt);
             error_log("Export Letter Requests Execute Error: ".$err);
             http_response_code(500);
             die("Server error executing query.");
         }

         $result = mysqli_stmt_get_result($stmt);
         if (!$result) {
             $err=mysqli_stmt_error($stmt);
             mysqli_stmt_close($stmt);
             error_log("Export Letter Requests Result Error: ".$err);
             http_response_code(500);
             die("Server error retrieving data.");
         }

         if ($export_format === 'csv') {
             try {
                 header('Content-Type: text/csv; charset=utf-8');
                 header('Content-Disposition: attachment; filename="letter_requests_export_' . date('Y-m-d') . '.csv"');
                 $output = fopen('php://output', 'w');
                 if (!$output) {
                     mysqli_stmt_close($stmt);
                     http_response_code(500);
                     die("Failed to create export file");
                 }

                 // Headers for Letter Requests export
                 $headers = [ 'ID', 'Locality', 'Barangay', 'District', 'Location', 'Date', 'Year', 'Type', 'Status', 'Accomplished', 'Remarks' ];

                 if (fputcsv($output, $headers) === false) {
                     fclose($output);
                     mysqli_stmt_close($stmt);
                     http_response_code(500);
                     die("Failed to write CSV headers");
                 }

                 while ($row = mysqli_fetch_assoc($result)) {
                     // Format date for CSV
                     if (!empty($row['date'])) {
                         $row['date'] = date('Y-m-d', strtotime($row['date']));
                     }

                     // Row data for Letter Requests
                     $csv_row = [
                         $row['id'],
                         $row['locality'],
                         $row['barangay'],
                         $row['district'],
                         $row['location'],
                         $row['date'],
                         $row['year'],
                         $row['type'],
                         $row['status'],
                         $row['accomplished'],
                         $row['remarks']
                     ];

                     if (fputcsv($output, $csv_row) === false) {
                         error_log("Export Letter Requests: Failed write row ID " . ($row['id'] ?? 'unknown'));
                     }
                 }

                 fclose($output);
                 mysqli_stmt_close($stmt);
                 if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
                     mysqli_close($conn);
                 }
                 exit;
             } catch (Exception $e) {
                 if (isset($stmt) && $stmt) mysqli_stmt_close($stmt);
                 error_log("Export Letter Requests CSV Error: ".$e->getMessage());
                 http_response_code(500);
                 die("Server error during CSV generation: " . $e->getMessage());
             }
         } else {
             mysqli_stmt_close($stmt);
             http_response_code(400);
             die("Unsupported export format.");
         }
         break;

     case 'exportLGUs':
         ini_set('memory_limit', '256M'); ini_set('display_errors', 0); error_reporting(0);
         if (!isset($conn) || !$conn || !@mysqli_ping($conn)) {
            require_once __DIR__ . '/config/database.php';
             if (!$conn || !@mysqli_ping($conn)) { http_response_code(500); die("DB connection error."); }
             mysqli_set_charset($conn, "utf8mb4");
         }
         if (ob_get_level() > 0) { ob_end_clean(); }

         $export_type = $_POST['type'] ?? 'all';
         $export_format = $_POST['format'] ?? 'csv';

         $where_clauses = []; $query_params = []; $query_param_types = '';

         if ($export_type === 'filtered') {
             $search_term = $_POST['search'] ?? '';
             if (!empty($search_term)) {
                 $like_term = '%' . $search_term . '%';
                 $searchable_columns = [
                     'province', 'district', 'municipality', 'lgu', 'class', 'systemtype', 'actiontype',
                     'businessstatus', 'barangaystatus', 'buildingstatus', 'workingstatus',
                     'bplstatus', 'ecedulastatus', 'elcrstatus', 'enewsstatus', 'remark'
                 ];
                 $search_conditions = [];
                 foreach ($searchable_columns as $column) {
                     $search_conditions[] = "`$column` LIKE ?";
                     $query_params[] = $like_term;
                     $query_param_types .= 's';
                 }
                 $where_clauses[] = "(" . implode(" OR ", $search_conditions) . ")";
             }

             // Filter columns for LGUs
             $filter_columns = ['systemtype', 'district', 'lgu', 'class'];
             foreach ($filter_columns as $column) {
                 $filter_value = $_POST['p_'.$column] ?? '';
                 if (!empty($filter_value)) {
                     $where_clauses[] = "`$column` = ?";
                     $query_params[] = $filter_value;
                     $query_param_types .= 's';
                 }
             }
         } elseif ($export_type === 'selected') {
             $ids = $_POST['ids'] ?? [];
             if (empty($ids) || !is_array($ids)) {
                 http_response_code(400);
                 die("No LGUs selected.");
             }

             $sanitized_ids = [];
             foreach ($ids as $id) {
                 if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                     $sanitized_ids[] = $validated_id;
                 }
             }

             if (empty($sanitized_ids)) {
                 http_response_code(400);
                 die("Invalid LGUs selected.");
             }

             $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
             $where_clauses[] = "id IN ($placeholders)";
             $query_params = array_merge($query_params, $sanitized_ids);
             $query_param_types .= str_repeat('i', count($sanitized_ids));
         }

         $sql_where_clause = !empty($where_clauses) ? " WHERE " . implode(" AND ", $where_clauses) : "";

         // SQL for LGUs (tblbpls table)
         $sql = "SELECT `id`, `province`, `district`, `municipality`, `lgu`, `class`, `systemtype`, `actiontype`,
                `businessyn`, `businessstatus`, `barangayyn`, `barangaystatus`, `buildingyn`, `buildingstatus`,
                `workingyn`, `workingstatus`, `bfpyn`, `bplyn`, `bplstatus`, `ecedulayn`, `ecedulastatus`,
                `elcryn`, `elcrstatus`, `enewsyn`, `enewsstatus`, `remark`
                FROM `tblbpls`" . $sql_where_clause . " ORDER BY id DESC";

         $stmt = mysqli_prepare($conn, $sql);
         if (!$stmt) { error_log("Export LGUs Prepare Error: " . mysqli_error($conn)); http_response_code(500); die("Server error preparing query."); }

         if (!empty($query_params)) {
             if (strlen($query_param_types) !== count($query_params)) {
                 error_log("Export LGUs Parameter Count Mismatch: Types=" . strlen($query_param_types) . ", Params=" . count($query_params));
                 http_response_code(500);
                 die("Server error with query parameters.");
             }
             mysqli_stmt_bind_param($stmt, $query_param_types, ...$query_params);
         }

         if (!mysqli_stmt_execute($stmt)) { $err=mysqli_stmt_error($stmt); mysqli_stmt_close($stmt); error_log("Export LGUs Execute Error: ".$err); http_response_code(500); die("Server error executing query."); }
         $result = mysqli_stmt_get_result($stmt);
         if (!$result) { $err=mysqli_stmt_error($stmt); mysqli_stmt_close($stmt); error_log("Export LGUs Result Error: ".$err); http_response_code(500); die("Server error retrieving data."); }

         if ($export_format === 'csv') {
             try {
                 header('Content-Type: text/csv; charset=utf-8');
                 header('Content-Disposition: attachment; filename="lgus_export_' . date('Y-m-d') . '.csv"');
                 $output = fopen('php://output', 'w');
                 if (!$output) {
                     mysqli_stmt_close($stmt);
                     http_response_code(500);
                     die("Failed to create export file");
                 }

                 // Headers for LGUs export
                 $headers = [
                     'ID', 'Province', 'District', 'Municipality', 'LGU', 'Class', 'System Type', 'Action Type',
                     'Business YN', 'Business Status', 'Barangay YN', 'Barangay Status', 'Building YN', 'Building Status',
                     'Working YN', 'Working Status', 'BFP YN', 'BPL YN', 'BPL Status', 'eCedula YN', 'eCedula Status',
                     'eLCR YN', 'eLCR Status', 'eNews YN', 'eNews Status', 'Remark'
                 ];

                 if (fputcsv($output, $headers) === false) {
                     fclose($output);
                     mysqli_stmt_close($stmt);
                     http_response_code(500);
                     die("Failed to write CSV headers");
                 }

                 while ($row = mysqli_fetch_assoc($result)) {
                     // Row data for LGUs
                     $csv_row = [
                         $row['id'],
                         $row['province'],
                         $row['district'],
                         $row['municipality'],
                         $row['lgu'],
                         $row['class'],
                         $row['systemtype'],
                         $row['actiontype'],
                         $row['businessyn'],
                         $row['businessstatus'],
                         $row['barangayyn'],
                         $row['barangaystatus'],
                         $row['buildingyn'],
                         $row['buildingstatus'],
                         $row['workingyn'],
                         $row['workingstatus'],
                         $row['bfpyn'],
                         $row['bplyn'],
                         $row['bplstatus'],
                         $row['ecedulayn'],
                         $row['ecedulastatus'],
                         $row['elcryn'],
                         $row['elcrstatus'],
                         $row['enewsyn'],
                         $row['enewsstatus'],
                         $row['remark']
                     ];

                     if (fputcsv($output, $csv_row) === false) {
                         error_log("Export LGUs: Failed write row ID " . ($row['id'] ?? 'unknown'));
                     }
                 }

                 fclose($output);
                 mysqli_stmt_close($stmt);
                 if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
                     mysqli_close($conn);
                 }
                 exit;
             } catch (Exception $e) {
                 if (isset($stmt) && $stmt) mysqli_stmt_close($stmt);
                 error_log("Export LGUs CSV Error: ".$e->getMessage());
                 http_response_code(500);
                 die("Server error during CSV generation: " . $e->getMessage());
             }
         } else {
             mysqli_stmt_close($stmt);
             http_response_code(400);
             die("Unsupported export format.");
         }
         break;

    case 'exportParticipants':
         ini_set('memory_limit', '256M'); ini_set('display_errors', 0); error_reporting(0);
         if (!isset($conn) || !$conn || !@mysqli_ping($conn)) {
            require_once __DIR__ . '/config/database.php';
             if (!$conn || !@mysqli_ping($conn)) { http_response_code(500); die("DB connection error."); }
             mysqli_set_charset($conn, "utf8mb4");
         }
         if (ob_get_level() > 0) { ob_end_clean(); }

         // Check if the referrer contains freewifi4all.php to determine if this is a FW4A export
         $is_fw4a_export = false;
         if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'freewifi4all.php') !== false) {
             $is_fw4a_export = true;
         }

         $export_type = $_POST['type'] ?? 'all';
         $export_format = $_POST['format'] ?? 'csv';
         // Use FW4A as project if it's from freewifi4all.php, otherwise use the provided project or default to CYBERSECURITY
         $project = $is_fw4a_export ? 'FW4A' : ($_POST['project'] ?? 'CYBERSECURITY');

         $where_clauses = []; $query_params = []; $query_param_types = '';

         if (!empty($project) && !$is_fw4a_export) {
             $where_clauses[] = "project = ?";
             $query_params[] = $project;
             $query_param_types .= 's';
         }

         if ($export_type === 'filtered') {
             $search_term = $_POST['p_search'] ?? '';
             if (!empty($search_term)) {
                 $like_term = '%' . $search_term . '%';

                 if ($is_fw4a_export) {
                     // Searchable columns for FW4A table
                     $searchable_columns = [ 'locality', 'barangay', 'district', 'locations', 'type', 'code', 'strategy', 'status', 'reason', 'remarks' ];
                 } else {
                     // Original searchable columns for participants
                 $searchable_columns = [ 'fullname', 'sex', 'contact', 'email', 'mode', 'agency', 'sector', 'activity', 'indicator', 'person', 'remarks' ];
                 }

                 $search_conditions = [];
                 foreach ($searchable_columns as $column) {
                     $search_conditions[] = "`" . $column . "` LIKE ?";
                     $query_params[] = $like_term;
                     $query_param_types .= 's';
                 }
                 $where_clauses[] = "(" . implode(" OR ", $search_conditions) . ")";
             }

             if ($is_fw4a_export) {
                 // Filter columns for FW4A
                 $filter_columns = ['status', 'strategy', 'type'];
             } else {
                 // Original filter columns for participants
             $filter_columns = ['indicator', 'sector', 'agency', 'mode'];
             }

             foreach ($filter_columns as $column) {
                 $filter_value = $_POST['p_'.$column] ?? '';
                 if (!empty($filter_value)) {
                     $where_clauses[] = "`$column` = ?";
                     $query_params[] = $filter_value;
                     $query_param_types .= 's';
                 }
             }
         } elseif ($export_type === 'selected') {
             $ids = $_POST['ids'] ?? [];
             if (empty($ids) || !is_array($ids)) {
                 http_response_code(400);
                 die("No " . ($is_fw4a_export ? "FW4A entries" : "participants") . " selected.");
             }

             $sanitized_ids = [];
             foreach ($ids as $id) {
                 if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                     $sanitized_ids[] = $validated_id;
                 }
             }

             if (empty($sanitized_ids)) {
                 http_response_code(400);
                 die("Invalid " . ($is_fw4a_export ? "FW4A entries" : "participants") . " selected.");
             }

             $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
             $where_clauses[] = "id IN ($placeholders)";
             foreach ($sanitized_ids as $id) {
                 $query_params[] = $id;
                 $query_param_types .= 'i';
         }
         }

         $sql_where_clause = !empty($where_clauses) ? " WHERE " . implode(" AND ", $where_clauses) : "";

         if ($is_fw4a_export) {
             // SQL for FW4A table
             $sql = "SELECT `id`, `locality`, `barangay`, `district`, `locations`, `type`, `code`, `strategy`, `status`, `reason`, `remarks` FROM tblfwfa" . $sql_where_clause . " ORDER BY id DESC";
         } else {
             // Original SQL for participants
         $sql = "SELECT `id`, `start`, `end`, `activity`, `indicator`, `fullname`, `sex`, `contact`, `email`, `mode`, `agency`, `sector`, `project`, `person`, `remarks` FROM tblparticipant" . $sql_where_clause . " ORDER BY id DESC";
         }

         $stmt = mysqli_prepare($conn, $sql);
         if (!$stmt) { error_log("Export " . ($is_fw4a_export ? "FW4A" : "Participants") . " Prepare Error: ".mysqli_error($conn)); http_response_code(500); die("Server error preparing query."); }

         if (!empty($query_params)) {
             try {
                 $bind_params_ref = [];
                 foreach ($query_params as $key => $value) {
                     $bind_params_ref[$key] = &$query_params[$key];
                 }
                 mysqli_stmt_bind_param($stmt, $query_param_types, ...$bind_params_ref);
             } catch (Exception $e) {
                 mysqli_stmt_close($stmt);
                 error_log("Export " . ($is_fw4a_export ? "FW4A" : "Participants") . " Bind Error: ".$e->getMessage());
                 http_response_code(500);
                 die("Server error binding parameters.");
             }
         }

         if (!mysqli_stmt_execute($stmt)) {
             $err=mysqli_stmt_error($stmt);
             mysqli_stmt_close($stmt);
             error_log("Export " . ($is_fw4a_export ? "FW4A" : "Participants") . " Execute Error: ".$err);
             http_response_code(500);
             die("Server error executing query.");
         }

         $result = mysqli_stmt_get_result($stmt);
         if (!$result) {
             $err=mysqli_stmt_error($stmt);
             mysqli_stmt_close($stmt);
             error_log("Export " . ($is_fw4a_export ? "FW4A" : "Participants") . " Result Error: ".$err);
             http_response_code(500);
             die("Server error retrieving data.");
         }

         if ($export_format === 'csv') {
             try {
                 header('Content-Type: text/csv; charset=utf-8');

                 if ($is_fw4a_export) {
                     header('Content-Disposition: attachment; filename="fw4a_export_' . date('Y-m-d') . '.csv"');
                 } else {
                 header('Content-Disposition: attachment; filename="participants_export_' . date('Y-m-d') . '.csv"');
                 }

                 $output = fopen('php://output', 'w');
                 if (!$output) {
                     mysqli_stmt_close($stmt);
                     http_response_code(500);
                     die("Failed to create export file");
                 }

                 if ($is_fw4a_export) {
                     // Headers for FW4A export
                     $headers = [ 'ID', 'Locality', 'Barangay', 'District', 'Locations', 'Type', 'Code', 'Strategy', 'Status', 'Reason', 'Remarks' ];
                 } else {
                     // Original headers for participants export
                 $headers = [ 'ID', 'Start Date', 'End Date', 'Activity', 'Indicator', 'Full Name', 'Sex', 'Contact', 'Email', 'Mode', 'Agency', 'Sector', 'Project', 'Responsible Person', 'Remarks' ];
                 }

                 if (fputcsv($output, $headers) === false) {
                     fclose($output);
                     mysqli_stmt_close($stmt);
                     http_response_code(500);
                     die("Failed to write CSV headers");
                 }

                 while ($row = mysqli_fetch_assoc($result)) {
                     if ($is_fw4a_export) {
                         // Row data for FW4A
                         $csv_row = [
                             $row['id'],
                             $row['locality'],
                             $row['barangay'],
                             $row['district'],
                             $row['locations'],
                             $row['type'],
                             $row['code'],
                             $row['strategy'],
                             $row['status'],
                             $row['reason'],
                             $row['remarks']
                         ];
                     } else {
                         // Original row data for participants
                         $csv_row = [
                             $row['id'],
                             $row['start'],
                             $row['end'],
                             $row['activity'],
                             $row['indicator'],
                             $row['fullname'],
                             $row['sex'],
                             $row['contact'],
                             $row['email'],
                             $row['mode'],
                             $row['agency'],
                             $row['sector'],
                             $row['project'],
                             $row['person'],
                             $row['remarks']
                         ];
                     }

                     if (fputcsv($output, $csv_row) === false) {
                         error_log("Export " . ($is_fw4a_export ? "FW4A" : "Participants") . ": Failed write row ID " . ($row['id'] ?? 'unknown'));
                     }
                 }

                 fclose($output);
                 mysqli_stmt_close($stmt);
                 if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
                     mysqli_close($conn);
                 }
                 exit;
             } catch (Exception $e) {
                 if (isset($stmt) && $stmt) mysqli_stmt_close($stmt);
                 error_log("Export " . ($is_fw4a_export ? "FW4A" : "Participants") . " CSV Error: ".$e->getMessage());
                 http_response_code(500);
                 die("Server error during CSV generation: " . $e->getMessage());
             }
         } else {
             mysqli_stmt_close($stmt);
             http_response_code(400);
             die("Unsupported export format.");
         }
         break;

     // --- NEW CASES FOR TARGET MANAGEMENT ---
    case 'getTargets':
        $category = $_POST['category'] ?? null;
        $year = $_POST['year'] ?? null; // Optional year filter

        if (!$category) { send_json_response(false, [], 'Category is required.'); }

        $sql = "SELECT id, category, subcategory, indicator, year, target_activities as target, target_participants FROM targets WHERE category = ?";
        $params = [$category];
        $types = "s";

        if ($year && filter_var($year, FILTER_VALIDATE_INT)) {
            $sql .= " AND year = ?";
            $params[] = $year;
            $types .= "i";
        }
        $sql .= " ORDER BY year DESC, subcategory ASC, indicator ASC";

        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Get Targets Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing query.'); }

        if (!empty($params)) {
            mysqli_stmt_bind_param($stmt, $types, ...$params);
        }

        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $targets = mysqli_fetch_all($result, MYSQLI_ASSOC);
        mysqli_stmt_close($stmt);

        send_json_response(true, $targets);
        break;

    case 'addTarget':
        $category = sanitize_input($conn, $_POST['category'] ?? null);
        $subcategory = sanitize_input($conn, $_POST['subcategory'] ?? null);
        $indicator = sanitize_input($conn, $_POST['indicator'] ?? null);
        $year = filter_input(INPUT_POST, 'year', FILTER_VALIDATE_INT);
        $target_value = filter_input(INPUT_POST, 'target', FILTER_VALIDATE_INT);
        $target_participants_value = filter_input(INPUT_POST, 'target_participants', FILTER_VALIDATE_INT);

        if (!$category || !$subcategory || !$indicator || !$year || $target_value === false || $target_value < 0) {
            send_json_response(false, [], 'Missing or invalid required fields.');
        }

        // If target_participants is provided but invalid, reject
        if ($target_participants_value !== null && $target_participants_value !== false && $target_participants_value < 0) {
            send_json_response(false, [], 'Target participants must be a positive number or empty.');
        }

        $sql = "INSERT INTO targets (category, subcategory, indicator, year, target_activities, target_participants) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Add Target Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing insert.'); }

        mysqli_stmt_bind_param($stmt, "sssiii", $category, $subcategory, $indicator, $year, $target_value, $target_participants_value);

        if (mysqli_stmt_execute($stmt)) {
            $new_id = mysqli_insert_id($conn);
            mysqli_stmt_close($stmt);
            send_json_response(true, ['id' => $new_id], 'Target added successfully.');
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Add Target Execute Error: " . $db_error);
            if (mysqli_errno($conn) == 1062) { // Duplicate entry
                 send_json_response(false, [], 'Error: A target with this combination (category, subcategory, indicator, year) might already exist.');
            } else {
                 send_json_response(false, [], 'DB error executing insert.');
            }
        }
        break;

    case 'updateTarget':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        $category = sanitize_input($conn, $_POST['category'] ?? null);
        $subcategory = sanitize_input($conn, $_POST['subcategory'] ?? null);
        $indicator = sanitize_input($conn, $_POST['indicator'] ?? null);
        $year = filter_input(INPUT_POST, 'year', FILTER_VALIDATE_INT);
        $target_value = filter_input(INPUT_POST, 'target', FILTER_VALIDATE_INT);
        $target_participants_value = filter_input(INPUT_POST, 'target_participants', FILTER_VALIDATE_INT);

        if (!$id || !$category || !$subcategory || !$indicator || !$year || $target_value === false || $target_value < 0) {
            send_json_response(false, [], 'Missing or invalid required fields for update.');
        }

        // If target_participants is provided but invalid, reject
        if ($target_participants_value !== null && $target_participants_value !== false && $target_participants_value < 0) {
            send_json_response(false, [], 'Target participants must be a positive number or empty.');
        }

        $sql = "UPDATE targets SET category = ?, subcategory = ?, indicator = ?, year = ?, target_activities = ?, target_participants = ? WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Update Target Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing update.'); }

        mysqli_stmt_bind_param($stmt, "sssiiii", $category, $subcategory, $indicator, $year, $target_value, $target_participants_value, $id);

        if (mysqli_stmt_execute($stmt)) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);
            if ($affected_rows > 0) {
                 send_json_response(true, [], 'Target updated successfully.');
            } else {
                 // Check if the target exists to differentiate between not found and no change
                 $check_stmt = mysqli_prepare($conn, "SELECT id FROM targets WHERE id = ?");
                 mysqli_stmt_bind_param($check_stmt, "i", $id);
                 mysqli_stmt_execute($check_stmt);
                 mysqli_stmt_store_result($check_stmt);
                 $exists = mysqli_stmt_num_rows($check_stmt) > 0;
                 mysqli_stmt_close($check_stmt);
                 if ($exists) {
                    send_json_response(true, [], 'No changes were made to the target.');
                 } else {
                    send_json_response(false, [], 'Target not found for update.');
                 }
            }
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Update Target Execute Error: " . $db_error);
             if (mysqli_errno($conn) == 1062) { // Duplicate entry check if needed
                 send_json_response(false, [], 'Error: Updating might create a duplicate target combination.');
             } else {
                send_json_response(false, [], 'DB error executing update.');
             }
        }
        break;

    case 'deleteTarget':
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        if (!$id) { send_json_response(false, [], 'Invalid Target ID.'); }

        $sql = "DELETE FROM targets WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) { error_log("Delete Target Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing delete.'); }

        mysqli_stmt_bind_param($stmt, "i", $id);

        if (mysqli_stmt_execute($stmt)) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);
            if ($affected_rows > 0) {
                 send_json_response(true, [], 'Target deleted successfully.');
            } else {
                 send_json_response(false, [], 'Target not found or already deleted.');
            }
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Delete Target Execute Error: " . $db_error);
            send_json_response(false, [], 'DB error executing delete.');
        }
        break;

    case 'deleteTech4edItems':
        $ids = $_POST['ids'] ?? [];
        if (empty($ids) || !is_array($ids)) {
            send_json_response(false, [], 'No Tech4ED DTC IDs provided.');
        }

        $sanitized_ids = [];
        foreach ($ids as $id) {
            if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                $sanitized_ids[] = $validated_id;
            }
        }

        if (empty($sanitized_ids)) {
            send_json_response(false, [], 'Invalid Tech4ED DTC IDs provided.');
        }

        $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
        $types = str_repeat('i', count($sanitized_ids));

        // --- Delete Tech4ED DTCs ---
        $sql_tech4ed = "DELETE FROM tbltech4ed WHERE id IN ($placeholders)";
        $stmt_tech4ed = mysqli_prepare($conn, $sql_tech4ed);

        if (!$stmt_tech4ed) {
            error_log("Delete Tech4ED DTCs: Failed prepare statement: " . mysqli_error($conn));
            send_json_response(false, [], 'DB Error preparing Tech4ED DTC deletion.');
        }

        $bind_params_ref = [];
        foreach ($sanitized_ids as $key => $value) {
            $bind_params_ref[$key] = &$sanitized_ids[$key];
        }

        mysqli_stmt_bind_param($stmt_tech4ed, $types, ...$bind_params_ref);

        if (mysqli_stmt_execute($stmt_tech4ed)) {
            $deleted_count = mysqli_stmt_affected_rows($stmt_tech4ed);
            mysqli_stmt_close($stmt_tech4ed);

            // Log the Tech4ED DTC deletion
            if (isset($_SESSION['user_id']) && isset($_SESSION['username']) && $deleted_count > 0) {
                $ids_str = implode(', ', $sanitized_ids);
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Deleted Tech4ED DTCs",
                    "delete",
                    null,
                    "tech4ed",
                    "Deleted $deleted_count Tech4ED DTC(s). IDs: $ids_str"
                );
            }

            send_json_response(true, [], "$deleted_count Tech4ED DTC(s) deleted successfully.");
        } else {
            $db_error = mysqli_stmt_error($stmt_tech4ed);
            mysqli_stmt_close($stmt_tech4ed);
            error_log("Delete Tech4ED DTCs: DB Execute Error: " . $db_error);
            send_json_response(false, [], 'DB Error deleting Tech4ED DTCs.');
        }
        break;

    // --- NEW CASE FOR REPORT DATA ---
    case 'getReportData':
        $category = $_POST['category'] ?? null;
        $year = filter_input(INPUT_POST, 'year', FILTER_VALIDATE_INT);

        if (!$category || !$year) {
            send_json_response(false, [], 'Category and Year are required for report data.');
        }

        // 1. Get targets for the category and year
        $sql_targets = "SELECT id, subcategory, indicator, year, target_activities as target, target_participants FROM targets WHERE category = ? AND year = ?";
        $stmt_targets = mysqli_prepare($conn, $sql_targets);
        if (!$stmt_targets) { error_log("Get Report Data (Targets) Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing targets query.'); }
        mysqli_stmt_bind_param($stmt_targets, "si", $category, $year);
        mysqli_stmt_execute($stmt_targets);
        $result_targets = mysqli_stmt_get_result($stmt_targets);
        $targets_data = mysqli_fetch_all($result_targets, MYSQLI_ASSOC);
        mysqli_stmt_close($stmt_targets);

        $report_data = [];

        if (empty($targets_data)) {
             send_json_response(true, [], 'No targets found for this category/year.'); // Success but empty data
        }

        // 2. For each target, get accomplishment counts from tblactivity
        $sql_activity_count = "SELECT COUNT(*) as accomplishment_count FROM tblactivity WHERE project = ? AND indicator = ? AND YEAR(start) = ?";
        $stmt_activity_count = mysqli_prepare($conn, $sql_activity_count);
        if (!$stmt_activity_count) { error_log("Get Report Data (Activity Count) Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing activity count query.'); }

        // Get sum of participants from tblactivity instead of counting from tblparticipant
        $sql_participant_sum = "SELECT COALESCE(SUM(participants), 0) as participant_sum FROM tblactivity WHERE project = ? AND indicator = ? AND YEAR(start) = ?";
        $stmt_participant_sum = mysqli_prepare($conn, $sql_participant_sum);
        if (!$stmt_participant_sum) { error_log("Get Report Data (Participant Sum) Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing participant sum query.'); }

        foreach ($targets_data as $target_row) {
            $indicator = $target_row['indicator'];
            $activity_count = 0;
            $participant_count = 0;

            // Get activity count
            mysqli_stmt_bind_param($stmt_activity_count, "ssi", $category, $indicator, $year);
            if (mysqli_stmt_execute($stmt_activity_count)) {
                $result_activity = mysqli_stmt_get_result($stmt_activity_count);
                $activity_row = mysqli_fetch_assoc($result_activity);
                $activity_count = $activity_row ? (int)$activity_row['accomplishment_count'] : 0;
                if($result_activity) mysqli_free_result($result_activity); // Free result
            } else {
                error_log("Error executing activity count query for indicator '$indicator': " . mysqli_stmt_error($stmt_activity_count));
                // Continue, but count will be 0
            }

            // Get sum of participants if target_participants is set
            if ($target_row['target_participants'] !== null) {
                mysqli_stmt_bind_param($stmt_participant_sum, "ssi", $category, $indicator, $year);
                if (mysqli_stmt_execute($stmt_participant_sum)) {
                    $result_participant = mysqli_stmt_get_result($stmt_participant_sum);
                    $participant_row = mysqli_fetch_assoc($result_participant);
                    $participant_count = $participant_row ? (int)$participant_row['participant_sum'] : 0;
                    if($result_participant) mysqli_free_result($result_participant); // Free result
                } else {
                    error_log("Error executing participant sum query for indicator '$indicator': " . mysqli_stmt_error($stmt_participant_sum));
                    // Continue, but count will be 0
                }
            }

            // Create data entry
            $data_entry = [
                'subcategory' => $target_row['subcategory'],
                'indicator' => $target_row['indicator'],
                'year' => $target_row['year'],
                'target' => (int)$target_row['target'],
                'accomplishment' => $activity_count,
                'type' => 'activity'
            ];

            // Add to report data
            $report_data[] = $data_entry;

            // If target_participants is set and greater than 0, add a separate entry for participants
            if ($target_row['target_participants'] !== null && (int)$target_row['target_participants'] > 0) {
                $report_data[] = [
                    'subcategory' => $target_row['subcategory'],
                    'indicator' => $target_row['indicator'],
                    'year' => $target_row['year'],
                    'target' => (int)$target_row['target_participants'],
                    'accomplishment' => $participant_count,
                    'type' => 'participant'
                ];
            }
        }

        mysqli_stmt_close($stmt_activity_count);
        if ($stmt_participant_sum) mysqli_stmt_close($stmt_participant_sum);

        // Sort data for consistent graph order (optional)
        usort($report_data, function($a, $b) {
            // First sort by subcategory
            $sub_cmp = strcmp($a['subcategory'] ?? '', $b['subcategory'] ?? '');
            if ($sub_cmp !== 0) return $sub_cmp;

            // Then by indicator
            $ind_cmp = strcmp($a['indicator'] ?? '', $b['indicator'] ?? '');
            if ($ind_cmp !== 0) return $ind_cmp;

            // Finally by type (activity first, then participant)
            return strcmp($a['type'] ?? '', $b['type'] ?? '');
        });


        send_json_response(true, $report_data);
        break;


    case 'getCurrentUser':
        // Get current user data from session
        if (!isset($_SESSION)) {
            session_start();
        }

        if (!isset($_SESSION['user_id'])) {
            send_json_response(false, [], 'User not logged in.');
        }

        $user_id = $_SESSION['user_id'];
        $sql = "SELECT user_id, username, full_name, email FROM tblusers WHERE user_id = ?";
        $stmt = mysqli_prepare($conn, $sql);

        if (!$stmt) {
            error_log("Get Current User Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB error preparing query.');
        }

        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $user_data = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);

        if ($user_data) {
            send_json_response(true, $user_data);
        } else {
            send_json_response(false, [], 'User not found.');
        }
        break;

    case 'updateProfile':
        // Update user profile
        if (!isset($_SESSION)) {
            session_start();
        }

        // Verify user is logged in and updating their own profile
        if (!isset($_SESSION['user_id'])) {
            send_json_response(false, [], 'User not logged in.');
        }

        $session_user_id = $_SESSION['user_id'];
        $user_id = filter_input(INPUT_POST, 'user_id', FILTER_VALIDATE_INT);

        if ($user_id != $session_user_id) {
            send_json_response(false, [], 'You can only update your own profile.');
        }

        $username = sanitize_input($conn, $_POST['username'] ?? '');
        $full_name = sanitize_input($conn, $_POST['full_name'] ?? '');
        $email = sanitize_input($conn, $_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';

        // Validate required fields
        if (empty($username) || empty($full_name)) {
            send_json_response(false, [], 'Username and Full Name are required.');
        }

        // Check if username already exists (for another user)
        $check_sql = "SELECT user_id FROM tblusers WHERE username = ? AND user_id != ?";
        $check_stmt = mysqli_prepare($conn, $check_sql);

        if (!$check_stmt) {
            error_log("Update Profile Check Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB error checking username.');
        }

        mysqli_stmt_bind_param($check_stmt, "si", $username, $user_id);
        mysqli_stmt_execute($check_stmt);
        mysqli_stmt_store_result($check_stmt);
        $username_exists = mysqli_stmt_num_rows($check_stmt) > 0;
        mysqli_stmt_close($check_stmt);

        if ($username_exists) {
            send_json_response(false, [], 'Username already exists. Please choose another.');
        }

        // Prepare update SQL based on whether password is being changed
        if (!empty($password)) {
            // Validate password length
            if (strlen($password) < 8) {
                send_json_response(false, [], 'Password must be at least 8 characters long.');
            }

            // Hash the new password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            $sql = "UPDATE tblusers SET username = ?, full_name = ?, email = ?, password = ?, updated_at = NOW() WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $sql);

            if (!$stmt) {
                error_log("Update Profile with Password Prepare Error: " . mysqli_error($conn));
                send_json_response(false, [], 'DB error preparing update.');
            }

            mysqli_stmt_bind_param($stmt, "ssssi", $username, $full_name, $email, $hashed_password, $user_id);
        } else {
            // Update without changing password
            $sql = "UPDATE tblusers SET username = ?, full_name = ?, email = ?, updated_at = NOW() WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $sql);

            if (!$stmt) {
                error_log("Update Profile Prepare Error: " . mysqli_error($conn));
                send_json_response(false, [], 'DB error preparing update.');
            }

            mysqli_stmt_bind_param($stmt, "sssi", $username, $full_name, $email, $user_id);
        }

        if (mysqli_stmt_execute($stmt)) {
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);

            if ($affected_rows > 0) {
                // Update session data
                $_SESSION['username'] = $username;
                $_SESSION['full_name'] = $full_name;

                send_json_response(true, [
                    'username' => $username,
                    'full_name' => $full_name
                ], 'Profile updated successfully.');
            } else {
                send_json_response(true, [], 'No changes were made to your profile.');
            }
        } else {
            $db_error = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Update Profile Execute Error: " . $db_error);
            send_json_response(false, [], 'DB error executing update.');
        }
        break;
        case 'getUser': // Fetch data for a single user (for edit modal)
            if (!isset($_SESSION['user_id'])) { send_json_response(false, [], 'Authentication required.'); } // Any logged-in user can request *a* user's data (for edit form prep)

            $user_id = filter_input(INPUT_POST, 'user_id', FILTER_VALIDATE_INT);
            if (!$user_id) { send_json_response(false, [], 'Invalid or missing User ID.'); }

            // Select fields needed for the edit form - **NEVER the password hash**
            $sql = "SELECT `user_id`, `username`, `full_name`, `email`, `role`, `status`, `bureau`, `reset_password` FROM `tblusers` WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            if (!$stmt) { error_log("Get User Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing query.'); }

            mysqli_stmt_bind_param($stmt, "i", $user_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $data = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);

            if ($data) {
                send_json_response(true, $data);
            } else {
                send_json_response(false, [], 'User not found.');
            }
            break;

        case 'addInventory':
            // Add a new inventory item

            // Create inventory table if it doesn't exist
            $create_table_sql = "CREATE TABLE IF NOT EXISTS `inventory` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `project` varchar(255) NOT NULL,
                `item` varchar(255) NOT NULL,
                `classification` varchar(100) DEFAULT NULL,
                `quantity` int(11) NOT NULL DEFAULT 1,
                `unit` varchar(50) DEFAULT NULL,
                `description` text DEFAULT NULL,
                `received` varchar(255) DEFAULT NULL,
                `property` varchar(255) DEFAULT NULL,
                `ics` varchar(255) DEFAULT NULL,
                `serial` varchar(255) DEFAULT NULL,
                `date` date DEFAULT NULL,
                `officer` varchar(255) DEFAULT NULL,
                `cost` decimal(10,2) DEFAULT 0.00,
                `life` varchar(100) DEFAULT NULL,
                `transferred` varchar(255) DEFAULT NULL,
                `remarks` text DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

            if (!mysqli_query($conn, $create_table_sql)) {
                error_log("Add Inventory: Failed to create inventory table: " . mysqli_error($conn));
                send_json_response(false, [], 'Server error: Failed to create inventory table.');
            }
            $allowed_fields = [
                'project' => 's',
                'item' => 's',
                'classification' => 's',
                'quantity' => 'i',
                'unit' => 's',
                'description' => 's',
                'received' => 's',
                'property' => 's',
                'ics' => 's',
                'serial' => 's',
                'date' => 's',
                'officer' => 's',
                'cost' => 'd',
                'life' => 's',
                'transferred' => 's',
                'remarks' => 's'
            ];

            $insert_fields = [];
            $insert_values = [];
            $params = [];
            $types = "";

            foreach ($allowed_fields as $field => $type) {
                if (array_key_exists($field, $_POST)) {
                    $value = trim($_POST[$field]);

                    // Handle empty date field
                    if ($field === 'date' && $value === '') {
                        $value = null;
                    }

                    // Handle numeric fields
                    if ($type === 'i') {
                        $value = filter_var($value, FILTER_VALIDATE_INT) !== false ? intval($value) : 0;
                    } else if ($type === 'd') {
                        $value = filter_var($value, FILTER_VALIDATE_FLOAT) !== false ? floatval($value) : 0.0;
                    }

                    $insert_fields[] = "`$field`";
                    $insert_values[] = "?";
                    $params[] = $value;
                    $types .= $type;
                }
            }

            if (empty($insert_fields)) {
                send_json_response(false, [], 'No valid data provided.');
            }

            $sql = "INSERT INTO inventory (" . implode(', ', $insert_fields) . ") VALUES (" . implode(', ', $insert_values) . ")";
            $stmt = mysqli_prepare($conn, $sql);

            if (!$stmt) {
                error_log("Add Inventory DB Prepare Error: " . mysqli_error($conn));
                send_json_response(false, [], 'DB Error preparing insert.');
            }

            $bind_params_ref = [];
            foreach ($params as $key => $value) {
                $bind_params_ref[$key] = &$params[$key];
            }

            mysqli_stmt_bind_param($stmt, $types, ...$bind_params_ref);

            if (mysqli_stmt_execute($stmt)) {
                $new_id = mysqli_insert_id($conn);
                mysqli_stmt_close($stmt);

                if ($new_id) {
                    // Log the inventory addition
                    if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                        add_log_entry(
                            $_SESSION['user_id'],
                            $_SESSION['username'],
                            "Added new inventory item",
                            "add",
                            $new_id,
                            "inventory",
                            "Inventory ID: " . $new_id . ", Item: " . ($_POST['item'] ?? 'Unknown')
                        );
                    }
                    send_json_response(true, ['id' => $new_id], 'Inventory item added successfully.');
                } else {
                    send_json_response(false, [], 'Failed to get ID of new inventory item.');
                }
            } else {
                $db_error = mysqli_stmt_error($stmt);
                mysqli_stmt_close($stmt);
                error_log("Add Inventory DB Execute Error: " . $db_error);
                send_json_response(false, [], 'DB Error executing insert.');
            }
            break;

        case 'addUser':
            require_admin_privileges(); // Ensure only admin can add users

            // Sanitize and validate inputs
            $username = sanitize_input($conn, $_POST['username'] ?? '');
            $full_name = sanitize_input($conn, $_POST['full_name'] ?? '');
            $email = sanitize_input($conn, $_POST['email'] ?? null); // Email can be null
            $password = $_POST['password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            $role = sanitize_input($conn, $_POST['role'] ?? 'user'); // Default to 'user'
            $status = sanitize_input($conn, $_POST['status'] ?? 'active'); // Default to 'active'
            $bureau = sanitize_input($conn, $_POST['bureau'] ?? ''); // Bureau for access control
            $reset_password = sanitize_input($conn, $_POST['reset_password'] ?? null); // Reset password token

            // Basic Validation
            if (empty($username) || empty($full_name) || empty($password) || empty($confirm_password) || empty($bureau)) {
                send_json_response(false, [], 'Username, Full Name, Password, Confirm Password, and Bureau are required.');
            }
            if (strlen($password) < 8) {
                send_json_response(false, [], 'Password must be at least 8 characters long.');
            }
            if ($password !== $confirm_password) {
                send_json_response(false, [], 'Passwords do not match.');
            }
            if (!in_array($role, ['admin', 'user'])) { // Add other valid roles if needed
                send_json_response(false, [], 'Invalid role selected.');
            }
             if (!in_array($status, ['active', 'inactive'])) {
                send_json_response(false, [], 'Invalid status selected.');
            }
             if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                send_json_response(false, [], 'Invalid email format.');
                $email = null; // Don't store invalid email
            }

            // Check if username already exists
            $check_sql = "SELECT user_id FROM tblusers WHERE username = ?";
            $check_stmt = mysqli_prepare($conn, $check_sql);
            mysqli_stmt_bind_param($check_stmt, "s", $username);
            mysqli_stmt_execute($check_stmt);
            mysqli_stmt_store_result($check_stmt);
            $username_exists = mysqli_stmt_num_rows($check_stmt) > 0;
            mysqli_stmt_close($check_stmt);

            if ($username_exists) {
                send_json_response(false, [], 'Username already exists. Please choose another.');
            }

            // Hash the password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            if ($hashed_password === false) {
                 error_log("Password hashing failed for addUser.");
                 send_json_response(false, [], 'Server error during user creation (hashing).');
            }


            // Prepare Insert Statement
            $sql = "INSERT INTO tblusers (username, password, full_name, email, role, status, bureau, reset_password, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            $stmt = mysqli_prepare($conn, $sql);
            if (!$stmt) { error_log("Add User Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing insert.'); }

            mysqli_stmt_bind_param($stmt, "ssssssss", $username, $hashed_password, $full_name, $email, $role, $status, $bureau, $reset_password);

            if (mysqli_stmt_execute($stmt)) {
                $new_id = mysqli_insert_id($conn);
                mysqli_stmt_close($stmt);
                send_json_response(true, ['id' => $new_id], 'User added successfully.');
            } else {
                $db_error = mysqli_stmt_error($stmt);
                mysqli_stmt_close($stmt);
                error_log("Add User Execute Error: " . $db_error);
                send_json_response(false, [], 'DB error executing insert.');
            }
            break;

        case 'updateUser':
            require_admin_privileges(); // Ensure only admin can update users

            $user_id = filter_input(INPUT_POST, 'user_id', FILTER_VALIDATE_INT);
            if (!$user_id) { send_json_response(false, [], 'Invalid or missing User ID.'); }

            // --- Prevent Admin Self-Role/Status Change ---
            $session_user_id = $_SESSION['user_id'] ?? null;
            $session_role = $_SESSION['role'] ?? null;

            // Get values from POST
            $username = sanitize_input($conn, $_POST['username'] ?? '');
            $full_name = sanitize_input($conn, $_POST['full_name'] ?? '');
            $email = sanitize_input($conn, $_POST['email'] ?? null);
            $password = $_POST['password'] ?? null; // Optional password
            $confirm_password = $_POST['confirm_password'] ?? null;
            $role = sanitize_input($conn, $_POST['role'] ?? null);
            $status = sanitize_input($conn, $_POST['status'] ?? null);
            $bureau = sanitize_input($conn, $_POST['bureau'] ?? null);
            $reset_password = sanitize_input($conn, $_POST['reset_password'] ?? null); // Reset password token

            // Basic Validation
            if (empty($username) || empty($full_name) || empty($bureau)) {
                send_json_response(false, [], 'Username, Full Name, and Bureau are required.');
            }
            if ($role && !in_array($role, ['admin', 'user'])) { // Add other valid roles if needed
                send_json_response(false, [], 'Invalid role selected.');
            }
             if ($status && !in_array($status, ['active', 'inactive'])) {
                send_json_response(false, [], 'Invalid status selected.');
            }
             if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                send_json_response(false, [], 'Invalid email format.');
                $email = null;
            }


            // Handle self-edit restrictions
            if ($session_user_id == $user_id && $session_role === 'admin') {
                 // Fetch the user's current role and status to ensure they aren't changed
                 $current_data_sql = "SELECT role, status FROM tblusers WHERE user_id = ?";
                 $current_stmt = mysqli_prepare($conn, $current_data_sql);
                 mysqli_stmt_bind_param($current_stmt, "i", $user_id);
                 mysqli_stmt_execute($current_stmt);
                 $current_result = mysqli_stmt_get_result($current_stmt);
                 $current_data = mysqli_fetch_assoc($current_result);
                 mysqli_stmt_close($current_stmt);

                 if ($current_data) {
                     // If the POSTed role/status is different from current, deny or revert
                     if (isset($_POST['role']) && $role !== $current_data['role']) {
                          error_log("Admin self-edit attempt: Tried to change role for user ID $user_id");
                          send_json_response(false, [], 'Admins cannot change their own role.');
                          // Or alternatively, just force the role back: $role = $current_data['role'];
                     }
                     if (isset($_POST['status']) && $status !== $current_data['status']) {
                          error_log("Admin self-edit attempt: Tried to change status for user ID $user_id");
                          send_json_response(false, [], 'Admins cannot change their own status.');
                          // Or alternatively, force the status back: $status = $current_data['status'];
                     }
                 }
                 // Ensure role and status are set if they weren't in POST but needed due to check
                 $role = $role ?? $current_data['role'] ?? 'admin';
                 $status = $status ?? $current_data['status'] ?? 'active';
            }


            // Check if username already exists (for another user)
            $check_sql = "SELECT user_id FROM tblusers WHERE username = ? AND user_id != ?";
            $check_stmt = mysqli_prepare($conn, $check_sql);
            mysqli_stmt_bind_param($check_stmt, "si", $username, $user_id);
            mysqli_stmt_execute($check_stmt);
            mysqli_stmt_store_result($check_stmt);
            $username_exists = mysqli_stmt_num_rows($check_stmt) > 0;
            mysqli_stmt_close($check_stmt);

            if ($username_exists) {
                send_json_response(false, [], 'Username already exists for another user.');
            }

            // Prepare SQL update fields and params
            $sql_parts = [];
            $params = [];
            $types = "";

            $sql_parts[] = "username = ?"; $params[] = $username; $types .= "s";
            $sql_parts[] = "full_name = ?"; $params[] = $full_name; $types .= "s";
            $sql_parts[] = "email = ?"; $params[] = $email; $types .= "s";
            $sql_parts[] = "role = ?"; $params[] = $role; $types .= "s";
            $sql_parts[] = "status = ?"; $params[] = $status; $types .= "s";
            $sql_parts[] = "bureau = ?"; $params[] = $bureau; $types .= "s";
            $sql_parts[] = "reset_password = ?"; $params[] = $reset_password; $types .= "s";

            // Handle optional password update
            if (!empty($password)) {
                if (strlen($password) < 8) {
                    send_json_response(false, [], 'New password must be at least 8 characters long.');
                }
                if ($password !== $confirm_password) {
                    send_json_response(false, [], 'New passwords do not match.');
                }
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                 if ($hashed_password === false) {
                     error_log("Password hashing failed for updateUser ID $user_id.");
                     send_json_response(false, [], 'Server error during user update (hashing).');
                 }
                $sql_parts[] = "password = ?";
                $params[] = $hashed_password;
                $types .= "s";
            }

            $sql_parts[] = "updated_at = NOW()";
            // WHERE clause param
            $params[] = $user_id;
            $types .= "i";

            $sql = "UPDATE tblusers SET " . implode(', ', $sql_parts) . " WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            if (!$stmt) { error_log("Update User Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing update.'); }

            // Use call_user_func_array for dynamic binding
            $bind_params_ref = [];
            foreach ($params as $key => $value) {
                $bind_params_ref[$key] = &$params[$key];
            }
            call_user_func_array('mysqli_stmt_bind_param', array_merge([$stmt, $types], $bind_params_ref));


            if (mysqli_stmt_execute($stmt)) {
                $affected_rows = mysqli_stmt_affected_rows($stmt);
                mysqli_stmt_close($stmt);

                // Get the updated user data to return
                $user_data = [];
                $get_user_stmt = mysqli_prepare($conn, "SELECT user_id, username, full_name, email, role, status, bureau, reset_password FROM tblusers WHERE user_id = ?");
                if ($get_user_stmt) {
                    mysqli_stmt_bind_param($get_user_stmt, "i", $user_id);
                    mysqli_stmt_execute($get_user_stmt);
                    $result = mysqli_stmt_get_result($get_user_stmt);
                    $user_data = mysqli_fetch_assoc($result);
                    mysqli_stmt_close($get_user_stmt);

                    // If the updated user is the currently logged-in user, update session variables
                    if ($user_id == $session_user_id) {
                        $_SESSION['username'] = $user_data['username'];
                        $_SESSION['full_name'] = $user_data['full_name'];
                    }
                }

                if ($affected_rows > 0) {
                    send_json_response(true, $user_data, 'User updated successfully.');
                } else {
                     // Check if the user actually exists to differentiate no change vs not found
                     $check_exist_stmt = mysqli_prepare($conn, "SELECT user_id FROM tblusers WHERE user_id = ?");
                     mysqli_stmt_bind_param($check_exist_stmt, "i", $user_id);
                     mysqli_stmt_execute($check_exist_stmt);
                     mysqli_stmt_store_result($check_exist_stmt);
                     $exists = mysqli_stmt_num_rows($check_exist_stmt) > 0;
                     mysqli_stmt_close($check_exist_stmt);

                     if ($exists) {
                         send_json_response(true, $user_data, 'No changes were made.'); // Success, but no rows affected
                     } else {
                         send_json_response(false, [], 'User not found for update.');
                     }
                }
            } else {
                $db_error = mysqli_stmt_error($stmt);
                mysqli_stmt_close($stmt);
                error_log("Update User Execute Error: " . $db_error);
                 if (mysqli_errno($conn) == 1062) { // Duplicate entry (e.g., if unique constraint on email)
                     send_json_response(false, [], 'Error: Update might create a duplicate username or email.');
                 } else {
                    send_json_response(false, [], 'DB error executing update.');
                 }
            }
            break;

        case 'deleteUser':
            require_admin_privileges(); // Ensure only admin can delete users

            $user_id = filter_input(INPUT_POST, 'user_id', FILTER_VALIDATE_INT);
            if (!$user_id) { send_json_response(false, [], 'Invalid or missing User ID.'); }

            // --- Prevent Self-Deletion ---
            if (isset($_SESSION['user_id']) && $_SESSION['user_id'] == $user_id) {
                send_json_response(false, [], 'You cannot delete your own account.');
            }

            // Optional: Check if this is the last admin user? (More complex logic needed)
            // $check_last_admin_sql = "SELECT COUNT(*) as admin_count FROM tblusers WHERE role = 'admin' AND status = 'active'"; ...

            $sql = "DELETE FROM tblusers WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            if (!$stmt) { error_log("Delete User Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing delete.'); }

            mysqli_stmt_bind_param($stmt, "i", $user_id);

            if (mysqli_stmt_execute($stmt)) {
                $affected_rows = mysqli_stmt_affected_rows($stmt);
                mysqli_stmt_close($stmt);
                if ($affected_rows > 0) {
                    send_json_response(true, [], 'User deleted successfully.');
                } else {
                    send_json_response(false, [], 'User not found or already deleted.');
                }
            } else {
                $db_error = mysqli_stmt_error($stmt);
                mysqli_stmt_close($stmt);
                error_log("Delete User Execute Error: " . $db_error);
                // Check for foreign key constraints if applicable
                if (mysqli_errno($conn) == 1451) { // Cannot delete or update a parent row
                     send_json_response(false, [], 'Cannot delete user. They might be associated with other records.');
                } else {
                    send_json_response(false, [], 'DB error executing delete.');
                }
            }
            break;

        // ========================================
        // END: New Cases for Credentials
        // ========================================


        // --- Profile Update Cases (Copied from index.php - ensure session handling is correct) ---
        case 'getCurrentUser': // Used by profile update modal
            // Get current user data from session
            if (!isset($_SESSION['user_id'])) { send_json_response(false, [], 'User not logged in.'); }
            $user_id = $_SESSION['user_id'];

            $sql = "SELECT user_id, username, full_name, email FROM tblusers WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            if (!$stmt) { error_log("Get Current User Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing query.'); }

            mysqli_stmt_bind_param($stmt, "i", $user_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user_data = mysqli_fetch_assoc($result);
            mysqli_stmt_close($stmt);

            if ($user_data) { send_json_response(true, $user_data); }
            else { send_json_response(false, [], 'User not found.'); }
            break;

        case 'updateProfile': // Used by profile update modal
            // Update user profile (self-update)
            if (!isset($_SESSION['user_id'])) { send_json_response(false, [], 'User not logged in.'); }

            $session_user_id = $_SESSION['user_id'];
            $user_id_from_post = filter_input(INPUT_POST, 'user_id', FILTER_VALIDATE_INT);

            // IMPORTANT: Ensure user is only updating their *own* profile via this action
            if ($user_id_from_post != $session_user_id) {
                 error_log("Security Alert: User ID $session_user_id attempted to update profile for User ID $user_id_from_post via updateProfile action.");
                send_json_response(false, [], 'Authorization denied.');
            }

            $username = sanitize_input($conn, $_POST['username'] ?? '');
            $full_name = sanitize_input($conn, $_POST['full_name'] ?? '');
            $email = sanitize_input($conn, $_POST['email'] ?? '');
            $password = $_POST['password'] ?? ''; // Optional new password

            // Validate required fields
            if (empty($username) || empty($full_name)) { send_json_response(false, [], 'Username and Full Name are required.'); }
            if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) { send_json_response(false, [], 'Invalid email format.'); $email = null; }

            // Check if username already exists (for another user)
            $check_sql = "SELECT user_id FROM tblusers WHERE username = ? AND user_id != ?";
            $check_stmt = mysqli_prepare($conn, $check_sql);
            if (!$check_stmt) { error_log("Update Profile Check Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error checking username.'); }
            mysqli_stmt_bind_param($check_stmt, "si", $username, $session_user_id);
            mysqli_stmt_execute($check_stmt); mysqli_stmt_store_result($check_stmt); $username_exists = mysqli_stmt_num_rows($check_stmt) > 0; mysqli_stmt_close($check_stmt);
            if ($username_exists) { send_json_response(false, [], 'Username already exists. Please choose another.'); }

            // Prepare update SQL
            $sql_parts = []; $params = []; $types = "";
            $sql_parts[] = "username = ?"; $params[] = $username; $types .= "s";
            $sql_parts[] = "full_name = ?"; $params[] = $full_name; $types .= "s";
            $sql_parts[] = "email = ?"; $params[] = $email; $types .= "s";

            if (!empty($password)) {
                if (strlen($password) < 8) { send_json_response(false, [], 'Password must be at least 8 characters long.'); }
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                 if ($hashed_password === false) { error_log("Password hashing failed for updateProfile User ID $session_user_id."); send_json_response(false, [], 'Server error updating profile (hashing).'); }
                $sql_parts[] = "password = ?"; $params[] = $hashed_password; $types .= "s";
            }
            $sql_parts[] = "updated_at = NOW()";
            $params[] = $session_user_id; $types .= "i"; // Add user_id for WHERE clause

            $sql = "UPDATE tblusers SET " . implode(', ', $sql_parts) . " WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            if (!$stmt) { error_log("Update Profile Prepare Error: " . mysqli_error($conn)); send_json_response(false, [], 'DB error preparing profile update.'); }

            // Bind parameters dynamically
            $bind_params_ref = []; foreach ($params as $key => $value) { $bind_params_ref[$key] = &$params[$key]; }
            call_user_func_array('mysqli_stmt_bind_param', array_merge([$stmt, $types], $bind_params_ref));

            if (mysqli_stmt_execute($stmt)) {
                $affected_rows = mysqli_stmt_affected_rows($stmt);
                mysqli_stmt_close($stmt);
                // Update session data if needed
                $_SESSION['username'] = $username; $_SESSION['full_name'] = $full_name;
                send_json_response(true, ['username' => $username, 'full_name' => $full_name ], $affected_rows > 0 ? 'Profile updated successfully.' : 'No changes were made to your profile.');
            } else {
                $db_error = mysqli_stmt_error($stmt); mysqli_stmt_close($stmt); error_log("Update Profile Execute Error: " . $db_error);
                if (mysqli_errno($conn) == 1062) { send_json_response(false, [], 'Error: Update might create a duplicate username or email.'); }
                else { send_json_response(false, [], 'DB error executing profile update.'); }
            }
            break;
    case 'importTech4ed':
        // Parse the data from POST
        $tech4ed_items = isset($_POST['tech4ed_items']) ? (is_array($_POST['tech4ed_items']) ? $_POST['tech4ed_items'] : json_decode($_POST['tech4ed_items'], true)) : [];

        if (empty($tech4ed_items) || !is_array($tech4ed_items)) {
            send_json_response(false, [], 'No valid Tech4ED data found for import.');
        }

        $imported_count = 0;
        $errors = [];

        // Define column mapping (CSV column name => DB column name)
        $column_mapping = [
            'region' => 'region',
            'province' => 'province',
            'district' => 'district',
            'municipality' => 'municipality',
            'barangay' => 'barangay',
            'street' => 'street',
            'location' => 'location',
            'center name' => 'cname',
            'host' => 'host',
            'category' => 'category',
            'longitude' => 'longitude',
            'latitude' => 'latitude',
            'center manager' => 'cmanager',
            'cm email' => 'cemail',
            'cm mobile' => 'cmobile',
            'cm landline' => 'clandline',
            'cm gender' => 'cgender',
            'asst. manager' => 'amanager',
            'am email' => 'aemail',
            'am mobile' => 'amobile',
            'am landline' => 'alandline',
            'am gender' => 'agender',
            'launch date' => 'launch',
            'registration date' => 'registration',
            'operation date' => 'operation',
            'last visited' => 'visited',
            'desktop' => 'desktop',
            'laptop' => 'laptop',
            'printer' => 'printer',
            'scanner' => 'scanner',
            'status' => 'status',
            'network' => 'network',
            'connectivity' => 'connectivity',
            'speed' => 'speed',
            'cm male trainees' => 'cmtmale',
            'cm female trainees' => 'cmtfemale',
            's-training' => 'straining',
            'e-training' => 'etraining',
            'signing' => 'signing',
            'partner' => 'partner',
            'expiration' => 'expiration',
            'donation' => 'donation',
            'donation date' => 'datedonation',
            'tcms' => 'tcms',
            'key one' => 'key_one',
            'identifier' => 'identifier'
        ];

        // Prepare the insert statement with all possible fields
        $fields = array_values($column_mapping);
        $placeholders = implode(', ', array_fill(0, count($fields), '?'));
        $sql = "INSERT INTO tbltech4ed (" . implode(', ', $fields) . ") VALUES (" . $placeholders . ")";

        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Import Tech4ED Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'Database error preparing import.');
        }

        // Process each Tech4ED DTC item
        foreach ($tech4ed_items as $index => $item) {
            $row_num = $index + 2; // +2 because index is 0-based and we skip header row
            $values = [];
            $types = '';

            // Only center name is truly required
            if (empty($item['Center Name']) && empty($item['center name'])) {
                $errors[] = "Row $row_num: Center Name is required";
                continue;
            }

            // Map CSV columns to database fields
            foreach ($column_mapping as $csv_col => $db_col) {
                // Handle case insensitivity for column names
                $value = null;
                foreach ($item as $key => $val) {
                    if (strtolower($key) === $csv_col) {
                        $value = trim($val);
                        break;
                    }
                }

                // Handle date fields
                if (in_array($db_col, ['launch', 'registration', 'operation', 'visited', 'expiration', 'datedonation']) && !empty($value)) {
                    // Try to convert to MySQL date format
                    $date_value = null;

                    // Try different date formats
                    $date_formats = ['Y-m-d', 'm/d/Y', 'd/m/Y', 'Y/m/d', 'M d, Y'];
                    foreach ($date_formats as $format) {
                        $d = DateTime::createFromFormat($format, $value);
                        if ($d && $d->format($format) == $value) {
                            $date_value = $d->format('Y-m-d');
                            break;
                        }
                    }

                    if ($date_value) {
                        $value = $date_value;
                    } else {
                        // If we couldn't parse the date, set to null
                        $value = null;
                    }
                }

                // Handle numeric fields
                if (in_array($db_col, ['desktop', 'laptop', 'printer', 'scanner', 'cmtmale', 'cmtfemale']) && $value !== null) {
                    $value = is_numeric($value) ? (int)$value : 0;
                    $types .= 'i'; // integer
                } else {
                    $types .= 's'; // string or null
                }

                $values[] = $value;
            }

            // Bind parameters and execute
            mysqli_stmt_bind_param($stmt, $types, ...$values);

            // Try to insert the record
            if (mysqli_stmt_execute($stmt)) {
                $imported_count++;
            } else {
                $errors[] = "Row $row_num: Database error - " . mysqli_stmt_error($stmt);
                error_log("Import Tech4ED Error Row $row_num: " . mysqli_stmt_error($stmt));
            }
        }

        mysqli_stmt_close($stmt);

        if ($imported_count > 0) {
            $message = "Successfully imported $imported_count Tech4ED DTCs.";
            if (!empty($errors)) {
                $message .= " However, there were " . count($errors) . " error(s).";
            }
            send_json_response(true, [
                'imported_count' => $imported_count,
                'errors' => $errors
            ], $message);
        } else {
            send_json_response(false, [
                'imported_count' => 0,
                'errors' => $errors
            ], 'Failed to import any Tech4ED DTCs. Please check the file format and try again.');
        }
        break;

    case 'exportTech4ed':
        ini_set('memory_limit', '256M');
        ini_set('display_errors', 0);
        error_reporting(0); // Suppress errors for direct output

        if (!isset($conn) || !$conn || !@mysqli_ping($conn)) {
            require_once __DIR__ . '/config/database.php';
            if (!$conn || !@mysqli_ping($conn)) {
                http_response_code(500);
                die("DB connection error.");
            }
            mysqli_set_charset($conn, "utf8mb4");
        }

        if (ob_get_level() > 0) {
            ob_end_clean();
        }

        $export_type = $_POST['type'] ?? 'all';
        $export_format = $_POST['format'] ?? 'csv';

        $where_clauses = [];
        $query_params = [];
        $query_param_types = '';

        if ($export_type === 'filtered') {
            $search_term = $_POST['t_search'] ?? '';
            if (!empty($search_term)) {
                $like_term = '%' . $search_term . '%';
                $searchable_columns = [
                    'region', 'province', 'district', 'municipality', 'barangay', 'street', 'location',
                    'cname', 'host', 'category', 'cmanager', 'cemail', 'cmobile', 'clandline',
                    'amanager', 'aemail', 'amobile', 'alandline', 'status', 'network', 'connectivity'
                ];
                $search_conditions = [];
                foreach ($searchable_columns as $column) {
                    $search_conditions[] = "`" . $column . "` LIKE ?";
                    $query_params[] = $like_term;
                    $query_param_types .= 's';
                }
                $where_clauses[] = "(" . implode(" OR ", $search_conditions) . ")";
            }

            $filter_columns = ['region', 'province', 'district', 'municipality', 'status', 'connectivity'];
            foreach ($filter_columns as $column) {
                $filter_value = $_POST['t_'.$column] ?? '';
                if (!empty($filter_value)) {
                    $where_clauses[] = "`$column` = ?";
                    $query_params[] = $filter_value;
                    $query_param_types .= 's';
                }
            }
        } elseif ($export_type === 'selected') {
            $ids = $_POST['ids'] ?? [];
            if (empty($ids) || !is_array($ids)) {
                http_response_code(400);
                die("No Tech4ED DTCs selected.");
            }

            $sanitized_ids = [];
            foreach ($ids as $id) {
                if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                    $sanitized_ids[] = $validated_id;
                }
            }

            if (empty($sanitized_ids)) {
                http_response_code(400);
                die("Invalid Tech4ED DTCs selected.");
            }

            $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
            $where_clauses[] = "id IN ($placeholders)";
            foreach ($sanitized_ids as $id) {
                $query_params[] = $id;
                $query_param_types .= 'i';
            }
        }

        $sql_where_clause = !empty($where_clauses) ? " WHERE " . implode(" AND ", $where_clauses) : "";

        $sql = "SELECT * FROM tbltech4ed" . $sql_where_clause . " ORDER BY cname ASC";

        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Export Tech4ED Prepare Error: " . mysqli_error($conn));
            http_response_code(500);
            die("Server error preparing query.");
        }

        if (!empty($query_params)) {
            if (count($query_params) !== strlen($query_param_types)) {
                error_log("Export Tech4ED Param/Type mismatch: P=" . count($query_params) . " T=" . strlen($query_param_types));
                mysqli_stmt_close($stmt);
                http_response_code(500);
                die("Server error: Parameter mismatch.");
            }

            $bind_params_ref = [];
            foreach ($query_params as $key => $value) {
                $bind_params_ref[$key] = &$query_params[$key];
            }

            mysqli_stmt_bind_param($stmt, $query_param_types, ...$bind_params_ref);
        }

        if (!mysqli_stmt_execute($stmt)) {
            $err = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Export Tech4ED Execute Error: " . $err);
            http_response_code(500);
            die("Server error executing query.");
        }

        $result = mysqli_stmt_get_result($stmt);
        if (!$result) {
            $err = mysqli_stmt_error($stmt);
            mysqli_stmt_close($stmt);
            error_log("Export Tech4ED Result Error: " . $err);
            http_response_code(500);
            die("Server error retrieving data.");
        }

        if ($export_format === 'csv') {
            try {
                header('Content-Type: text/csv; charset=utf-8');
                header('Content-Disposition: attachment; filename="tech4ed_export_' . date('Y-m-d') . '.csv"');
                $output = fopen('php://output', 'w');
                if (!$output) {
                    mysqli_stmt_close($stmt);
                    http_response_code(500);
                    die("Failed to create export file");
                }

                // Use custom headers for Tech4ED export
                $headers = [
                    'Region',
                    'Province',
                    'District',
                    'Municipality',
                    'Barangay',
                    'Street',
                    'Location',
                    'Center Name',
                    'Host',
                    'Category',
                    'Longitude',
                    'Latitude',
                    'Center Manager',
                    'CM Email',
                    'CM Mobile',
                    'CM Landline',
                    'CM Gender',
                    'Asst. Manager',
                    'AM Email',
                    'AM Mobile',
                    'AM Landline',
                    'AM Gender',
                    'Launch Date',
                    'Registration Date',
                    'Operation Date',
                    'Last Visited',
                    'Desktop',
                    'Laptop',
                    'Printer',
                    'Scanner',
                    'Status',
                    'Network',
                    'Connectivity',
                    'Speed',
                    'CM Male Trainees',
                    'CM Female Trainees',
                    'S-Training',
                    'E-Training',
                    'Signing',
                    'Partner',
                    'Expiration',
                    'Donation',
                    'Donation Date',
                    'TCMS',
                    'Key One',
                    'Identifier'
                ];

                if (fputcsv($output, $headers) === false) {
                    fclose($output);
                    mysqli_stmt_close($stmt);
                    http_response_code(500);
                    die("Failed to write CSV headers");
                }

                while ($row = mysqli_fetch_assoc($result)) {
                    // Map database fields to the custom headers
                    $formatted_row = [
                        $row['region'] ?? '',
                        $row['province'] ?? '',
                        $row['district'] ?? '',
                        $row['municipality'] ?? '',
                        $row['barangay'] ?? '',
                        $row['street'] ?? '',
                        $row['location'] ?? '',
                        $row['cname'] ?? '',
                        $row['host'] ?? '',
                        $row['category'] ?? '',
                        $row['longitude'] ?? '',
                        $row['latitude'] ?? '',
                        $row['cmanager'] ?? '',
                        $row['cemail'] ?? '',
                        $row['cmobile'] ?? '',
                        $row['clandline'] ?? '',
                        $row['cgender'] ?? '',
                        $row['amanager'] ?? '',
                        $row['aemail'] ?? '',
                        $row['amobile'] ?? '',
                        $row['alandline'] ?? '',
                        $row['agender'] ?? '',
                        $row['launch'] ?? '',
                        $row['registration'] ?? '',
                        $row['operation'] ?? '',
                        $row['visited'] ?? '',
                        $row['desktop'] ?? '',
                        $row['laptop'] ?? '',
                        $row['printer'] ?? '',
                        $row['scanner'] ?? '',
                        $row['status'] ?? '',
                        $row['network'] ?? '',
                        $row['connectivity'] ?? '',
                        $row['speed'] ?? '',
                        $row['cmtmale'] ?? '',
                        $row['cmtfemale'] ?? '',
                        $row['straining'] ?? '',
                        $row['etraining'] ?? '',
                        $row['signing'] ?? '',
                        $row['partner'] ?? '',
                        $row['expiration'] ?? '',
                        $row['donation'] ?? '',
                        $row['datedonation'] ?? '',
                        $row['tcms'] ?? '',
                        $row['key_one'] ?? '',
                        $row['identifier'] ?? ''
                    ];

                    if (fputcsv($output, $formatted_row) === false) {
                        error_log("Export Tech4ED: Failed write row ID " . ($row['id'] ?? 'unknown'));
                    }
                }

                fclose($output);
                mysqli_stmt_close($stmt);
                if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
                    mysqli_close($conn);
                }
                exit;
            } catch (Exception $e) {
                if (isset($stmt) && $stmt) mysqli_stmt_close($stmt);
                error_log("Export Tech4ED CSV Error: " . $e->getMessage());
                http_response_code(500);
                die("Server error during CSV generation: " . $e->getMessage());
            }
        } else {
            mysqli_stmt_close($stmt);
            http_response_code(400);
            die("Unsupported export format.");
        }
        break;

    case 'getStrategyTable':
        // Fetch strategy counts per municipality for the strategy table
        try {
            $sql = "SELECT locality, strategy, COUNT(*) as count
                   FROM tblfwfa
                   WHERE strategy IS NOT NULL AND strategy != ''
                   GROUP BY locality, strategy
                   ORDER BY locality ASC, strategy ASC";

            $stmt = mysqli_prepare($conn, $sql);
            if (!$stmt) {
                error_log("Get Strategy Table Prepare Error: " . mysqli_error($conn));
                send_json_response(false, [], 'DB error preparing query.');
            }

            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);

            if (!$result) {
                $err = mysqli_stmt_error($stmt);
                mysqli_stmt_close($stmt);
                error_log("Get Strategy Table Result Error: " . $err);
                send_json_response(false, [], 'Error retrieving strategy data.');
            }

            // Fetch all strategies to build the column headers
            $strategies_sql = "SELECT DISTINCT strategy FROM tblfwfa WHERE strategy IS NOT NULL AND strategy != '' ORDER BY strategy ASC";
            $strategies_stmt = mysqli_prepare($conn, $strategies_sql);

            if (!$strategies_stmt) {
                mysqli_stmt_close($stmt);
                error_log("Get Strategies List Prepare Error: " . mysqli_error($conn));
                send_json_response(false, [], 'DB error preparing strategies query.');
            }

            mysqli_stmt_execute($strategies_stmt);
            $strategies_result = mysqli_stmt_get_result($strategies_stmt);

            if (!$strategies_result) {
                mysqli_stmt_close($stmt);
                mysqli_stmt_close($strategies_stmt);
                error_log("Get Strategies List Result Error: " . mysqli_stmt_error($strategies_stmt));
                send_json_response(false, [], 'Error retrieving strategies list.');
            }

            // Build array of all strategies
            $strategies = [];
            while ($strategy_row = mysqli_fetch_assoc($strategies_result)) {
                $strategies[] = $strategy_row['strategy'];
            }
            mysqli_stmt_close($strategies_stmt);

            // Process the data into a format suitable for the table
            $strategy_data = [];
            while ($row = mysqli_fetch_assoc($result)) {
                $locality = $row['locality'];
                $strategy = $row['strategy'];
                $count = (int)$row['count'];

                if (!isset($strategy_data[$locality])) {
                    $strategy_data[$locality] = [
                        'municipality' => $locality,
                        'total' => 0
                    ];

                    // Initialize all strategies to 0
                    foreach ($strategies as $s) {
                        $strategy_data[$locality][$s] = 0;
                    }
                }

                $strategy_data[$locality][$strategy] = $count;
                $strategy_data[$locality]['total'] += $count;
            }

            mysqli_stmt_close($stmt);

            // Convert to indexed array for easier handling in JavaScript
            $result_data = [
                'strategies' => $strategies,
                'data' => array_values($strategy_data)
            ];

            send_json_response(true, $result_data);

        } catch (Exception $e) {
            error_log("Get Strategy Table Exception: " . $e->getMessage());
            send_json_response(false, [], 'Unexpected error retrieving strategy data: ' . $e->getMessage());
        }
        break;

    case 'addInventory':
        // Add a new inventory item
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            send_json_response(false, [], 'Invalid request method.');
        }

        // Get JSON data from request
        $json_data = file_get_contents('php://input');
        $data = json_decode($json_data, true);

        if (!isset($data['data']) || !is_array($data['data'])) {
            send_json_response(false, [], 'Invalid data format.');
        }

        $inventory_data = $data['data'];

        // Sanitize all input fields
        $project = isset($inventory_data['project']) ? sanitize_input($conn, $inventory_data['project']) : '';
        $item = isset($inventory_data['item']) ? sanitize_input($conn, $inventory_data['item']) : '';
        $classification = isset($inventory_data['classification']) ? sanitize_input($conn, $inventory_data['classification']) : '';
        $quantity = isset($inventory_data['quantity']) ? intval($inventory_data['quantity']) : 0;
        $unit = isset($inventory_data['unit']) ? sanitize_input($conn, $inventory_data['unit']) : '';
        $description = isset($inventory_data['description']) ? sanitize_input($conn, $inventory_data['description']) : '';
        $received = isset($inventory_data['received']) ? sanitize_input($conn, $inventory_data['received']) : '';
        $property = isset($inventory_data['property']) ? sanitize_input($conn, $inventory_data['property']) : '';
        $ics = isset($inventory_data['ics']) ? sanitize_input($conn, $inventory_data['ics']) : '';
        $serial = isset($inventory_data['serial']) ? sanitize_input($conn, $inventory_data['serial']) : '';
        $date = isset($inventory_data['date']) && !empty($inventory_data['date']) ? sanitize_input($conn, $inventory_data['date']) : null;
        $officer = isset($inventory_data['officer']) ? sanitize_input($conn, $inventory_data['officer']) : '';
        $cost = isset($inventory_data['cost']) && is_numeric($inventory_data['cost']) ? floatval($inventory_data['cost']) : 0.00;
        $life = isset($inventory_data['life']) ? intval($inventory_data['life']) : 0;
        $transferred = isset($inventory_data['transferred']) ? sanitize_input($conn, $inventory_data['transferred']) : '';
        $remarks = isset($inventory_data['remarks']) ? sanitize_input($conn, $inventory_data['remarks']) : '';

        // Validate required fields
        if (empty($project) || empty($item) || empty($classification) || $quantity <= 0 || empty($unit) || empty($description)) {
            send_json_response(false, [], 'Required fields are missing. Please fill in all required fields.');
        }

        // Prepare SQL statement
        $sql = "INSERT INTO inventory (
            project, item, classification, quantity, unit, description,
            received, property, ics, serial, date, officer,
            cost, life, transferred, remarks
        ) VALUES (
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?
        )";

        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . mysqli_error($conn));
            send_json_response(false, [], 'Database error: ' . mysqli_error($conn));
        }

        // Bind parameters
        mysqli_stmt_bind_param($stmt,
            "sssississssdiss",
            $project, $item, $classification, $quantity, $unit, $description,
            $received, $property, $ics, $serial, $date, $officer,
            $cost, $life, $transferred, $remarks
        );

        // Execute statement
        if (mysqli_stmt_execute($stmt)) {
            $inventory_id = mysqli_insert_id($conn);
            mysqli_stmt_close($stmt);

            // Log the action
            if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Added Inventory Item",
                    "add",
                    $inventory_id,
                    "inventory",
                    "Inventory ID: " . $inventory_id . ", Item: " . $item
                );
            }

            send_json_response(true, ['id' => $inventory_id], 'Inventory item added successfully.');
        } else {
            error_log("Error executing statement: " . mysqli_stmt_error($stmt));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'Error adding inventory item: ' . mysqli_error($conn));
        }
        break;

    case 'uploadInventoryFiles':
        $result = handle_inventory_upload($conn, $_POST['ids'] ?? [], $_FILES['files'] ?? []);

        // Log the file upload
        if ($result['success'] && isset($result['uploaded_count']) && $result['uploaded_count'] > 0) {
            if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                $ids = $_POST['ids'] ?? [];
                $ids_str = is_array($ids) ? implode(', ', $ids) : $ids;
                add_log_entry(
                    $_SESSION['user_id'],
                    $_SESSION['username'],
                    "Uploaded inventory files",
                    "upload",
                    null,
                    "inventory_files",
                    "Uploaded {$result['uploaded_count']} files for inventory items: $ids_str"
                );
            }
        }

        send_json_response($result['success'], $result, $result['message']);
        break;

    case 'getInventoryFiles':
        $ids = $_POST['ids'] ?? [];
        if (empty($ids) || !is_array($ids)) {
            send_json_response(false, [], 'No inventory items selected.');
        }

        $sanitized_ids = [];
        foreach ($ids as $id) {
            if ($validated_id = filter_var($id, FILTER_VALIDATE_INT)) {
                $sanitized_ids[] = $validated_id;
            }
        }

        if (empty($sanitized_ids)) {
            send_json_response(false, [], 'Invalid inventory item IDs.');
        }

        // Check if inventory_files table exists
        $table_check_sql = "SHOW TABLES LIKE 'inventory_files'";
        $table_check_result = mysqli_query($conn, $table_check_sql);

        if (!$table_check_result || mysqli_num_rows($table_check_result) == 0) {
            // Table doesn't exist, create it
            $create_table_sql = "CREATE TABLE IF NOT EXISTS inventory_files (
                file_id INT(11) NOT NULL AUTO_INCREMENT,
                inventory_id INT(11) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                filepath VARCHAR(512) NOT NULL,
                filesize INT(11) NOT NULL,
                filetype VARCHAR(100) NOT NULL,
                uploaded_at DATETIME NOT NULL,
                uploaded_by INT(11) DEFAULT NULL,
                PRIMARY KEY (file_id),
                KEY inventory_id (inventory_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            if (!mysqli_query($conn, $create_table_sql)) {
                error_log("Get Inventory Files: Failed to create inventory_files table: " . mysqli_error($conn));
                send_json_response(false, [], 'Server error: Failed to create file storage table.');
            }
        }

        $placeholders = implode(',', array_fill(0, count($sanitized_ids), '?'));
        $sql = "SELECT f.file_id, f.inventory_id, f.original_filename, f.filepath, f.filesize, f.filetype, f.uploaded_at, i.item AS parent_item_name
                FROM inventory_files f
                JOIN inventory i ON f.inventory_id = i.id
                WHERE f.inventory_id IN ($placeholders)
                ORDER BY i.item, f.original_filename";

        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Get Inventory Files Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB Error preparing file list.');
        }

        $types = str_repeat('i', count($sanitized_ids));
        mysqli_stmt_bind_param($stmt, $types, ...$sanitized_ids);

        if (!mysqli_stmt_execute($stmt)) {
            error_log("Get Inventory Files Execute Error: " . mysqli_stmt_error($stmt));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'Error retrieving inventory files.');
        }

        $result = mysqli_stmt_get_result($stmt);
        $files = [];
        $grouped_files = [];
        $total_files = 0;

        while ($file = mysqli_fetch_assoc($result)) {
            $file['formatted_filesize'] = formatBytes($file['filesize']);
            $file['formatted_uploaded_at'] = date('M d, Y g:i A', strtotime($file['uploaded_at']));
            $parent_name = $file['parent_item_name'] ?? ('Inventory Item #' . $file['inventory_id']);

            if (!isset($grouped_files[$parent_name])) {
                $grouped_files[$parent_name] = [];
            }

            $grouped_files[$parent_name][] = $file;
            $files[] = $file;
            $total_files++;
        }

        mysqli_stmt_close($stmt);

        send_json_response(true, [
            'groupedFiles' => $grouped_files,
            'totalFiles' => $total_files,
            'parentItemCount' => count($sanitized_ids)
        ]);
        break;

    case 'downloadInventoryFile':
        $file_id = filter_input(INPUT_GET, 'file_id', FILTER_VALIDATE_INT);
        if (!$file_id) {
            send_json_response(false, [], 'Invalid or missing file ID.');
        }

        $sql = "SELECT original_filename, filepath, filesize, filetype FROM inventory_files WHERE file_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        if (!$stmt) {
            error_log("Download Inventory File Prepare Error: " . mysqli_error($conn));
            send_json_response(false, [], 'DB Error preparing download.');
        }

        mysqli_stmt_bind_param($stmt, "i", $file_id);

        if (!mysqli_stmt_execute($stmt)) {
            error_log("Download Inventory File Execute Error: " . mysqli_stmt_error($stmt));
            mysqli_stmt_close($stmt);
            send_json_response(false, [], 'Error retrieving file information.');
        }

        $result = mysqli_stmt_get_result($stmt);
        $file = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);

        if (!$file) {
            send_json_response(false, [], 'File not found.');
        }

        $filepath = $file['filepath'];
        if (!file_exists($filepath)) {
            error_log("Download Inventory File: File not found at path: " . $filepath);
            send_json_response(false, [], 'File not found on server.');
        }

        // Clean output buffer
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Set headers for download
        header('Content-Description: File Transfer');
        header('Content-Type: ' . $file['filetype']);
        header('Content-Disposition: attachment; filename="' . $file['original_filename'] . '"');
        header('Content-Length: ' . $file['filesize']);
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Expires: 0');

        // Output file
        readfile($filepath);
        exit;

    case 'getStrategyTable':
        // Simple test endpoint for debugging AJAX calls
        try {
            // Return a simple success response
            send_json_response(true, ['message' => 'AJAX connection working properly']);
        } catch (Exception $e) {
            error_log("Get Strategy Table Exception: " . $e->getMessage());
            send_json_response(false, [], 'Error in test endpoint: ' . $e->getMessage());
        }
        break;

    case 'getFW4AData':
        // Fetch FW4A data for the Reports tab
        try {
            // Get all locations with their strategies
            $locations_sql = "SELECT id, locality, barangay, district, locations, type, code, strategy, status, reason, remarks
                             FROM tblfwfa
                             ORDER BY locality ASC, barangay ASC";

            $locations_stmt = mysqli_prepare($conn, $locations_sql);
            if (!$locations_stmt) {
                error_log("Get FW4A Data Prepare Error: " . mysqli_error($conn));
                send_json_response(false, [], 'DB error preparing locations query.');
            }

            mysqli_stmt_execute($locations_stmt);
            $locations_result = mysqli_stmt_get_result($locations_stmt);

            if (!$locations_result) {
                $err = mysqli_stmt_error($locations_stmt);
                mysqli_stmt_close($locations_stmt);
                error_log("Get FW4A Data Result Error: " . $err);
                send_json_response(false, [], 'Error retrieving FW4A locations data.');
            }

            // Fetch all locations
            $locations = [];
            while ($row = mysqli_fetch_assoc($locations_result)) {
                $locations[] = $row;
            }
            mysqli_stmt_close($locations_stmt);

            // Get distinct strategies
            $strategies_sql = "SELECT DISTINCT strategy FROM tblfwfa WHERE strategy IS NOT NULL AND strategy != '' ORDER BY strategy ASC";
            $strategies_stmt = mysqli_prepare($conn, $strategies_sql);

            if (!$strategies_stmt) {
                error_log("Get FW4A Strategies Prepare Error: " . mysqli_error($conn));
                send_json_response(false, [], 'DB error preparing strategies query.');
            }

            mysqli_stmt_execute($strategies_stmt);
            $strategies_result = mysqli_stmt_get_result($strategies_stmt);

            if (!$strategies_result) {
                error_log("Get FW4A Strategies Result Error: " . mysqli_stmt_error($strategies_stmt));
                send_json_response(false, [], 'Error retrieving FW4A strategies data.');
            }

            // Fetch all strategies
            $strategies = [];
            while ($row = mysqli_fetch_assoc($strategies_result)) {
                $strategies[] = $row['strategy'];
            }
            mysqli_stmt_close($strategies_stmt);

            // Get strategy counts
            $strategy_counts_sql = "SELECT strategy, COUNT(*) as count FROM tblfwfa WHERE strategy IS NOT NULL AND strategy != '' GROUP BY strategy ORDER BY strategy ASC";
            $strategy_counts_stmt = mysqli_prepare($conn, $strategy_counts_sql);

            if (!$strategy_counts_stmt) {
                error_log("Get FW4A Strategy Counts Prepare Error: " . mysqli_error($conn));
                send_json_response(false, [], 'DB error preparing strategy counts query.');
            }

            mysqli_stmt_execute($strategy_counts_stmt);
            $strategy_counts_result = mysqli_stmt_get_result($strategy_counts_stmt);

            if (!$strategy_counts_result) {
                error_log("Get FW4A Strategy Counts Result Error: " . mysqli_stmt_error($strategy_counts_stmt));
                send_json_response(false, [], 'Error retrieving FW4A strategy counts data.');
            }

            // Fetch strategy counts
            $strategy_counts = [];
            while ($row = mysqli_fetch_assoc($strategy_counts_result)) {
                $strategy_counts[] = [
                    'strategy' => $row['strategy'],
                    'count' => (int)$row['count']
                ];
            }
            mysqli_stmt_close($strategy_counts_stmt);

            // Get total counts and status counts
            $total_count_sql = "SELECT COUNT(*) as total,
                               SUM(CASE WHEN status = 'Active' THEN 1 ELSE 0 END) as active,
                               SUM(CASE WHEN status != 'Active' THEN 1 ELSE 0 END) as inactive
                               FROM tblfwfa";

            $total_count_stmt = mysqli_prepare($conn, $total_count_sql);
            if (!$total_count_stmt) {
                error_log("Get FW4A Total Count Prepare Error: " . mysqli_error($conn));
                send_json_response(false, [], 'DB error preparing total count query.');
            }

            mysqli_stmt_execute($total_count_stmt);
            $total_count_result = mysqli_stmt_get_result($total_count_stmt);

            if (!$total_count_result) {
                error_log("Get FW4A Total Count Result Error: " . mysqli_stmt_error($total_count_stmt));
                send_json_response(false, [], 'Error retrieving FW4A total count data.');
            }

            $counts = mysqli_fetch_assoc($total_count_result);
            mysqli_stmt_close($total_count_stmt);

            // Prepare response data
            $response_data = [
                'locations' => $locations,
                'strategies' => $strategies,
                'strategy_counts' => $strategy_counts,
                'total_count' => (int)($counts['total'] ?? 0),
                'active_count' => (int)($counts['active'] ?? 0),
                'inactive_count' => (int)($counts['inactive'] ?? 0)
            ];

            send_json_response(true, $response_data);

        } catch (Exception $e) {
            error_log("Get FW4A Data Exception: " . $e->getMessage());
            send_json_response(false, [], 'Unexpected error retrieving FW4A data: ' . $e->getMessage());
        }
        break;

    default:
        send_json_response(false, [], 'Invalid action specified.');
}

// Final connection check/close (only if not closed by send_json_response or downloads)
if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
    mysqli_close($conn);
}
?>