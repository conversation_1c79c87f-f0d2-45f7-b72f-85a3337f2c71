<?php
// --- PHP Code Block ---
// Start the session at the beginning of the file
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php'); // Redirect to login page if not logged in
    exit;
}

require_once 'config/database.php'; // Ensure this path is correct
require_once 'access_control.php'; // Include access control functions

// Check if user has full access to this page
$hasFullAccess = hasFullAccess('FreeWifi4All');

// **** START FW4A FILTER ****
$project_filter_value = 'FW4A'; // Define the filter value for BOTH activities and participants
$target_category_filter = 'FW4A'; // Filter targets by this category
// **** END FW4A FILTER ****

// --- Determine Active Tab ---
$active_tab = isset($_GET['tab']) && in_array($_GET['tab'], ['activities', 'participants', 'letters', 'reports']) ? $_GET['tab'] : 'activities'; // Default to activities

// --- Determine Selected Report Year ---
$selected_report_year = isset($_GET['year']) ? filter_var($_GET['year'], FILTER_VALIDATE_INT) : date('Y'); // Get year from URL or default to current


// Check database connection
if (!$conn) {
    error_log("Database connection failed in freewifi4all.php: " . mysqli_connect_error());
    $db_connection_error = "Error connecting to the database. Please try again later or contact support.";
} else {
    $db_connection_error = null;
     // Set charset for the connection
    mysqli_set_charset($conn, "utf8mb4");
}

// --- Fetch list of distinct years from targets AND activities for dropdown ---
$available_years = [];
if (!$db_connection_error) {
    // Years from Targets
    $target_year_sql = "SELECT DISTINCT year FROM targets WHERE category = ? ORDER BY year DESC";
    $stmt_target_years = mysqli_prepare($conn, $target_year_sql);
    if ($stmt_target_years) {
        mysqli_stmt_bind_param($stmt_target_years, "s", $target_category_filter);
        if (mysqli_stmt_execute($stmt_target_years)) {
            $result_target_years = mysqli_stmt_get_result($stmt_target_years);
            while ($row = mysqli_fetch_assoc($result_target_years)) {
                $available_years[] = (int)$row['year'];
            }
        } else { error_log("Error fetching target years: " . mysqli_stmt_error($stmt_target_years)); }
        mysqli_stmt_close($stmt_target_years);
    } else { error_log("Error preparing target years query: " . mysqli_error($conn)); }

    // Years from Activities (where start date is not null)
    $activity_year_sql = "SELECT DISTINCT YEAR(start) as activity_year FROM tblactivity WHERE project = ? AND start IS NOT NULL ORDER BY activity_year DESC";
    $stmt_activity_years = mysqli_prepare($conn, $activity_year_sql);
     if ($stmt_activity_years) {
        mysqli_stmt_bind_param($stmt_activity_years, "s", $project_filter_value);
         if (mysqli_stmt_execute($stmt_activity_years)) {
             $result_activity_years = mysqli_stmt_get_result($stmt_activity_years);
             while ($row = mysqli_fetch_assoc($result_activity_years)) {
                 $available_years[] = (int)$row['activity_year'];
             }
         } else { error_log("Error fetching activity years: " . mysqli_stmt_error($stmt_activity_years)); }
         mysqli_stmt_close($stmt_activity_years);
     } else { error_log("Error preparing activity years query: " . mysqli_error($conn)); }

    // Add current year if missing and ensure defaults exist if query fails
    $current_actual_year = (int)date('Y');
    $available_years[] = $current_actual_year; // Ensure current year is always an option
    $available_years = array_unique($available_years); // Remove duplicates
    rsort($available_years); // Sort descending

    if (empty($available_years)) {
       $available_years = [(int)date('Y'), (int)date('Y')-1]; // Fallback if absolutely no data
    }

} else {
     $available_years = [(int)date('Y'), (int)date('Y')-1]; // Fallback on DB error
}

// Validate selected year or default
if (!in_array($selected_report_year, $available_years)) {
    $selected_report_year = !empty($available_years) ? $available_years[0] : date('Y'); // Default to the latest available year or current year
}


// --- View Parameter (always 'month' now for Stats) ---
$current_view = 'month';

// --- Date Calculations (Only needed if view is 'month' for Stats) ---
$current_month_start = null; $current_month_end = null; $prev_month_start = null; $prev_month_end = null;
if ($current_view === 'month') {
    $current_month_start = date('Y-m-01 00:00:00');
    $current_month_end = date('Y-m-t 23:59:59');
    $prev_month_timestamp = strtotime('-1 month', strtotime($current_month_start));
    $prev_month_start = date('Y-m-01 00:00:00', $prev_month_timestamp);
    $prev_month_end = date('Y-m-t 23:59:59', $prev_month_timestamp);
}

// --- Helper Function for Calculating Change ---
function calculateChange($current, $previous) {
     $diff = $current - $previous; $percentage = 0; $direction = 'no_change'; $icon_class = 'fa-minus'; $color_class = 'text-neutral';
    if ($previous > 0) { $percentage = round(($diff / $previous) * 100); }
    elseif ($current > 0 && $previous == 0) { $percentage = 100; $direction = 'increase'; }
    $formatted_diff = number_format(abs($diff)); $current_month = date('F'); $prev_month_name = date('F', strtotime('-1 month'));
    if ($diff > 0) { $direction = 'increase'; $icon_class = 'fa-arrow-up'; $color_class = 'text-green'; $text = "{$formatted_diff} ({$percentage}%) more in {$current_month} vs {$prev_month_name}"; }
    elseif ($diff < 0) { $direction = 'decrease'; $icon_class = 'fa-arrow-down'; $color_class = 'text-red'; $text = "{$formatted_diff} (" . abs($percentage) . "%) less in {$current_month} vs {$prev_month_name}"; }
    else { if ($current == 0 && $previous == 0) { $text = "No activity in {$current_month} or {$prev_month_name}"; } else { $text = "No change in {$current_month} vs {$prev_month_name}"; } }
    return [ 'difference' => $diff, 'percentage' => $percentage, 'direction' => $direction, 'icon_class' => $icon_class, 'color_class' => $color_class, 'text' => $text ];
}

// --- Fetch Statistics Data (Filtered for FW4A Activities) ---
$stats_data = []; $stats_data_current_month = []; $stats_data_previous = []; $stats = []; $stat_queries_ok = true;
if (!$db_connection_error) {
    // Keep the existing fetchStatValue function definition here
    function fetchStatValue($conn, $sql, $params = [], $types = '') {
        $value = 0; $stmt = mysqli_prepare($conn, $sql);
        if ($stmt) { if (!empty($params) && !empty($types)) { if (count($params) !== strlen($types)) { error_log("Stat Fetch Mismatch Error: P".count($params)."!=T".strlen($types)."|SQL:".$sql); mysqli_stmt_close($stmt); $GLOBALS['stat_queries_ok'] = false; return 0; } mysqli_stmt_bind_param($stmt, $types, ...$params); }
            if (mysqli_stmt_execute($stmt)) { $result = mysqli_stmt_get_result($stmt); if ($result) { $row = mysqli_fetch_assoc($result); $value = $row['total'] ?? 0; mysqli_free_result($result); } else { error_log("Stat Fetch Result Error: ".mysqli_stmt_error($stmt)."|SQL:".$sql); $GLOBALS['stat_queries_ok'] = false; } }
            else { error_log("Stat Execute Error: ".mysqli_stmt_error($stmt)."|SQL:".$sql); $GLOBALS['stat_queries_ok'] = false; } mysqli_stmt_close($stmt);
        } else { error_log("Stat Prepare Error: ".mysqli_error($conn)."|SQL:".$sql); $GLOBALS['stat_queries_ok'] = false; } return is_numeric($value) ? (int)$value : 0;
    }
    $project_filter_where = " WHERE project = ?"; $project_filter_and = " AND project = ?";
    $count_sql_monthly = "SELECT COUNT(*) AS total FROM tblactivity WHERE start >= ? AND start <= ?" . $project_filter_and;
    $count_distinct_sql_monthly = "SELECT COUNT(DISTINCT %s) AS total FROM tblactivity WHERE %s IS NOT NULL AND %s != '' AND start >= ? AND start <= ?" . $project_filter_and;
    $sum_sql_monthly = "SELECT SUM(%s) AS total FROM tblactivity WHERE start >= ? AND start <= ?" . $project_filter_and;
    $count_district_sql_monthly = "SELECT COUNT(*) AS total FROM tblactivity WHERE district = ? AND start >= ? AND start <= ?" . $project_filter_and;
    $types_ss_project = 'sss'; $types_sss_project = 'ssss'; $count_sql_all = "SELECT COUNT(*) AS total FROM tblactivity" . $project_filter_where;
    $count_distinct_sql_all = "SELECT COUNT(DISTINCT %s) AS total FROM tblactivity WHERE %s IS NOT NULL AND %s != ''" . $project_filter_and;
    $sum_sql_all = "SELECT SUM(%s) AS total FROM tblactivity" . $project_filter_where;
    $count_district_sql_all = "SELECT COUNT(*) AS total FROM tblactivity WHERE district = ?" . $project_filter_and;
    $types_s_project = 's'; $types_ss_project_dist = 'ss'; $district1_name = 'District 1 (Siargao Island)'; $district2_name = 'District 2 (Mainland)';
    $params_project = [$project_filter_value];
    $stats_data['activities'] = fetchStatValue($conn, $count_sql_all, $params_project, $types_s_project);
    $stats_data['participants'] = fetchStatValue($conn, sprintf($sum_sql_all, 'participants'), $params_project, $types_s_project);
    $stats_data['sectors'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'sector', 'sector', 'sector'), $params_project, $types_s_project);
    $stats_data['agencies'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'agency', 'agency', 'agency'), $params_project, $types_s_project);
    $stats_data['municipalities'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'municipality', 'municipality', 'municipality'), $params_project, $types_s_project);
    $stats_data['barangays'] = fetchStatValue($conn, sprintf($count_distinct_sql_all, 'barangay', 'barangay', 'barangay'), $params_project, $types_s_project);
    $stats_data['district1'] = fetchStatValue($conn, $count_district_sql_all, [$district1_name, $project_filter_value], $types_ss_project_dist);
    $stats_data['district2'] = fetchStatValue($conn, $count_district_sql_all, [$district2_name, $project_filter_value], $types_ss_project_dist);
    $date_params_current = [$current_month_start, $current_month_end]; $params_current_project = [...$date_params_current, $project_filter_value];
    $stats_data_current_month['activities'] = fetchStatValue($conn, $count_sql_monthly, $params_current_project, $types_ss_project);
    $stats_data_current_month['participants'] = fetchStatValue($conn, sprintf($sum_sql_monthly, 'participants'), $params_current_project, $types_ss_project);
    $stats_data_current_month['sectors'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'sector', 'sector', 'sector'), $params_current_project, $types_ss_project);
    $stats_data_current_month['agencies'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'agency', 'agency', 'agency'), $params_current_project, $types_ss_project);
    $stats_data_current_month['municipalities'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'municipality', 'municipality', 'municipality'), $params_current_project, $types_ss_project);
    $stats_data_current_month['barangays'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'barangay', 'barangay', 'barangay'), $params_current_project, $types_ss_project);
    $stats_data_current_month['district1'] = fetchStatValue($conn, $count_district_sql_monthly, [$district1_name, ...$date_params_current, $project_filter_value], $types_sss_project);
    $stats_data_current_month['district2'] = fetchStatValue($conn, $count_district_sql_monthly, [$district2_name, ...$date_params_current, $project_filter_value], $types_sss_project);
    $date_params_previous = [$prev_month_start, $prev_month_end]; $params_previous_project = [...$date_params_previous, $project_filter_value];
    $stats_data_previous['activities'] = fetchStatValue($conn, $count_sql_monthly, $params_previous_project, $types_ss_project);
    $stats_data_previous['participants'] = fetchStatValue($conn, sprintf($sum_sql_monthly, 'participants'), $params_previous_project, $types_ss_project);
    $stats_data_previous['sectors'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'sector', 'sector', 'sector'), $params_previous_project, $types_ss_project);
    $stats_data_previous['agencies'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'agency', 'agency', 'agency'), $params_previous_project, $types_ss_project);
    $stats_data_previous['municipalities'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'municipality', 'municipality', 'municipality'), $params_previous_project, $types_ss_project);
    $stats_data_previous['barangays'] = fetchStatValue($conn, sprintf($count_distinct_sql_monthly, 'barangay', 'barangay', 'barangay'), $params_previous_project, $types_ss_project);
    $stats_data_previous['district1'] = fetchStatValue($conn, $count_district_sql_monthly, [$district1_name, ...$date_params_previous, $project_filter_value], $types_sss_project);
    $stats_data_previous['district2'] = fetchStatValue($conn, $count_district_sql_monthly, [$district2_name, ...$date_params_previous, $project_filter_value], $types_sss_project);
}
if ($stat_queries_ok) {
    // Prepare $stats array with keys and all data needed for the modal
    $change_activities = calculateChange($stats_data_current_month['activities'], $stats_data_previous['activities']);
    $stats[] = [
        'key' => 'activities', 'icon' => 'fas fa-tasks', 'title' => 'Total Activities',
        'value' => number_format($stats_data['activities']),
        'current_month_value' => number_format($stats_data_current_month['activities']),
        'prev_month_value' => number_format($stats_data_previous['activities']),
        'insight' => $change_activities
    ];
    $change_participants = calculateChange($stats_data_current_month['participants'], $stats_data_previous['participants']);
    $stats[] = [
        'key' => 'participants', 'icon' => 'fas fa-users', 'title' => 'Total Participants',
        'value' => number_format($stats_data['participants']),
        'current_month_value' => number_format($stats_data_current_month['participants']),
        'prev_month_value' => number_format($stats_data_previous['participants']),
        'insight' => $change_participants
    ];
     $change_sectors = calculateChange($stats_data_current_month['sectors'], $stats_data_previous['sectors']);
    $stats[] = [
        'key' => 'sectors', 'icon' => 'fas fa-tags', 'title' => 'Total Unique Sectors',
        'value' => number_format($stats_data['sectors']),
        'current_month_value' => number_format($stats_data_current_month['sectors']),
        'prev_month_value' => number_format($stats_data_previous['sectors']),
        'insight' => $change_sectors
    ];
    $change_agencies = calculateChange($stats_data_current_month['agencies'], $stats_data_previous['agencies']);
    $stats[] = [
        'key' => 'agencies', 'icon' => 'fas fa-building', 'title' => 'Total Unique Agencies',
        'value' => number_format($stats_data['agencies']),
        'current_month_value' => number_format($stats_data_current_month['agencies']),
        'prev_month_value' => number_format($stats_data_previous['agencies']),
        'insight' => $change_agencies
    ];
    $change_municipalities = calculateChange($stats_data_current_month['municipalities'], $stats_data_previous['municipalities']);
    $stats[] = [
        'key' => 'municipalities', 'icon' => 'fas fa-map-marker-alt', 'title' => 'Total Unique Municipalities',
        'value' => number_format($stats_data['municipalities']),
        'current_month_value' => number_format($stats_data_current_month['municipalities']),
        'prev_month_value' => number_format($stats_data_previous['municipalities']),
        'insight' => $change_municipalities
    ];
    $change_barangays = calculateChange($stats_data_current_month['barangays'], $stats_data_previous['barangays']);
    $stats[] = [
        'key' => 'barangays', 'icon' => 'fas fa-map-pin', 'title' => 'Total Unique Barangays',
        'value' => number_format($stats_data['barangays']),
        'current_month_value' => number_format($stats_data_current_month['barangays']),
        'prev_month_value' => number_format($stats_data_previous['barangays']),
        'insight' => $change_barangays
    ];
    $change_district1 = calculateChange($stats_data_current_month['district1'], $stats_data_previous['district1']);
    $stats[] = [
        'key' => 'district1', 'icon' => 'fas fa-map', 'title' => 'Total District 1 Activities',
        'value' => number_format($stats_data['district1']),
        'current_month_value' => number_format($stats_data_current_month['district1']),
        'prev_month_value' => number_format($stats_data_previous['district1']),
        'insight' => $change_district1
    ];
    $change_district2 = calculateChange($stats_data_current_month['district2'], $stats_data_previous['district2']);
    $stats[] = [
        'key' => 'district2', 'icon' => 'fas fa-map-signs', 'title' => 'Total District 2 Activities',
        'value' => number_format($stats_data['district2']),
        'current_month_value' => number_format($stats_data_current_month['district2']),
        'prev_month_value' => number_format($stats_data_previous['district2']),
        'insight' => $change_district2
    ];
} else {
     $stats[] = [
        'key' => 'error', 'icon' => 'fas fa-exclamation-circle', 'title' => 'Stats Error',
        'value' => 'N/A', 'current_month_value' => 'N/A', 'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Could not load statistics.', 'icon_class' => 'fa-exclamation-triangle', 'color_class' => 'text-red']
    ];
}

// === ACTIVITIES TAB DATA ===
$filter_columns_activity = ['year', 'indicator', 'sector', 'municipality', 'barangay']; $search_term_activity = isset($_GET['search']) ? trim($_GET['search']) : ''; $where_clauses_activity = ["project = ?"]; $query_params_activity = [$project_filter_value]; $query_param_types_activity = 's';
if (!empty($search_term_activity)) { $like_term_activity = '%' . $search_term_activity . '%'; $searchable_columns_activity = [ 'subproject', 'activity', 'indicator', 'training', 'municipality', 'district', 'barangay', 'agency', 'mode', 'sector', 'person', 'resource', 'remarks', 'mov' ]; $search_conditions_activity = []; foreach ($searchable_columns_activity as $column) { $search_conditions_activity[] = "`" . $column . "` LIKE ?"; $query_params_activity[] = $like_term_activity; $query_param_types_activity .= 's'; } $where_clauses_activity[] = "(" . implode(" OR ", $search_conditions_activity) . ")"; }
foreach ($filter_columns_activity as $column) {
    $filter_value_activity = isset($_GET[$column]) ? trim($_GET[$column]) : '';
    if (!empty($filter_value_activity)) {
        if ($column === 'year') {
            $where_clauses_activity[] = "YEAR(`start`) = ?";
        } else {
            $where_clauses_activity[] = "`$column` = ?";
        }
        $query_params_activity[] = $filter_value_activity;
        $query_param_types_activity .= 's';
    }
}
$sql_where_clause_activity = !empty($where_clauses_activity) ? " WHERE " . implode(" AND ", $where_clauses_activity) : "";
$results_per_page_options = [5, 10, 20, 50, 100]; $default_results_per_page = 10; $current_page_activity = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1; $results_per_page_activity = isset($_GET['limit']) ? (int)$_GET['limit'] : $default_results_per_page; if (!in_array($results_per_page_activity, $results_per_page_options)) { $results_per_page_activity = $default_results_per_page; }
// Update URL params to include report year
$url_params_array_activity = ['tab' => 'activities'];
if (!empty($search_term_activity)) $url_params_array_activity['search'] = $search_term_activity;
if ($results_per_page_activity !== $default_results_per_page) $url_params_array_activity['limit'] = $results_per_page_activity;
foreach ($filter_columns_activity as $column) { if (isset($_GET[$column]) && trim($_GET[$column]) !== '') { $url_params_array_activity[$column] = trim($_GET[$column]); } }
$url_params_array_activity['p_search'] = $_GET['p_search'] ?? '';
$url_params_array_activity['p_limit'] = $_GET['p_limit'] ?? $default_results_per_page;
$url_params_array_activity['p_page'] = $_GET['p_page'] ?? 1;
$url_params_array_activity['year'] = $selected_report_year; // Add report year
$url_params_activity = '&' . http_build_query(array_filter($url_params_array_activity));

$total_activities = 0; $total_pages_activity = 0; $activities_result = false; $activities_fetch_error = null; $offset_activity = 0;
if (!$db_connection_error) { $total_activities_query = "SELECT COUNT(*) as total FROM tblactivity" . $sql_where_clause_activity; $total_activities = fetchStatValue($conn, $total_activities_query, $query_params_activity, $query_param_types_activity);
    if ($total_activities > 0) { $total_pages_activity = ceil($total_activities / $results_per_page_activity); if ($current_page_activity > $total_pages_activity) $current_page_activity = $total_pages_activity; if ($current_page_activity < 1) $current_page_activity = 1; $offset_activity = ($current_page_activity - 1) * $results_per_page_activity; $activities_sql = "SELECT id, start, end, project, subproject, activity, indicator, training, municipality, district, barangay, agency, mode, sector, person, resource, participants, completers, male, female, approved, mov, remarks FROM tblactivity" . $sql_where_clause_activity . " ORDER BY start DESC, id DESC LIMIT ? OFFSET ?"; $all_bind_params_activity = [...$query_params_activity, $results_per_page_activity, $offset_activity]; $combined_param_types_activity = $query_param_types_activity . 'ii'; $stmt_activities = mysqli_prepare($conn, $activities_sql);
        if ($stmt_activities) { if (count($all_bind_params_activity) !== strlen($combined_param_types_activity)) { $activities_fetch_error = "Error setting up activity data filter."; } else { mysqli_stmt_bind_param($stmt_activities, $combined_param_types_activity, ...$all_bind_params_activity); if (!mysqli_stmt_execute($stmt_activities)) { $activities_fetch_error = "Error fetching activity data."; } else { $activities_result = mysqli_stmt_get_result($stmt_activities); } } mysqli_stmt_close($stmt_activities); }
        else { $activities_fetch_error = "Error preparing to fetch activities."; }
    } else { $total_pages_activity = 1; if (empty($activities_fetch_error)) { if (!empty($search_term_activity) || count(array_filter(array_intersect_key($_GET, array_flip($filter_columns_activity)))) > 0 ) { $activities_fetch_error = "No FW4A activities found matching the search/filter criteria."; } else { $activities_fetch_error = "No FW4A activities found."; } } } // <-- Changed Text
} else { $activities_fetch_error = "Cannot fetch activities due to database connection error."; }
$table_colspan_activity = 24;

// === PARTICIPANTS TAB DATA ===
$participant_stats = []; $participant_stats_data = []; $participant_stat_queries_ok = true;
if (!$db_connection_error) { /* Fetch FW4A monitoring stats */
    // Get total number of municipalities in Surigao del Norte
    $total_municipalities_sql = "SELECT COUNT(DISTINCT municipality) AS total FROM tblbrgy";
    $total_municipalities = fetchStatValue($conn, $total_municipalities_sql, [], '');

    // Get total number of barangays in Surigao del Norte
    $total_barangays_sql = "SELECT COUNT(*) AS total FROM tblbrgy";
    $total_barangays = fetchStatValue($conn, $total_barangays_sql, [], '');

    // Get number of municipalities with access points
    $municipalities_with_ap_sql = "SELECT COUNT(DISTINCT locality) AS total FROM tblfwfa";
    $municipalities_with_ap = fetchStatValue($conn, $municipalities_with_ap_sql, [], '');

    // Get number of barangays with access points
    $barangays_with_ap_sql = "SELECT COUNT(DISTINCT CONCAT(locality, ' ', barangay)) AS total FROM tblfwfa WHERE barangay IS NOT NULL AND barangay != ''";
    $barangays_with_ap = fetchStatValue($conn, $barangays_with_ap_sql, [], '');

    // Get number of active access points
    $active_ap_sql = "SELECT COUNT(*) AS total FROM tblfwfa WHERE status = 'Active'";
    $active_ap = fetchStatValue($conn, $active_ap_sql, [], '');

    // Get number of inactive access points
    $inactive_ap_sql = "SELECT COUNT(*) AS total FROM tblfwfa WHERE status = 'Inactive'";
    $inactive_ap = fetchStatValue($conn, $inactive_ap_sql, [], '');

    // Calculate penetration rates
    $lgu_penetration_rate = ($total_municipalities > 0) ? ($municipalities_with_ap / $total_municipalities) * 100 : 0;
    $barangay_penetration_rate = ($total_barangays > 0) ? ($barangays_with_ap / $total_barangays) * 100 : 0;

    // Store the calculated values
    $participant_stats_data['lgu_penetration'] = $lgu_penetration_rate;
    $participant_stats_data['barangay_penetration'] = $barangay_penetration_rate;
    $participant_stats_data['active_ap'] = $active_ap;
    $participant_stats_data['inactive_ap'] = $inactive_ap;
}
if ($participant_stat_queries_ok) {
     // LGU Penetration Rate
     $participant_stats[] = [
         'key' => 'lgu_penetration',
         'icon' => 'fas fa-building',
         'title' => 'LGU PENETRATION RATE',
         'value' => number_format($participant_stats_data['lgu_penetration'], 2) . '%',
         'current_month_value' => 'N/A',
         'prev_month_value' => 'N/A',
         'insight' => ['text' => 'Current penetration rate', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-primary']
     ];

     // Barangay Penetration Rate
     $participant_stats[] = [
         'key' => 'barangay_penetration',
         'icon' => 'fas fa-map-marker-alt',
         'title' => 'BARANGAY PENETRATION RATE',
         'value' => number_format($participant_stats_data['barangay_penetration'], 2) . '%',
         'current_month_value' => 'N/A',
         'prev_month_value' => 'N/A',
         'insight' => ['text' => 'Current penetration rate', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-primary']
     ];

     // Active Access Points
     $participant_stats[] = [
         'key' => 'active_ap',
         'icon' => 'fas fa-wifi',
         'title' => 'ACTIVE ACCESS POINTS',
         'value' => number_format($participant_stats_data['active_ap']),
         'current_month_value' => 'N/A',
         'prev_month_value' => 'N/A',
         'insight' => ['text' => 'Total active access points', 'icon_class' => 'fa-check-circle', 'color_class' => 'text-success']
     ];

     // Inactive Access Points
     $participant_stats[] = [
         'key' => 'inactive_ap',
         'icon' => 'fas fa-wifi',
         'title' => 'INACTIVE ACCESS POINTS',
         'value' => number_format($participant_stats_data['inactive_ap']),
         'current_month_value' => 'N/A',
         'prev_month_value' => 'N/A',
         'insight' => ['text' => 'Total inactive access points', 'icon_class' => 'fa-times-circle', 'color_class' => 'text-danger']
     ];
} else {
     $participant_stats[] = [
         'key' => 'p_error', 'icon' => 'fas fa-exclamation-circle', 'title' => 'Participant Stats Error',
         'value' => 'N/A', 'current_month_value' => 'N/A', 'prev_month_value' => 'N/A',
         'insight' => ['text' => 'Could not load participant stats.', 'icon_class' => 'fa-exclamation-triangle', 'color_class' => 'text-red']
     ];
 }

// *** START PARTICIPANT TABLE LOGIC ***
$search_term_participant = isset($_GET['p_search']) ? trim($_GET['p_search']) : '';
$current_page_participant = isset($_GET['p_page']) ? max(1, (int)$_GET['p_page']) : 1;
$results_per_page_participant = isset($_GET['p_limit']) ? (int)$_GET['p_limit'] : $default_results_per_page;
if (!in_array($results_per_page_participant, $results_per_page_options)) {
    $results_per_page_participant = $default_results_per_page;
}
$where_clauses_participant = ["1"];  // Changed from project filter to allow all records
$query_params_participant = [];
$query_param_types_participant = '';
if (!empty($search_term_participant)) {
    $like_term_participant = '%' . $search_term_participant . '%';
    $searchable_columns_participant = [ 'locality', 'barangay', 'district', 'locations', 'type', 'code', 'strategy', 'status', 'reason', 'remarks' ];
    $search_conditions_participant = [];
    foreach ($searchable_columns_participant as $column) { $search_conditions_participant[] = "`" . $column . "` LIKE ?"; $query_params_participant[] = $like_term_participant; $query_param_types_participant .= 's'; }
    $where_clauses_participant[] = "(" . implode(" OR ", $search_conditions_participant) . ")";
}
$filter_columns_participant = ['locality', 'type', 'status', 'strategy'];
foreach ($filter_columns_participant as $column) {
    $param_name = 'p_' . $column;
    $filter_value_participant = isset($_GET[$param_name]) ? trim($_GET[$param_name]) : '';
    if (!empty($filter_value_participant)) {
        $where_clauses_participant[] = "`$column` = ?";
        $query_params_participant[] = $filter_value_participant;
        $query_param_types_participant .= 's';
    }
}
$sql_where_clause_participant = !empty($where_clauses_participant) ? " WHERE " . implode(" AND ", $where_clauses_participant) : "";
// Update participant URL params to include report year
$url_params_array_participant = ['tab' => 'participants'];
if (!empty($search_term_participant)) $url_params_array_participant['p_search'] = $search_term_participant;
if ($results_per_page_participant !== $default_results_per_page) $url_params_array_participant['p_limit'] = $results_per_page_participant;
foreach ($filter_columns_participant as $column) {
    $param_name = 'p_' . $column;
    if (isset($_GET[$param_name]) && trim($_GET[$param_name]) !== '') { $url_params_array_participant[$param_name] = trim($_GET[$param_name]); }
}
$url_params_array_participant['search'] = $_GET['search'] ?? '';
$url_params_array_participant['limit'] = $_GET['limit'] ?? $default_results_per_page;
$url_params_array_participant['page'] = $_GET['page'] ?? 1;
$url_params_array_participant['year'] = $selected_report_year; // Add report year
foreach ($filter_columns_activity as $column) { if (isset($_GET[$column]) && trim($_GET[$column]) !== '') { $url_params_array_participant[$column] = trim($_GET[$column]); } }
$url_params_participant = '&' . http_build_query(array_filter($url_params_array_participant));

$total_participants = 0; $total_pages_participant = 0; $participants_result = false; $participants_fetch_error = null; $offset_participant = 0;
if (!$db_connection_error) {
    $total_participants_query = "SELECT COUNT(*) as total FROM tblfwfa" . $sql_where_clause_participant;
    $total_participants = fetchStatValue($conn, $total_participants_query, $query_params_participant, $query_param_types_participant);
    if ($total_participants > 0) {
        $total_pages_participant = ceil($total_participants / $results_per_page_participant);
        if ($current_page_participant > $total_pages_participant) $current_page_participant = $total_pages_participant;
        if ($current_page_participant < 1) $current_page_participant = 1;
        $offset_participant = ($current_page_participant - 1) * $results_per_page_participant;
        $participants_sql = "SELECT `id`, `locality`, `barangay`, `district`, `locations`, `type`, `code`, `strategy`, `status`, `reason`, `remarks` FROM tblfwfa" . $sql_where_clause_participant . " ORDER BY locality ASC LIMIT ? OFFSET ?";
        $all_bind_params_participant = [...$query_params_participant, $results_per_page_participant, $offset_participant];
        $combined_param_types_participant = $query_param_types_participant . 'ii';
        $stmt_participants = mysqli_prepare($conn, $participants_sql);
        if ($stmt_participants) {
            if (count($all_bind_params_participant) !== strlen($combined_param_types_participant)) { $participants_fetch_error = "Error setting up monitoring data filter: Param/Type count mismatch."; error_log("Monitoring Fetch Mismatch: P".count($all_bind_params_participant)."!=T".strlen($combined_param_types_participant)."|SQL:".$participants_sql); }
            else { mysqli_stmt_bind_param($stmt_participants, $combined_param_types_participant, ...$all_bind_params_participant); if (!mysqli_stmt_execute($stmt_participants)) { $participants_fetch_error = "Error fetching monitoring data: " . mysqli_stmt_error($stmt_participants); } else { $participants_result = mysqli_stmt_get_result($stmt_participants); } }
            mysqli_stmt_close($stmt_participants);
        } else { $participants_fetch_error = "Error preparing to fetch monitoring data: " . mysqli_error($conn); }
    } else { $total_pages_participant = 1; if (empty($participants_fetch_error)) { if (!empty($search_term_participant) || !empty(array_filter(array_intersect_key($_GET, array_flip(array_map(fn($c) => 'p_'.$c, $filter_columns_participant))))) ) { $participants_fetch_error = "No monitoring data found matching the search/filter criteria."; } else { $participants_fetch_error = "No monitoring data found."; } } }
} else { $participants_fetch_error = "Cannot fetch monitoring data due to database connection error."; }
$table_colspan_participant = 12; // Updated column count
// *** END PARTICIPANT TABLE LOGIC ***

// *** START LETTER REQUESTS STATISTICS ***
$letter_stats = []; $letter_stats_data = []; $letter_stat_queries_ok = true;

if (!$db_connection_error) {
    // Total Letter Requests
    $total_letters_sql = "SELECT COUNT(*) AS total FROM locationrequests";
    $letter_stats_data['total'] = fetchStatValue($conn, $total_letters_sql);

    // Count total unique localities/municipalities
    $total_localities_sql = "SELECT COUNT(DISTINCT locality) AS total FROM locationrequests WHERE locality IS NOT NULL AND locality != ''";
    $letter_stats_data['total_localities'] = fetchStatValue($conn, $total_localities_sql);

    // Count total unique barangays
    $total_barangays_sql = "SELECT COUNT(DISTINCT barangay) AS total FROM locationrequests WHERE barangay IS NOT NULL AND barangay != ''";
    $letter_stats_data['total_barangays'] = fetchStatValue($conn, $total_barangays_sql);

    // Count by type = 'Provision'
    $provision_letters_sql = "SELECT COUNT(*) AS total FROM locationrequests WHERE type = 'Provision'";
    $letter_stats_data['provisions'] = fetchStatValue($conn, $provision_letters_sql);

    // Count by type = 'Request'
    $request_letters_sql = "SELECT COUNT(*) AS total FROM locationrequests WHERE type = 'Request'";
    $letter_stats_data['requests'] = fetchStatValue($conn, $request_letters_sql);
}

if ($letter_stat_queries_ok) {
    // We're not showing the total letter requests as requested

    // Total Localities/Municipalities
    $letter_stats[] = [
        'key' => 'l_localities', 'icon' => 'fas fa-city', 'title' => 'Total Localities',
        'value' => number_format($letter_stats_data['total_localities']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Unique municipalities with requests', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-primary']
    ];

    // Total Barangays
    $letter_stats[] = [
        'key' => 'l_barangays', 'icon' => 'fas fa-map-marker-alt', 'title' => 'Total Barangays',
        'value' => number_format($letter_stats_data['total_barangays']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Unique barangays with requests', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-primary']
    ];

    // Provisions
    $letter_stats[] = [
        'key' => 'l_provisions', 'icon' => 'fas fa-wifi', 'title' => 'Provisions',
        'value' => number_format($letter_stats_data['provisions']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Letters with type "Provision"', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-success']
    ];

    // Requests
    $letter_stats[] = [
        'key' => 'l_requests', 'icon' => 'fas fa-file-alt', 'title' => 'Requests',
        'value' => number_format($letter_stats_data['requests']),
        'current_month_value' => 'N/A',
        'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Letters with type "Request"', 'icon_class' => 'fa-info-circle', 'color_class' => 'text-info']
    ];
} else {
    $letter_stats[] = [
        'key' => 'l_error', 'icon' => 'fas fa-exclamation-circle', 'title' => 'Letter Stats Error',
        'value' => 'N/A', 'current_month_value' => 'N/A', 'prev_month_value' => 'N/A',
        'insight' => ['text' => 'Could not load letter statistics.', 'icon_class' => 'fa-exclamation-triangle', 'color_class' => 'text-red']
    ];
}

// *** START LETTER REQUESTS TABLE LOGIC ***
$search_term_letter = isset($_GET['l_search']) ? trim($_GET['l_search']) : '';
$current_page_letter = isset($_GET['l_page']) ? max(1, (int)$_GET['l_page']) : 1;
$results_per_page_letter = isset($_GET['l_limit']) ? (int)$_GET['l_limit'] : $default_results_per_page;
if (!in_array($results_per_page_letter, $results_per_page_options)) {
    $results_per_page_letter = $default_results_per_page;
}
$where_clauses_letter = ["1"];  // Default where clause
$query_params_letter = [];
$query_param_types_letter = '';
if (!empty($search_term_letter)) {
    $like_term_letter = '%' . $search_term_letter . '%';
    $searchable_columns_letter = [ 'locality', 'barangay', 'district', 'location', 'date', 'year', 'type', 'status', 'accomplished', 'remarks' ];
    $search_conditions_letter = [];
    foreach ($searchable_columns_letter as $column) {
        $search_conditions_letter[] = "`" . $column . "` LIKE ?";
        $query_params_letter[] = $like_term_letter;
        $query_param_types_letter .= 's';
    }
    $where_clauses_letter[] = "(" . implode(" OR ", $search_conditions_letter) . ")";
}
$filter_columns_letter = ['locality', 'type', 'status', 'year'];
foreach ($filter_columns_letter as $column) {
    $param_name = 'l_' . $column;
    $filter_value_letter = isset($_GET[$param_name]) ? trim($_GET[$param_name]) : '';
    if (!empty($filter_value_letter)) {
        $where_clauses_letter[] = "`$column` = ?";
        $query_params_letter[] = $filter_value_letter;
        $query_param_types_letter .= 's';
    }
}
$sql_where_clause_letter = !empty($where_clauses_letter) ? " WHERE " . implode(" AND ", $where_clauses_letter) : "";
// Update letter URL params to include report year
$url_params_array_letter = ['tab' => 'letters'];
if (!empty($search_term_letter)) $url_params_array_letter['l_search'] = $search_term_letter;
if ($results_per_page_letter !== $default_results_per_page) $url_params_array_letter['l_limit'] = $results_per_page_letter;
foreach ($filter_columns_letter as $column) {
    $param_name = 'l_' . $column;
    if (isset($_GET[$param_name]) && trim($_GET[$param_name]) !== '') {
        $url_params_array_letter[$param_name] = trim($_GET[$param_name]);
    }
}
$url_params_array_letter['search'] = $_GET['search'] ?? '';
$url_params_array_letter['limit'] = $_GET['limit'] ?? $default_results_per_page;
$url_params_array_letter['page'] = $_GET['page'] ?? 1;
$url_params_array_letter['p_search'] = $_GET['p_search'] ?? '';
$url_params_array_letter['p_limit'] = $_GET['p_limit'] ?? $default_results_per_page;
$url_params_array_letter['p_page'] = $_GET['p_page'] ?? 1;
$url_params_array_letter['year'] = $selected_report_year; // Add report year
foreach ($filter_columns_activity as $column) {
    if (isset($_GET[$column]) && trim($_GET[$column]) !== '') {
        $url_params_array_letter[$column] = trim($_GET[$column]);
    }
}
foreach ($filter_columns_participant as $column) {
    if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== '') {
        $url_params_array_letter['p_'.$column] = trim($_GET['p_'.$column]);
    }
}
$url_params_letter = '&' . http_build_query(array_filter($url_params_array_letter));

$total_letters = 0;
$total_pages_letter = 0;
$letters_result = false;
$letters_fetch_error = null;
$offset_letter = 0;
if (!$db_connection_error) {
    $total_letters_query = "SELECT COUNT(*) as total FROM locationrequests" . $sql_where_clause_letter;
    $total_letters = fetchStatValue($conn, $total_letters_query, $query_params_letter, $query_param_types_letter);
    if ($total_letters > 0) {
        $total_pages_letter = ceil($total_letters / $results_per_page_letter);
        if ($current_page_letter > $total_pages_letter) $current_page_letter = $total_pages_letter;
        if ($current_page_letter < 1) $current_page_letter = 1;
        $offset_letter = ($current_page_letter - 1) * $results_per_page_letter;
        $letters_sql = "SELECT `id`, `locality`, `barangay`, `district`, `location`, `date`, `year`, `type`, `status`, `accomplished`, `remarks` FROM locationrequests" . $sql_where_clause_letter . " ORDER BY locality ASC LIMIT ? OFFSET ?";
        $all_bind_params_letter = [...$query_params_letter, $results_per_page_letter, $offset_letter];
        $combined_param_types_letter = $query_param_types_letter . 'ii';
        $stmt_letters = mysqli_prepare($conn, $letters_sql);
        if ($stmt_letters) {
            if (count($all_bind_params_letter) !== strlen($combined_param_types_letter)) {
                $letters_fetch_error = "Error setting up letter requests filter: Param/Type count mismatch.";
                error_log("Letter Requests Fetch Mismatch: P".count($all_bind_params_letter)."!=T".strlen($combined_param_types_letter)."|SQL:".$letters_sql);
            }
            else {
                mysqli_stmt_bind_param($stmt_letters, $combined_param_types_letter, ...$all_bind_params_letter);
                if (!mysqli_stmt_execute($stmt_letters)) {
                    $letters_fetch_error = "Error fetching letter requests data: " . mysqli_stmt_error($stmt_letters);
                } else {
                    $letters_result = mysqli_stmt_get_result($stmt_letters);
                }
            }
            mysqli_stmt_close($stmt_letters);
        } else {
            $letters_fetch_error = "Error preparing to fetch letter requests data: " . mysqli_error($conn);
        }
    } else {
        $total_pages_letter = 1;
        if (empty($letters_fetch_error)) {
            if (!empty($search_term_letter) || !empty(array_filter(array_intersect_key($_GET, array_flip(array_map(fn($c) => 'l_'.$c, $filter_columns_letter))))) ) {
                $letters_fetch_error = "No letter requests found matching the search/filter criteria.";
            } else {
                $letters_fetch_error = "No letter requests found.";
            }
        }
    }
} else {
    $letters_fetch_error = "Cannot fetch letter requests due to database connection error.";
}
$table_colspan_letter = 12; // Column count for letter requests table
// *** END LETTER REQUESTS TABLE LOGIC ***

// --- Helper Date Functions ---
function formatDateForDisplay($date_string) { if (empty($date_string) || $date_string === '0000-00-00' || $date_string === '0000-00-00 00:00:00') return ''; $timestamp = strtotime($date_string); return $timestamp ? date('M d, Y', $timestamp) : ''; }
function formatDateForInput($date_string) { if (empty($date_string) || $date_string === '0000-00-00' || $date_string === '0000-00-00 00:00:00') return ''; $timestamp = strtotime($date_string); return $timestamp ? date('Y-m-d', $timestamp) : ''; }

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Wifi for All Dashboard - Activity Monitoring</title> <!-- <-- Changed Title -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/filter-dropdown.css"> <!-- Filter dropdown styles -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>
    <!-- ADD Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* ----- Navigation Tabs Container ----- */
        .nav-tabs-container { margin-bottom: 20px; }
        .nav-tabs { display: flex; border-bottom: 1px solid #ddd; margin-bottom: 0; background-color: #f8f9fa; border-radius: 4px 4px 0 0; justify-content: space-between; flex-wrap: wrap; /* Allow wrapping on small screens */ }
        .nav-tabs-left { display: flex; flex-grow: 1; /* Allow left side to take space */ }
        .nav-tabs-right { display: flex; align-items: center; padding-right: 10px; gap: 8px; flex-shrink: 0; /* Prevent shrinking too much */ padding-top: 5px; padding-bottom: 5px; /* Add padding for wrapped items */ }
        .nav-tab { padding: 12px 20px; color: #495057; text-decoration: none; font-weight: 500; border-bottom: 3px solid transparent; transition: all 0.2s ease; cursor: pointer; white-space: nowrap; }
        .nav-tab:hover { color: var(--primary-color); background-color: rgba(0, 123, 255, 0.05); }
        .nav-tab.active { color: var(--primary-color); border-bottom: 3px solid var(--primary-color); }
        .tab-content { display: none; padding: 0; }

        /* Statistics Card Styles */
        .stat-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
        }

        /* Statistics Breakdown Modal Styles */
        .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0;
        }

        .stats-tabs {
            display: flex;
        }

        .stats-tab-btn {
            padding: 8px 15px;
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-weight: 500;
            color: var(--text-light);
            transition: all 0.2s ease;
        }

        .stats-tab-btn:hover {
            color: var(--primary-color);
        }

        .stats-tab-btn.active {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }

        .stats-view {
            margin-top: 15px;
        }

        .chart-message {
            text-align: center;
            padding: 40px;
            color: var(--text-light);
            font-style: italic;
        }

        #statsChartContainer {
            position: relative;
            height: 400px;
            margin-top: 15px;
        }

        /* Statistics Table Controls and Pagination Styles */
        .stats-table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 8px 12px;
        }

        .stats-table-length {
            display: flex;
            align-items: center;
            white-space: nowrap;
            font-size: 13px;
            color: #666;
        }

        .stats-table-length select {
            margin: 0 5px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            width: 50px;
            font-size: 13px;
            color: #333;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 6px center;
            background-size: 12px;
            padding-right: 20px;
        }

        .stats-table-search {
            display: flex;
            align-items: center;
            white-space: nowrap;
            font-size: 13px;
            color: #666;
            position: relative;
        }

        .stats-table-search input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            color: #333;
            background-color: white;
            width: 250px;
        }

        .stats-table-container {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 15px;
            border-top: 1px solid var(--border-light);
            border-bottom: 1px solid var(--border-light);
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
        }

        .stats-table th,
        .stats-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-light);
        }

        .stats-table th {
            background-color: var(--bg-light);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .stats-table tr:hover {
            background-color: rgba(106, 90, 224, 0.05);
        }

        .stats-table-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding: 8px 12px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .stats-table-info {
            color: #666;
            font-size: 13px;
        }

        .stats-table-pages {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background-color: #fff;
            border-radius: 4px;
            cursor: pointer;
            color: #333;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover:not([disabled]) {
            background-color: #f0f0f0;
            color: #333;
        }

        .pagination-btn[disabled] {
            opacity: 0.5;
            cursor: not-allowed;
            color: #999;
        }

        .pagination-numbers {
            display: flex;
            margin: 0 5px;
        }

        .page-number {
            padding: 4px 10px;
            margin: 0 2px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            background-color: white;
            font-size: 13px;
            color: #333;
            text-decoration: none;
        }

        .page-number.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .page-number.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            color: #999;
        }
        .tab-content.active { display: block; }
        .btn-sm { padding: 6px 12px; font-size: 13px; }
        .nav-tabs-right .btn-secondary { background-color: transparent; border-color: #ddd; color: #495057; }
        .nav-tabs-right .btn-secondary:hover { background-color: rgba(0, 123, 255, 0.05); color: var(--primary-color); }
        .nav-tabs-right .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); color: white; }
        .nav-tabs-right .btn-primary:hover { background-color: #5a4bd3; border-color: #5a4bd3; }
        .content-placeholder { display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 60px 20px; background-color: #f8f9fa; border-radius: 4px; color: #6c757d; text-align: center; margin-bottom: 20px; }
        .content-placeholder i { margin-bottom: 15px; color: #adb5bd; }
        .content-placeholder h3 { margin-bottom: 10px; font-weight: 500; }

        /* Style for the report year filter */
        #reportYearFilter {
            padding: 5px 8px; /* Slightly smaller padding */
            font-size: 13px;
            border-radius: var(--border-radius);
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            margin-left: 5px; /* Space from Manage Targets button */
            height: 31px; /* Match btn-sm height approx */
            vertical-align: middle; /* Align with buttons */
        }
        #reportYearFilter:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.2);
        }


        /* ----- Enhanced View Files Modal Styles ----- */
        .modal.large .modal-content { max-width: 800px; }
        #viewFilesModal .modal-body { padding-top: 15px; } /* Adjusted padding */
        .view-files-header { display: flex; justify-content: space-between; align-items: center; padding-bottom: 15px; margin-bottom: 15px; border-bottom: 1px solid var(--border-color); } /* Moved styles here */
        .view-files-header p { margin: 0; font-weight: 500; color: var(--text-dark); }
        .view-files-controls { display: flex; align-items: center; gap: 15px; }
        .view-files-controls .checkbox-label { display: flex; align-items: center; cursor: pointer; font-size: 14px; margin-bottom: 0; }
        .view-files-controls input[type="checkbox"] { margin-right: 5px; accent-color: var(--primary-color); }
        .view-files-controls .btn-delete { padding: 5px 10px; font-size: 13px; }
        #fileListContainer.grouped { max-height: 55vh; overflow-y: auto; padding: 0 5px 5px 5px; /* Reduced padding slightly */ }
        .file-group { margin-bottom: 15px; border: 1px solid var(--border-color); border-radius: 4px; background-color: #fff; transition: background-color 0.2s ease; }
        .file-group:last-child { margin-bottom: 5px; }
        .file-group-header { display: flex; align-items: center; padding: 10px 15px; background-color: #f8f9fa; border-bottom: 1px solid var(--border-color); cursor: pointer; position: sticky; top: 0; z-index: 1; }
        .file-group.collapsed + .file-group .file-group-header { border-top: none; }
        .file-group-header .toggle-group { background: none; border: none; padding: 0 10px 0 0; margin: 0; cursor: pointer; color: var(--primary-color); font-size: 14px; transition: transform 0.2s ease; line-height: 1; }
        .file-group.collapsed .toggle-group i { transform: rotate(-90deg); }
        .file-group:not(.collapsed) .toggle-group i { transform: rotate(0deg); }
        .file-group-header .group-title { font-weight: 600; color: var(--text-dark); flex-grow: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-right: 10px; }
        .file-group-header .badge { background-color: var(--primary-color); color: white; padding: 3px 8px; border-radius: 10px; font-size: 12px; font-weight: 500; flex-shrink: 0; }
        .file-group-content { padding: 0px 15px 5px 15px; display: block; transition: max-height 0.3s ease-out, opacity 0.3s ease-out; max-height: 1000px; overflow: hidden; }
        .file-group.collapsed .file-group-content { max-height: 0; opacity: 0; padding-top: 0; padding-bottom: 0; border-top: none; }
        .file-item { display: flex; align-items: center; padding: 8px 0; border-bottom: 1px dashed var(--border-light); gap: 10px; }
        .file-item:last-child { border-bottom: none; padding-bottom: 10px; }
        .file-group.collapsed .file-item { display: none; }
        .file-item input[type="checkbox"] { margin-right: 5px; accent-color: var(--primary-color); flex-shrink: 0; vertical-align: middle; margin-top: 0; }
        .file-item .file-icon { font-size: 1.4em; color: #6c757d; width: 25px; text-align: center; flex-shrink: 0; }
        .file-item .fa-file-pdf { color: #dc3545; } .file-item .fa-file-word { color: #0d6efd; } .file-item .fa-file-excel { color: #198754; } .file-item .fa-file-image { color: #ffc107; } .file-item .fa-file-archive { color: #6f42c1; }
        .file-item .file-details { flex-grow: 1; display: flex; flex-direction: column; overflow: hidden; min-width: 0; }
        .file-item .file-name { font-weight: 500; color: var(--text-dark); white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block; }
        .file-item .file-meta { font-size: 12px; color: var(--text-light); }
        .file-item .file-size { font-size: 13px; color: var(--text-medium); white-space: nowrap; flex-shrink: 0; min-width: 80px; text-align: right; }
        .file-item .btn-icon { background: none; border: none; color: var(--text-light); cursor: pointer; padding: 3px; font-size: 14px; flex-shrink: 0; margin-left: 5px; }
        .file-item .btn-icon:hover { color: var(--primary-color); }
        .file-item .btn-preview-file { color: var(--text-light); }
        .file-item .btn-preview-file:hover { color: var(--primary-color); }
        .file-item .btn-download-file { color: var(--text-light); }
        .file-item .btn-download-file:hover { color: var(--primary-color); }
        #viewFilesModal .loading-indicator { display: none; justify-content: center; align-items: center; padding: 30px; font-size: 16px; color: var(--text-light); }
        #viewFilesModal .loading-indicator.active { display: flex; }
        #viewFilesModal .empty-message, #viewFilesModal .error-message { text-align: center; padding: 30px; color: var(--text-light); display: none; }
        #viewFilesModal .error-message { color: var(--red-color); }
        #viewFilesSelectAll:indeterminate { accent-color: var(--primary-color); }
        .col-checkbox input[type="checkbox"] { vertical-align: middle; }

        /* Strategy Table Styles */
        #strategyTableControls { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        #strategyTableControls .results-per-page { display: flex; align-items: center; gap: 10px; }
        #strategyTableControls .results-per-page label { margin-bottom: 0; font-size: 13px; color: var(--text-light); white-space: nowrap; }
        #strategyTableControls .select-wrapper {
            position: relative;
            display: inline-block;
        }
        #strategyTableControls .results-per-page select {
            padding: 6px 30px 6px 10px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            font-size: 13px;
            background-color: var(--bg-light);
            cursor: pointer;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 12px;
            min-width: 70px;
            text-align: center;
        }
        #strategyTablePagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            min-height: 50px; /* Fixed height for pagination area */
            position: relative; /* For absolute positioning of children if needed */
        }
        #strategyTablePaginationInfo {
            font-size: 13px;
            color: var(--text-light);
            min-width: 200px; /* Ensure consistent width */
            height: 20px; /* Fixed height */
        }
        #strategyTablePaginationNav {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            min-height: 36px; /* Fixed height for buttons */
            align-items: center;
        }
        #strategyTablePaginationNav .btn {
            padding: 5px 10px;
            font-size: 13px;
            height: 32px; /* Fixed height for buttons */
            min-width: 32px; /* Minimum width for page number buttons */
            display: flex;
            align-items: center;
            justify-content: center;
        }
        #strategyTablePaginationNav .btn-nav {
            min-width: 80px; /* Fixed width for Previous/Next buttons */
        }
        #strategyTablePaginationNav .btn-page.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        #strategyTableContainer {
            max-height: 500px; /* Fixed maximum height */
            overflow-y: auto; /* Enable vertical scrolling */
        }
        #strategyTable {
            width: 100%;
            table-layout: auto; /* Changed to auto to allow columns to fit content */
            border-collapse: collapse;
            border: 1px solid var(--border-color);
            font-size: 14px;
        }
        #strategyTableHead {
            position: sticky;
            top: 0;
            background-color: var(--bg-color);
            z-index: 2;
        }
        #strategyTableHead th {
            position: sticky;
            top: 0;
            background-color: var(--bg-color);
            z-index: 2;
            padding: 12px 15px;
            text-align: center;
            white-space: nowrap; /* Prevent text wrapping in headers */
            min-width: 100px; /* Minimum width for columns */
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            color: var(--secondary-color);
        }
        #strategyTable td {
            padding: 10px 15px;
            text-align: center;
            border-bottom: 1px solid var(--border-light);
        }
        #strategyTable .municipality-col {
            text-align: left; /* Left align municipality names */
            font-weight: 500;
            min-width: 150px; /* Wider first column for municipality names */
        }
        #strategyTable .strategy-col {
            min-width: 100px; /* Minimum width for strategy columns */
        }
        /* Specific styling for longer column headers */
        #strategyTable th[data-strategy="CORE-FW4A"],
        #strategyTable th[data-strategy="PICS-MUN"],
        #strategyTable th[data-strategy="PICS-PP"],
        #strategyTable th[data-strategy="PICS-SUC"],
        #strategyTable th[data-strategy="RIS-PICS"],
        #strategyTable th[data-strategy="RIS-WISPS"] {
            min-width: 120px; /* Wider columns for longer headers */
        }
        #strategyTable .total-col {
            min-width: 80px;
        }
        #strategyTable .total-row {
            background-color: var(--bg-light);
            position: sticky;
            bottom: 0;
            z-index: 1;
        }
        #strategyTable tbody tr:hover {
            background-color: var(--sidebar-active-bg);
        }

        /* --- Target Management Modal --- */
        #manageTargetsModal .modal-content { max-width: 950px; }
        #manageTargetsModal .modal-body { padding-top: 10px; }
        #existingTargetsTableContainer { max-height: 300px; overflow-y: auto; margin-bottom: 20px; border: 1px solid var(--border-color); }
        #existingTargetsTable { width: 100%; border-collapse: collapse; }
        #existingTargetsTable th, #existingTargetsTable td { padding: 8px 10px; font-size: 13px; text-align: left; border-bottom: 1px solid var(--border-light); }
        #existingTargetsTable th { font-weight: 600; color: var(--text-light); background-color: var(--bg-color); }
        #existingTargetsTable thead { position: sticky; top: 0; background-color: var(--bg-color); z-index: 1; }
        #existingTargetsTable .actions-col { width: 80px; text-align: center; }
        #existingTargetsTable .btn-icon { background: none; border: none; padding: 3px; font-size: 14px; cursor: pointer; color: var(--text-light); }
        #existingTargetsTable .btn-icon.edit-target:hover { color: var(--primary-color); }
        #existingTargetsTable .btn-icon.delete-target:hover { color: var(--red-color); }
        #addTargetFormContainer { margin-top: 15px; padding-top: 15px; border-top: 1px solid var(--border-color); }
        #addTargetFormContainer h4 { margin-bottom: 15px; font-weight: 600; color: var(--secondary-color); }
        #addTargetForm .form-grid { grid-template-columns: repeat(5, 1fr); gap: 15px; } /* 5 columns for better layout */
        #addTargetForm .category-field { grid-column: span 1; }
        #addTargetForm .subcategory-field { grid-column: span 2; }
        #addTargetForm .indicator-field { grid-column: span 2; }
        #addTargetForm .year-field { grid-column: span 1; }
        #addTargetForm .target-field { grid-column: span 1; }
        #addTargetForm .form-field label { margin-bottom: 3px; font-size: 12px; }
        #addTargetForm input[readonly] { background-color: #eee; cursor: not-allowed; }
        #addTargetForm .required { color: var(--red-color); margin-left: 2px;}
        #addTargetForm .form-field small { font-size: 11px; color: var(--text-light); margin-top: 2px; display: block;}
        .modal-footer.target-modal-footer { display: flex; justify-content: space-between; align-items: center; }
        .target-modal-footer .left-buttons { display: flex; gap: 10px; }
        .target-modal-footer .right-buttons { display: flex; gap: 10px; }

        /* Remove hover effect for Close Filters button */
        .filter-toggle-button.active:hover {
            background-color: var(--primary-color) !important;
            color: white !important;
            border-color: var(--primary-color) !important;
        }

        /* Make stat cards clickable with modern gradient border effect */
        .stat-card {
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease, border 0.3s ease;
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: var(--border-radius);
            padding: 1px; /* Thin border width */
            background: linear-gradient(
                135deg,
                rgba(106, 90, 224, 0) 0%,
                rgba(106, 90, 224, 0.4) 50%,
                rgba(106, 90, 224, 0) 100%
            );
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        /* Futuristic animated gradient border */
        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: var(--border-radius);
            padding: 1px;
            background: linear-gradient(
                90deg,
                rgba(106, 90, 224, 0) 0%,
                rgba(106, 90, 224, 0.2) 25%,
                rgba(106, 90, 224, 0.5) 50%,
                rgba(106, 90, 224, 0.2) 75%,
                rgba(106, 90, 224, 0) 100%
            );
            background-size: 200% 100%;
            background-position: 100% 0;
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.3s ease;
            animation: none;
        }

        @keyframes gradientMove {
            0% {
                background-position: 100% 0;
            }
            100% {
                background-position: 0% 0;
            }
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 12px rgba(106, 90, 224, 0.1);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card:hover::after {
            opacity: 1;
            animation: gradientMove 1.5s linear infinite;
        }

        /* --- Report/Graph Styles --- */
        #reportGraphContainer {
            display: flex;
            flex-wrap: wrap;
            gap: 20px; /* Gap between graphs */
            margin-top: 20px; /* Adjust as needed */
            padding: 15px; /* Add padding to the container if it's a card */
            background-color: transparent; /* Assuming graphs are inside a card */
            border: none; /* Assuming graphs are inside a card */
        }
        .graph-wrapper {
            background-color: var(--bg-light);
            padding: 15px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            /* box-shadow removed as requested */
            flex: 1 1 calc(50% - 20px); /* Two columns, accounting for gap */
            min-width: 300px; /* Minimum width before wrapping */
            display: flex;
            flex-direction: column;
            height: 350px; /* Fixed height for consistency */
        }
        .graph-wrapper h4 {
            font-size: 14px;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 15px;
            text-align: center;
            white-space: normal; /* Allow title to wrap */
        }
        .graph-wrapper canvas {
            max-width: 100%;
            flex-grow: 1; /* Allow canvas to take remaining space */
            height: auto !important; /* Ensure responsive height within flex */
            min-height: 200px; /* Prevent collapsing too much */
        }

        /* Responsive adjustments for graphs */
        @media (max-width: 768px) {
            .graph-wrapper { flex-basis: 100%; height: 300px; } /* One column, maybe reduce height */
        }

        /* Modern search field with icon */
        .search-container {
            position: relative;
            width: 100%;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 14px;
            z-index: 2;
            pointer-events: none;
        }

        .search-input {
            padding-left: 35px !important;
            padding-right: 10px !important;
            transition: all 0.3s ease;
            width: 100%;
        }

        /* Free Wi-Fi Report Tab Styles */
        .report-header-container {
            margin-bottom: 15px; /* Standard spacing below the title */
        }

        .fw4a-report-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .fw4a-info-column {
            flex: 1;
            min-width: 300px;
            max-width: 400px;
        }

        .fw4a-data-column {
            flex: 2;
            min-width: 500px;
        }

        .fw4a-version-info {
            background-color: var(--bg-light);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 20px;
            margin-bottom: 20px;
        }

        .fw4a-stats-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
            padding-right: 70px;
        }

        .fw4a-stats-illustration {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .wifi-icon {
            position: relative;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .wifi-circle {
            position: absolute;
            width: 12px;
            height: 12px;
            background-color: var(--primary-color);
            border-radius: 50%;
            z-index: 4;
        }

        .wifi-wave {
            position: absolute;
            border: 2px solid var(--primary-color);
            border-radius: 50%;
            opacity: 0.7;
        }

        .wave1 {
            width: 20px;
            height: 20px;
            z-index: 3;
        }

        .wave2 {
            width: 32px;
            height: 32px;
            z-index: 2;
        }

        .wave3 {
            width: 44px;
            height: 44px;
            z-index: 1;
        }

        .fw4a-big-number {
            font-size: 54px;
            font-weight: 700;
            color: var(--primary-color);
            margin-right: 15px;
            line-height: 1;
            min-width: 90px;
            text-align: center;
            display: flex;
            align-items: center;
        }

        .fw4a-stats-title {
            flex: 1;
            text-align: left;
        }

        .fw4a-stats-title h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
            text-align: left;
        }

        .fw4a-stats-summary {
            padding: 0;
            margin: 0;
            font-size: 14px;
            color: var(--text-medium);
            line-height: 1.4;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 8px;
            text-align: left;
        }

        .stats-divider {
            color: var(--border-color);
            font-weight: 300;
        }

        .fw4a-strategy-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .strategy-stat-item {
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 12px 10px;
            flex: 1 1 calc(50% - 10px);
            min-width: 120px;
            text-align: left;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .strategy-stat-number {
            font-size: 28px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .strategy-stat-label {
            font-size: 13px;
            color: var(--text-medium);
            white-space: normal;
            line-height: 1.3;
            max-width: 100%;
        }



        .fw4a-description {
            font-size: 14px;
            color: var(--text-medium);
            line-height: 1.5;
            margin-top: 5px;
        }

        .fw4a-table-controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .fw4a-table-left-controls,
        .fw4a-table-right-controls {
            display: flex;
            align-items: center;
        }

        .fw4a-table-right-controls .search-container {
            width: 250px;
        }

        .fw4a-table-container {
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-bottom: 15px;
        }

        #fw4aTable {
            width: 100%;
            border-collapse: collapse;
        }

        #fw4aTable th {
            background-color: var(--bg-color);
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            color: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1;
        }

        #fw4aTable td {
            padding: 10px 15px;
            border-bottom: 1px solid var(--border-light);
        }

        #fw4aTable tr:hover {
            background-color: var(--sidebar-active-bg);
        }

        .fw4a-table-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pagination-info {
            font-size: 13px;
            color: var(--text-light);
        }

        .pagination-nav {
            display: flex;
            gap: 5px;
        }

        /* Visualization Styles */
        .fw4a-visualization-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .fw4a-visualization-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .fw4a-data-summary {
            margin-bottom: 20px;
        }

        .summary-box {
            background-color: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 15px;
        }

        .summary-content {
            display: flex;
            flex-direction: column;
        }

        .summary-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 10px;
        }

        .summary-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .summary-stat {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-label {
            font-size: 13px;
            color: var(--text-medium);
        }

        .stat-value {
            font-size: 15px;
            font-weight: 600;
            color: var(--primary-color);
            margin-right: 4px;
        }

        .stat-percentage {
            font-size: 13px;
            color: var(--text-medium);
            font-style: italic;
        }

        .stat-active {
            color: var(--green-color);
        }

        .stat-inactive {
            color: var(--red-color);
        }

        .fw4a-visualization-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-container {
            flex: 1;
            min-width: 300px;
            height: 300px;
            background-color: #fff;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 15px;
            position: relative;
        }

        @media (max-width: 768px) {
            .chart-container {
                flex-basis: 100%;
            }
        }

    </style>
</head>
<body>
    <!-- Notification -->
    <div id="notification" class="notification"><p class="notification-message" id="notificationMessage"></p></div>
    <!-- App Container -->
    <div class="app-container">
        <?php
        // Set current page name for sidebar highlighting
        $current_page_name = 'freewifi4all'; // <-- Changed for freewifi4all

        // Include the sidebar
        include 'sidebar.php';
        ?>
        <!-- Main Content -->
        <main class="app-main">
             <div class="dashboard-header">
                 <div class="office-header">
                     <div class="office-logo"> <img src="images/dict-logo.png" alt="DICT Logo"> </div>
                     <div class="office-info"> <h1>DICT SDN - Free Wifi for All (FW4A) Activities</h1> <p>Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p> </div> <!-- <-- Changed Title -->
                 </div>
             </div>
             <?php if ($db_connection_error): ?> <div class="db-error-message"> <strong>Database Error:</strong> <?php echo htmlspecialchars($db_connection_error); ?> </div> <?php endif; ?>

             <!-- NAV TABS CONTAINER -->
            <section class="nav-tabs-container">
                <div class="nav-tabs">
                    <div class="nav-tabs-left">
                        <a href="#" class="nav-tab <?php echo ($active_tab === 'activities') ? 'active' : ''; ?>" data-tab="activities">Activities Conducted</a>
                        <a href="#" class="nav-tab <?php echo ($active_tab === 'participants') ? 'active' : ''; ?>" data-tab="participants">Monitoring Status</a>
                        <a href="#" class="nav-tab <?php echo ($active_tab === 'letters') ? 'active' : ''; ?>" data-tab="letters">Letter Requests</a>
                        <a href="#" class="nav-tab <?php echo ($active_tab === 'reports') ? 'active' : ''; ?>" data-tab="reports">Graph/Report</a>
                    </div>
                    <div class="nav-tabs-right">
                        <!-- Year Filter and Manage Targets buttons removed -->

                        <!-- Import/Export buttons (Hidden on Reports Tab via JS) -->
                        <button id="importButton" class="btn btn-secondary btn-sm" title="Import data from CSV" style="display: <?php echo ($active_tab !== 'reports') ? 'inline-flex' : 'none'; ?>;"><i class="fas fa-file-import"></i> Import</button>
                        <button id="exportButton" class="btn btn-primary btn-sm" title="Export data" style="display: <?php echo ($active_tab !== 'reports') ? 'inline-flex' : 'none'; ?>;"><i class="fas fa-file-export"></i> Export</button>
                    </div>
                </div>
            </section> <!-- END Nav Tabs Container -->


            <!-- Activities Tab -->
            <div class="tab-content <?php echo ($active_tab === 'activities') ? 'active' : ''; ?>" id="activities-tab">
                <!-- Activity Stats -->
                <section class="stats-cards">
                    <?php if (!empty($stats)): foreach ($stats as $stat): ?>
                        <?php $insight = $stat['insight'] ?? []; ?>
                        <div class="stat-card" data-key="<?php echo htmlspecialchars($stat['key'] ?? ''); ?>">
                            <div class="card-header">
                                <h4><?php echo htmlspecialchars($stat['title'] ?? 'N/A'); ?></h4>
                                <div class="stat-card-icon icon-<?php echo htmlspecialchars(str_replace('fas fa-', '', $stat['icon'] ?? 'fa-chart-bar')); ?>">
                                    <i class="<?php echo htmlspecialchars($stat['icon'] ?? 'fas fa-chart-bar'); ?>"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="value"><?php echo htmlspecialchars($stat['value'] ?? 'N/A'); ?></p>
                                <?php if (isset($stat['insight']) && is_array($insight)): ?>
                                    <div class="stat-insight <?php echo htmlspecialchars($insight['color_class'] ?? 'text-neutral'); ?>">
                                        <i class="fas <?php echo htmlspecialchars($insight['icon_class'] ?? 'fa-minus'); ?>"></i>
                                        <span><?php echo htmlspecialchars($insight['text'] ?? 'No comparison data.'); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; else: ?>
                        <div class="stat-card">
                            <div class="card-header"><h4>Status</h4><div class="stat-card-icon icon-exclamation-circle"><i class="fas fa-exclamation-circle"></i></div></div>
                            <div class="card-body"><p class="value" style="font-size: 18px; color: var(--red-color);">Error</p><div class="stat-insight text-red"><i class="fas fa-exclamation-triangle"></i><span>Cannot load statistics.</span></div></div>
                        </div>
                    <?php endif; ?>
                </section>
                <!-- Activities Table -->
                <section class="recent-activities card">
                     <div class="activities-header"> <h2><?php echo !empty($search_term_activity) || count(array_filter(array_intersect_key($_GET, array_flip($filter_columns_activity)))) > 0 ? 'Filtered Free Wifi for All Activities' : 'All Free Wifi for All Activities'; ?></h2> <button id="addActivityButton" class="btn btn-primary"> <i class="fas fa-plus"></i> Add Activity </button> </div> <!-- <-- Changed Title -->
                     <div class="table-controls">
                        <form action="freewifi4all.php" method="get" id="resultsPerPageFormActivity" class="results-per-page-form"> <input type="hidden" name="tab" value="activities"> <?php if (!empty($search_term_activity)): ?> <input type="hidden" name="search" value="<?php echo htmlspecialchars($search_term_activity); ?>"> <?php endif; ?> <?php foreach ($filter_columns_activity as $column): if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?> <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>"> <?php endif; endforeach; ?> <input type="hidden" name="page" value="1"> <!-- Carry over participant params --> <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($_GET['p_search'] ?? ''); ?>"> <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($_GET['p_limit'] ?? $default_results_per_page); ?>"> <input type="hidden" name="p_page" value="<?php echo htmlspecialchars($_GET['p_page'] ?? 1); ?>"> <input type="hidden" name="year" value="<?php echo $selected_report_year; ?>"> <!-- Carry over year --> <?php foreach ($filter_columns_participant as $column): if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== ''): ?> <input type="hidden" name="<?php echo 'p_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['p_'.$column])); ?>"> <?php endif; endforeach; ?> <label for="resultsPerPageSelectActivity">Result per page:</label> <select name="limit" id="resultsPerPageSelectActivity" onchange="this.form.submit();"> <?php foreach($results_per_page_options as $option): ?> <option value="<?php echo $option; ?>" <?php if($option == $results_per_page_activity) echo 'selected'; ?>><?php echo $option; ?></option> <?php endforeach; ?> </select> </form> <!-- <-- Changed Action -->
                        <div class="table-right-controls">
                            <form action="freewifi4all.php" method="get" class="search-form">
                                <div class="filter-group search-container">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" id="search" name="search" class="form-control search-input"
                                           placeholder="Search in actions, details..."
                                           value="<?php echo htmlspecialchars($search_term_activity); ?>">
                                    <!-- Pass current tab -->
                                    <input type="hidden" name="tab" value="activities">
                                    <!-- Pass current limit -->
                                    <input type="hidden" name="limit" value="<?php echo $results_per_page_activity; ?>">
                                    <!-- Pass filters -->
                                    <?php foreach ($filter_columns_activity as $column): ?>
                                        <?php if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?>
                                            <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>">
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </form>

                        </div>
                     </div>

                     <!-- Filter Dropdown Area -->
                     <div id="activityFilters" class="filter-dropdown-container" style="display: block;">
                         <form action="freewifi4all.php" method="get" class="filter-form"> <!-- <-- Changed Action -->
                             <input type="hidden" name="tab" value="activities">
                             <!-- Carry over other params -->
                             <input type="hidden" name="search" value="<?php echo htmlspecialchars($search_term_activity); ?>">
                             <input type="hidden" name="limit" value="<?php echo htmlspecialchars($results_per_page_activity); ?>">
                             <input type="hidden" name="page" value="1">
                             <!-- Carry over participant params -->
                             <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($_GET['p_search'] ?? ''); ?>">
                             <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($_GET['p_limit'] ?? $default_results_per_page); ?>">
                             <input type="hidden" name="p_page" value="<?php echo htmlspecialchars($_GET['p_page'] ?? 1); ?>">

                             <div class="filter-group">
                                 <label for="filter_year">Year:</label>
                                 <select name="year" id="filter_year">
                                     <option value="">All Years</option>
                                     <?php
                                     // Get distinct years from database
                                     $activity_years = [];
                                     if ($conn) {
                                         $year_query = "SELECT DISTINCT YEAR(start) as year FROM tblactivity WHERE project = ? AND start IS NOT NULL ORDER BY year DESC";
                                         $stmt_year = mysqli_prepare($conn, $year_query);
                                         if ($stmt_year) {
                                             mysqli_stmt_bind_param($stmt_year, "s", $project_filter_value);
                                             if (mysqli_stmt_execute($stmt_year)) {
                                                 $result_year = mysqli_stmt_get_result($stmt_year);
                                                 while ($row = mysqli_fetch_assoc($result_year)) {
                                                     if (!empty($row['year'])) {
                                                         $activity_years[] = $row['year'];
                                                     }
                                                 }
                                                 mysqli_free_result($result_year);
                                             }
                                             mysqli_stmt_close($stmt_year);
                                         }
                                     }

                                     // If no years found, use current year as fallback
                                     if (empty($activity_years)) {
                                         $activity_years[] = date('Y');
                                     }

                                     foreach ($activity_years as $year):
                                     ?>
                                         <option value="<?php echo $year; ?>" <?php echo (isset($_GET['year']) && $_GET['year'] == $year) ? 'selected' : ''; ?>><?php echo $year; ?></option>
                                     <?php endforeach; ?>
                                 </select>
                             </div>
                             <div class="filter-group">
                                 <label for="filter_sector">Sector:</label>
                                 <select name="sector" id="filter_sector">
                                     <option value="">All Sectors</option>
                                     <?php
                                     // Get distinct sectors from database
                                     $sectors = [];
                                     if ($conn) {
                                         $sector_query = "SELECT DISTINCT sector FROM tblactivity WHERE project = ? AND sector IS NOT NULL AND sector != '' ORDER BY sector";
                                         $stmt_sector = mysqli_prepare($conn, $sector_query);
                                         if ($stmt_sector) {
                                             mysqli_stmt_bind_param($stmt_sector, "s", $project_filter_value);
                                             if (mysqli_stmt_execute($stmt_sector)) {
                                                 $result_sector = mysqli_stmt_get_result($stmt_sector);
                                                 while ($row = mysqli_fetch_assoc($result_sector)) {
                                                     $sectors[] = $row['sector'];
                                                 }
                                                 mysqli_free_result($result_sector);
                                             }
                                             mysqli_stmt_close($stmt_sector);
                                         }
                                     }
                                     foreach ($sectors as $option):
                                     ?>
                                         <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['sector']) && $_GET['sector'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                     <?php endforeach; ?>
                                 </select>
                             </div>
                             <div class="filter-group">
                                 <label for="filter_indicator">Indicator:</label>
                                 <select name="indicator" id="filter_indicator">
                                     <option value="">All Indicators</option>
                                     <?php
                                     // Get distinct indicators from database
                                     $indicators = [];
                                     if ($conn) {
                                         $indicator_query = "SELECT DISTINCT indicator FROM tblactivity WHERE project = ? AND indicator IS NOT NULL AND indicator != '' ORDER BY indicator";
                                         $stmt_indicator = mysqli_prepare($conn, $indicator_query);
                                         if ($stmt_indicator) {
                                             mysqli_stmt_bind_param($stmt_indicator, "s", $project_filter_value);
                                             if (mysqli_stmt_execute($stmt_indicator)) {
                                                 $result_indicator = mysqli_stmt_get_result($stmt_indicator);
                                                 while ($row = mysqli_fetch_assoc($result_indicator)) {
                                                     $indicators[] = $row['indicator'];
                                                 }
                                                 mysqli_free_result($result_indicator);
                                             }
                                             mysqli_stmt_close($stmt_indicator);
                                         }
                                     }
                                     foreach ($indicators as $option):
                                     ?>
                                         <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['indicator']) && $_GET['indicator'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                     <?php endforeach; ?>
                                 </select>
                             </div>
                             <div class="filter-group">
                                 <label for="filter_municipality">Municipality:</label>
                                 <select name="municipality" id="filter_municipality">
                                     <option value="">All Municipalities</option>
                                     <?php
                                     // Get distinct municipalities from database
                                     $municipalities = [];
                                     if ($conn) {
                                         $municipality_query = "SELECT DISTINCT municipality FROM tblactivity WHERE project = ? AND municipality IS NOT NULL AND municipality != '' ORDER BY municipality";
                                         $stmt_municipality = mysqli_prepare($conn, $municipality_query);
                                         if ($stmt_municipality) {
                                             mysqli_stmt_bind_param($stmt_municipality, "s", $project_filter_value);
                                             if (mysqli_stmt_execute($stmt_municipality)) {
                                                 $result_municipality = mysqli_stmt_get_result($stmt_municipality);
                                                 while ($row = mysqli_fetch_assoc($result_municipality)) {
                                                     $municipalities[] = $row['municipality'];
                                                 }
                                                 mysqli_free_result($result_municipality);
                                             }
                                             mysqli_stmt_close($stmt_municipality);
                                         }
                                     }
                                     foreach ($municipalities as $option):
                                     ?>
                                         <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['municipality']) && $_GET['municipality'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                     <?php endforeach; ?>
                                 </select>
                             </div>

                             <div class="filter-actions">
                                 <button type="submit" class="btn btn-primary btn-sm apply-filters-btn">Apply Filters</button>
                                 <a href="freewifi4all.php?tab=activities&limit=<?php echo $results_per_page_activity; ?>" class="btn btn-secondary btn-sm clear-filters-btn">Clear Filters</a> <!-- <-- Changed Link -->
                             </div>
                         </form>
                     </div>

                     <div class="table-container"> <table> <thead> <tr> <th class="col-checkbox"><input type="checkbox" id="selectAllCheckboxActivity" class="select-all-checkbox" data-type="activity" title="Select/Deselect All Activities"></th> <th class="col-rownum">#</th> <th>Start Date</th><th>End Date</th><th>Project</th><th>Subproject</th><th>Activity</th><th>Indicator</th><th>Training?</th><th>Municipality</th><th>District</th><th>Barangay</th><th>Agency</th><th>Mode</th><th>Sector</th><th>Person(s)</th><th>Resource Person(s)</th><th>Participants</th><th>Completers</th><th>Male</th><th>Female</th><th>Approved?</th><th>MOV</th><th>Remarks</th> </tr> </thead> <tbody id="activityTableBody"> <?php if ($activities_fetch_error && !$activities_result): ?> <tr> <td colspan="<?php echo $table_colspan_activity; ?>" class="table-message error"> <?php echo htmlspecialchars($activities_fetch_error); ?> </td> </tr> <?php elseif ($activities_result && mysqli_num_rows($activities_result) > 0): $row_index_activity = 0; while ($activity = mysqli_fetch_assoc($activities_result)): $row_number_activity = $offset_activity + $row_index_activity + 1; ?> <tr data-activity-id="<?php echo htmlspecialchars($activity['id']); ?>"> <td class="col-checkbox"><input type="checkbox" class="row-checkbox activity-row-checkbox" name="activity_ids[]" value="<?php echo htmlspecialchars($activity['id']); ?>"></td> <td class="col-rownum"><?php echo $row_number_activity; ?></td> <td class="date-col"><?php echo formatDateForDisplay($activity['start']); ?></td> <td class="date-col"><?php echo formatDateForDisplay($activity['end']); ?></td> <td><?php echo htmlspecialchars($activity['project'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['subproject'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['activity'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['indicator'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['training'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['municipality'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['district'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['barangay'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['agency'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['mode'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['sector'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['person'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['resource'] ?? ''); ?></td> <td class="participants-col"><?php echo number_format((int)($activity['participants'] ?? 0)); ?></td> <td class="participants-col"><?php echo number_format((int)($activity['completers'] ?? 0)); ?></td> <td class="participants-col"><?php echo number_format((int)($activity['male'] ?? 0)); ?></td> <td class="participants-col"><?php echo number_format((int)($activity['female'] ?? 0)); ?></td> <td><?php echo htmlspecialchars($activity['approved'] ?? ''); ?></td> <td><?php echo htmlspecialchars($activity['mov'] ?? ''); ?></td> <td><?php echo nl2br(htmlspecialchars($activity['remarks'] ?? '')); ?></td> </tr> <?php $row_index_activity++; endwhile; else: ?> <tr> <td colspan="<?php echo $table_colspan_activity; ?>" class="table-message"> <?php echo htmlspecialchars($activities_fetch_error); ?> </td> </tr> <?php endif; if ($activities_result && is_object($activities_result)) mysqli_free_result($activities_result); ?> </tbody> </table> </div>
                     <div class="pagination-controls"> <div class="pagination-info"> <?php if ($total_activities > 0) { echo "Showing " . ($offset_activity + 1) . " - " . min($offset_activity + $results_per_page_activity, $total_activities) . " of " . number_format($total_activities); } else { echo "Showing 0 - 0 of 0"; } ?> </div> <div class="pagination-nav"> <?php $page_link_base_activity = "freewifi4all.php?page="; if ($current_page_activity > 1): ?> <a href="<?php echo $page_link_base_activity . ($current_page_activity - 1) . $url_params_activity; ?>" class="btn btn-nav">Previous</a> <?php else: ?> <span class="btn btn-nav disabled">Previous</span> <?php endif; if ($total_pages_activity > 1) { $max_links = 5; $start_link = max(1, $current_page_activity - floor($max_links / 2)); $end_link = min($total_pages_activity, $start_link + $max_links - 1); if ($end_link == $total_pages_activity) $start_link = max(1, $total_pages_activity - $max_links + 1); if ($start_link > 1) { echo '<a href="'.$page_link_base_activity.'1'.$url_params_activity.'" class="btn btn-page">1</a>'; if ($start_link > 2) echo '<span class="btn btn-page disabled">...</span>'; } for ($i = $start_link; $i <= $end_link; $i++): echo '<a href="'.$page_link_base_activity.$i.$url_params_activity.'" class="btn btn-page '.($i == $current_page_activity ? 'active' : '').'">'.$i.'</a>'; endfor; if ($end_link < $total_pages_activity) { if ($end_link < $total_pages_activity - 1) echo '<span class="btn btn-page disabled">...</span>'; echo '<a href="'.$page_link_base_activity.$total_pages_activity.$url_params_activity.'" class="btn btn-page">'.$total_pages_activity.'</a>'; } } elseif ($total_activities > 0 && $total_pages_activity == 1) { echo '<a href="'.$page_link_base_activity.'1'.$url_params_activity.'" class="btn btn-page active">1</a>'; } if ($current_page_activity < $total_pages_activity): ?> <a href="<?php echo $page_link_base_activity . ($current_page_activity + 1) . $url_params_activity; ?>" class="btn btn-nav">Next</a> <?php else: ?> <span class="btn btn-nav disabled">Next</span> <?php endif; ?> </div> </div> <!-- <-- Changed Base URL -->
                </section>
            </div>

             <!-- Participants Tab -->
            <div class="tab-content <?php echo ($active_tab === 'participants') ? 'active' : ''; ?>" id="participants-tab">
                 <section class="stats-cards">
                     <?php if (!empty($participant_stats)): foreach ($participant_stats as $stat): ?>
                         <?php $insight = $stat['insight'] ?? []; ?>
                         <div class="stat-card" data-key="<?php echo htmlspecialchars($stat['key'] ?? ''); ?>">
                             <div class="card-header">
                                 <h4><?php echo htmlspecialchars($stat['title'] ?? 'N/A'); ?></h4>
                                 <div class="stat-card-icon icon-<?php echo htmlspecialchars(str_replace('fas fa-', '', $stat['icon'] ?? 'fa-chart-bar')); ?>">
                                     <i class="<?php echo htmlspecialchars($stat['icon'] ?? 'fas fa-chart-bar'); ?>"></i>
                                 </div>
                             </div>
                             <div class="card-body">
                                 <p class="value"><?php echo htmlspecialchars($stat['value'] ?? 'N/A'); ?></p>
                                 <?php if (isset($stat['insight']) && is_array($insight)): ?>
                                     <div class="stat-insight <?php echo htmlspecialchars($insight['color_class'] ?? 'text-neutral'); ?>">
                                         <i class="fas <?php echo htmlspecialchars($insight['icon_class'] ?? 'fa-minus'); ?>"></i>
                                         <span><?php echo htmlspecialchars($insight['text'] ?? 'No comparison data.'); ?></span>
                                     </div>
                                 <?php endif; ?>
                             </div>
                         </div>
                     <?php endforeach; else: ?>
                         <div class="stat-card">
                             <div class="card-header"><h4>Status</h4><div class="stat-card-icon icon-exclamation-circle"><i class="fas fa-exclamation-circle"></i></div></div>
                             <div class="card-body"><p class="value" style="font-size: 18px; color: var(--red-color);">Error</p><div class="stat-insight text-red"><i class="fas fa-exclamation-triangle"></i><span>Cannot load statistics.</span></div></div>
                         </div>
                     <?php endif; ?>
                 </section>
                 <section class="recent-participants card">
                      <div class="participants-header"> <h2><?php echo !empty($search_term_participant) || count(array_filter(array_intersect_key($_GET, array_flip(array_map(fn($c) => 'p_'.$c, $filter_columns_participant))))) > 0 ? 'Filtered Monitoring Status' : 'Monitoring Status'; ?></h2> <div style="display: flex; gap: 10px;"><button id="strategyTableButton" class="btn btn-secondary"> <i class="fas fa-table"></i> Strategy Table </button><button id="addParticipantButton" class="btn btn-primary"> <i class="fas fa-plus"></i> Add LGU </button></div> </div>
                      <div class="table-controls">
                         <form action="freewifi4all.php" method="get" id="resultsPerPageFormParticipant" class="results-per-page-form"> <input type="hidden" name="tab" value="participants"> <?php if (!empty($search_term_participant)): ?> <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($search_term_participant); ?>"> <?php endif; ?> <input type="hidden" name="p_page" value="1"> <?php foreach ($filter_columns_participant as $column): if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== ''): ?> <input type="hidden" name="<?php echo 'p_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['p_'.$column])); ?>"> <?php endif; endforeach; ?> <!-- Carry over activity params --> <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"> <input type="hidden" name="limit" value="<?php echo htmlspecialchars($_GET['limit'] ?? $default_results_per_page); ?>"> <input type="hidden" name="page" value="<?php echo htmlspecialchars($_GET['page'] ?? 1); ?>"> <input type="hidden" name="year" value="<?php echo $selected_report_year; ?>"> <!-- Carry over year --> <?php foreach ($filter_columns_activity as $column): if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?> <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>"> <?php endif; endforeach; ?> <label for="resultsPerPageSelectParticipant">Result per page:</label> <select name="p_limit" id="resultsPerPageSelectParticipant" onchange="this.form.submit();"> <?php foreach($results_per_page_options as $option): ?> <option value="<?php echo $option; ?>" <?php if($option == $results_per_page_participant) echo 'selected'; ?>><?php echo $option; ?></option> <?php endforeach; ?> </select> </form> <!-- <-- Changed Action -->
                         <div class="table-right-controls">
                            <form action="freewifi4all.php" method="get" class="search-form">
                                <div class="filter-group search-container">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" id="p_search" name="p_search" class="form-control search-input"
                                           placeholder="Search in actions, details..."
                                           value="<?php echo htmlspecialchars($search_term_participant); ?>">
                                    <!-- Pass current tab -->
                                    <input type="hidden" name="tab" value="participants">
                                    <!-- Pass current limit -->
                                    <input type="hidden" name="p_limit" value="<?php echo $results_per_page_participant; ?>">
                                    <!-- Pass filters -->
                                    <?php foreach ($filter_columns_participant as $column): ?>
                                        <?php if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== ''): ?>
                                            <input type="hidden" name="<?php echo 'p_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['p_'.$column])); ?>">
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </form>

                         </div>
                      </div>

                      <!-- Filter Dropdown Area -->
                      <div id="participantFilters" class="filter-dropdown-container" style="display: block;">
                          <form action="freewifi4all.php" method="get" class="filter-form"> <!-- <-- Changed Action -->
                              <input type="hidden" name="tab" value="participants">
                              <!-- Carry over other params -->
                              <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($search_term_participant); ?>">
                              <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($results_per_page_participant); ?>">
                              <input type="hidden" name="p_page" value="1">
                              <!-- Carry over activity params -->
                              <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                              <input type="hidden" name="limit" value="<?php echo htmlspecialchars($_GET['limit'] ?? $default_results_per_page); ?>">
                              <input type="hidden" name="page" value="<?php echo htmlspecialchars($_GET['page'] ?? 1); ?>">

                              <div class="filter-group">
                                  <label for="filter_p_locality">Locality:</label>
                                  <select name="p_locality" id="filter_p_locality">
                                      <option value="">All Localities</option>
                                      <?php
                                      // Get distinct localities from database
                                      $p_localities = [];
                                      if ($conn) {
                                          $p_locality_query = "SELECT DISTINCT locality FROM tblfwfa WHERE locality IS NOT NULL AND locality != '' ORDER BY locality";
                                          $stmt_p_locality = mysqli_prepare($conn, $p_locality_query);
                                          if ($stmt_p_locality) {
                                              if (mysqli_stmt_execute($stmt_p_locality)) {
                                                  $result_p_locality = mysqli_stmt_get_result($stmt_p_locality);
                                                  while ($row = mysqli_fetch_assoc($result_p_locality)) {
                                                      if (!empty($row['locality'])) {
                                                          $p_localities[] = $row['locality'];
                                                      }
                                                  }
                                                  mysqli_free_result($result_p_locality);
                                              }
                                              mysqli_stmt_close($stmt_p_locality);
                                          }
                                      }

                                      // If no localities found, show empty list
                                      if (empty($p_localities)) {
                                          $p_localities = [];
                                      }

                                      foreach ($p_localities as $locality):
                                      ?>
                                          <option value="<?php echo htmlspecialchars($locality); ?>" <?php echo (isset($_GET['p_locality']) && $_GET['p_locality'] == $locality) ? 'selected' : ''; ?>><?php echo htmlspecialchars($locality); ?></option>
                                      <?php endforeach; ?>
                                  </select>
                              </div>
                              <div class="filter-group">
                                  <label for="filter_p_type">Type:</label>
                                  <select name="p_type" id="filter_p_type">
                                      <option value="">All Types</option>
                                      <?php
                                      // Get distinct types from database
                                      $p_types = [];
                                      if ($conn) {
                                          $p_type_query = "SELECT DISTINCT type FROM tblfwfa WHERE type IS NOT NULL AND type != '' ORDER BY type";
                                          $stmt_p_type = mysqli_prepare($conn, $p_type_query);
                                          if ($stmt_p_type) {
                                              if (mysqli_stmt_execute($stmt_p_type)) {
                                                  $result_p_type = mysqli_stmt_get_result($stmt_p_type);
                                                  while ($row = mysqli_fetch_assoc($result_p_type)) {
                                                      $p_types[] = $row['type'];
                                                  }
                                                  mysqli_free_result($result_p_type);
                                              }
                                              mysqli_stmt_close($stmt_p_type);
                                          }
                                      }
                                      foreach ($p_types as $option):
                                      ?>
                                          <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['p_type']) && $_GET['p_type'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                      <?php endforeach; ?>
                                  </select>
                              </div>
                              <div class="filter-group">
                                  <label for="filter_p_status">Status:</label>
                                  <select name="p_status" id="filter_p_status">
                                      <option value="">All Statuses</option>
                                      <?php
                                      // Get distinct statuses from database
                                      $p_statuses = [];
                                      if ($conn) {
                                          $p_status_query = "SELECT DISTINCT status FROM tblfwfa WHERE status IS NOT NULL AND status != '' ORDER BY status";
                                          $stmt_p_status = mysqli_prepare($conn, $p_status_query);
                                          if ($stmt_p_status) {
                                              if (mysqli_stmt_execute($stmt_p_status)) {
                                                  $result_p_status = mysqli_stmt_get_result($stmt_p_status);
                                                  while ($row = mysqli_fetch_assoc($result_p_status)) {
                                                      $p_statuses[] = $row['status'];
                                                  }
                                                  mysqli_free_result($result_p_status);
                                              }
                                              mysqli_stmt_close($stmt_p_status);
                                          }
                                      }
                                      foreach ($p_statuses as $option):
                                      ?>
                                          <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['p_status']) && $_GET['p_status'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                      <?php endforeach; ?>
                                  </select>
                              </div>
                              <div class="filter-group">
                                  <label for="filter_p_strategy">Strategy:</label>
                                  <select name="p_strategy" id="filter_p_strategy">
                                      <option value="">All Strategies</option>
                                      <?php
                                      // Get distinct strategies from database
                                      $p_strategies = [];
                                      if ($conn) {
                                          $p_strategy_query = "SELECT DISTINCT strategy FROM tblfwfa WHERE strategy IS NOT NULL AND strategy != '' ORDER BY strategy";
                                          $stmt_p_strategy = mysqli_prepare($conn, $p_strategy_query);
                                          if ($stmt_p_strategy) {
                                              if (mysqli_stmt_execute($stmt_p_strategy)) {
                                                  $result_p_strategy = mysqli_stmt_get_result($stmt_p_strategy);
                                                  while ($row = mysqli_fetch_assoc($result_p_strategy)) {
                                                      $p_strategies[] = $row['strategy'];
                                                  }
                                                  mysqli_free_result($result_p_strategy);
                                              }
                                              mysqli_stmt_close($stmt_p_strategy);
                                          }
                                      }
                                      foreach ($p_strategies as $option):
                                      ?>
                                          <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['p_strategy']) && $_GET['p_strategy'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                      <?php endforeach; ?>
                                  </select>
                              </div>

                              <div class="filter-actions">
                                  <button type="submit" class="btn btn-primary btn-sm apply-filters-btn">Apply Filters</button>
                                  <a href="freewifi4all.php?tab=participants&p_limit=<?php echo $results_per_page_participant; ?>" class="btn btn-secondary btn-sm clear-filters-btn">Clear Filters</a> <!-- <-- Changed Link -->
                              </div>
                          </form>
                      </div>

                      <div class="table-container"> <table> <thead> <tr> <th class="col-checkbox"><input type="checkbox" id="selectAllCheckboxParticipant" class="select-all-checkbox" data-type="participant" title="Select/Deselect All Monitoring Data"></th> <th class="col-rownum">#</th> <th>Locality</th><th>Barangay</th><th>District</th><th>Locations</th><th>Type</th><th>Code</th><th>Strategy</th><th>Status</th><th>Reason</th><th>Remarks</th> </tr> </thead> <tbody id="participantTableBody"> <?php if ($participants_fetch_error && !$participants_result): ?> <tr> <td colspan="<?php echo $table_colspan_participant; ?>" class="table-message error"> <?php echo htmlspecialchars($participants_fetch_error); ?> </td> </tr> <?php elseif ($participants_result && mysqli_num_rows($participants_result) > 0): $row_index_participant = 0; while ($participant = mysqli_fetch_assoc($participants_result)): $row_number_participant = $offset_participant + $row_index_participant + 1; ?> <tr data-participant-id="<?php echo htmlspecialchars($participant['id']); ?>"> <td class="col-checkbox"><input type="checkbox" class="row-checkbox participant-row-checkbox" name="participant_ids[]" value="<?php echo htmlspecialchars($participant['id']); ?>"></td> <td class="col-rownum"><?php echo $row_number_participant; ?></td> <td><?php echo htmlspecialchars($participant['locality'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['barangay'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['district'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['locations'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['type'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['code'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['strategy'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['status'] ?? ''); ?></td> <td><?php echo htmlspecialchars($participant['reason'] ?? ''); ?></td> <td><?php echo nl2br(htmlspecialchars($participant['remarks'] ?? '')); ?></td> </tr> <?php $row_index_participant++; endwhile; else: ?> <tr> <td colspan="<?php echo $table_colspan_participant; ?>" class="table-message"> <?php echo htmlspecialchars($participants_fetch_error); ?> </td> </tr> <?php endif; if ($participants_result && is_object($participants_result)) mysqli_free_result($participants_result); ?> </tbody> </table> </div>
                      <div class="pagination-controls"> <div class="pagination-info"> <?php if ($total_participants > 0) { echo "Showing " . ($offset_participant + 1) . " - " . min($offset_participant + $results_per_page_participant, $total_participants) . " of " . number_format($total_participants); } else { echo "Showing 0 - 0 of 0"; } ?> </div> <div class="pagination-nav"> <?php $page_link_base_participant = "freewifi4all.php?p_page="; if ($current_page_participant > 1): ?> <a href="<?php echo $page_link_base_participant . ($current_page_participant - 1) . $url_params_participant; ?>" class="btn btn-nav">Previous</a> <?php else: ?> <span class="btn btn-nav disabled">Previous</span> <?php endif; if ($total_pages_participant > 1) { $max_links = 5; $start_link = max(1, $current_page_participant - floor($max_links / 2)); $end_link = min($total_pages_participant, $start_link + $max_links - 1); if ($end_link == $total_pages_participant) $start_link = max(1, $total_pages_participant - $max_links + 1); if ($start_link > 1) { echo '<a href="'.$page_link_base_participant.'1'.$url_params_participant.'" class="btn btn-page">1</a>'; if ($start_link > 2) echo '<span class="btn btn-page disabled">...</span>'; } for ($i = $start_link; $i <= $end_link; $i++): echo '<a href="'.$page_link_base_participant.$i.$url_params_participant.'" class="btn btn-page '.($i == $current_page_participant ? 'active' : '').'">'.$i.'</a>'; endfor; if ($end_link < $total_pages_participant) { if ($end_link < $total_pages_participant - 1) echo '<span class="btn btn-page disabled">...</span>'; echo '<a href="'.$page_link_base_participant.$total_pages_participant.$url_params_participant.'" class="btn btn-page">'.$total_pages_participant.'</a>'; } } elseif ($total_participants > 0 && $total_pages_participant == 1) { echo '<a href="'.$page_link_base_participant.'1'.$url_params_participant.'" class="btn btn-page active">1</a>'; } if ($current_page_participant < $total_pages_participant): ?> <a href="<?php echo $page_link_base_participant . ($current_page_participant + 1) . $url_params_participant; ?>" class="btn btn-nav">Next</a> <?php else: ?> <span class="btn btn-nav disabled">Next</span> <?php endif; ?> </div> </div> <!-- <-- Changed Base URL -->
                 </section>
            </div>

            <!-- Letter Requests Tab -->
            <div class="tab-content <?php echo ($active_tab === 'letters') ? 'active' : ''; ?>" id="letters-tab">
                <!-- Letter Requests Stats -->
                <section class="stats-cards">
                    <?php if (!empty($letter_stats)): foreach ($letter_stats as $stat): ?>
                        <?php $insight = $stat['insight'] ?? []; ?>
                        <div class="stat-card" data-key="<?php echo htmlspecialchars($stat['key'] ?? ''); ?>">
                            <div class="card-header">
                                <h4><?php echo htmlspecialchars($stat['title'] ?? 'N/A'); ?></h4>
                                <div class="stat-card-icon icon-<?php echo htmlspecialchars(str_replace('fas fa-', '', $stat['icon'] ?? 'fa-chart-bar')); ?>">
                                    <i class="<?php echo htmlspecialchars($stat['icon'] ?? 'fas fa-chart-bar'); ?>"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="value"><?php echo htmlspecialchars($stat['value'] ?? 'N/A'); ?></p>
                                <?php if (isset($stat['insight']) && is_array($insight)): ?>
                                    <div class="stat-insight <?php echo htmlspecialchars($insight['color_class'] ?? 'text-neutral'); ?>">
                                        <i class="fas <?php echo htmlspecialchars($insight['icon_class'] ?? 'fa-minus'); ?>"></i>
                                        <span><?php echo htmlspecialchars($insight['text'] ?? 'No comparison data.'); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; else: ?>
                        <div class="stat-card">
                            <div class="card-header"><h4>Status</h4><div class="stat-card-icon icon-exclamation-circle"><i class="fas fa-exclamation-circle"></i></div></div>
                            <div class="card-body"><p class="value" style="font-size: 18px; color: var(--red-color);">Error</p><div class="stat-insight text-red"><i class="fas fa-exclamation-triangle"></i><span>Cannot load statistics.</span></div></div>
                        </div>
                    <?php endif; ?>
                </section>

                <section class="recent-letters card">
                    <div class="letters-header">
                        <h2><?php echo !empty($search_term_letter) || count(array_filter(array_intersect_key($_GET, array_flip(array_map(fn($c) => 'l_'.$c, $filter_columns_letter))))) > 0 ? 'Filtered Letter Requests' : 'Letter Requests'; ?></h2>
                        <button id="addLetterButton" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Letter
                        </button>
                    </div>
                    <div class="table-controls">
                        <form action="freewifi4all.php" method="get" id="resultsPerPageFormLetter" class="results-per-page-form">
                            <input type="hidden" name="tab" value="letters">
                            <?php if (!empty($search_term_letter)): ?>
                                <input type="hidden" name="l_search" value="<?php echo htmlspecialchars($search_term_letter); ?>">
                            <?php endif; ?>
                            <input type="hidden" name="l_page" value="1">
                            <?php foreach ($filter_columns_letter as $column): if (isset($_GET['l_'.$column]) && trim($_GET['l_'.$column]) !== ''): ?>
                                <input type="hidden" name="<?php echo 'l_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['l_'.$column])); ?>">
                            <?php endif; endforeach; ?>
                            <!-- Carry over activity params -->
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                            <input type="hidden" name="limit" value="<?php echo htmlspecialchars($_GET['limit'] ?? $default_results_per_page); ?>">
                            <input type="hidden" name="page" value="<?php echo htmlspecialchars($_GET['page'] ?? 1); ?>">
                            <input type="hidden" name="year" value="<?php echo $selected_report_year; ?>">
                            <!-- Carry over participant params -->
                            <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($_GET['p_search'] ?? ''); ?>">
                            <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($_GET['p_limit'] ?? $default_results_per_page); ?>">
                            <input type="hidden" name="p_page" value="<?php echo htmlspecialchars($_GET['p_page'] ?? 1); ?>">
                            <?php foreach ($filter_columns_activity as $column): if (isset($_GET[$column]) && trim($_GET[$column]) !== ''): ?>
                                <input type="hidden" name="<?php echo $column; ?>" value="<?php echo htmlspecialchars(trim($_GET[$column])); ?>">
                            <?php endif; endforeach; ?>
                            <?php foreach ($filter_columns_participant as $column): if (isset($_GET['p_'.$column]) && trim($_GET['p_'.$column]) !== ''): ?>
                                <input type="hidden" name="<?php echo 'p_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['p_'.$column])); ?>">
                            <?php endif; endforeach; ?>
                            <label for="resultsPerPageSelectLetter">Result per page:</label>
                            <select name="l_limit" id="resultsPerPageSelectLetter" onchange="this.form.submit();">
                                <?php foreach($results_per_page_options as $option): ?>
                                    <option value="<?php echo $option; ?>" <?php if($option == $results_per_page_letter) echo 'selected'; ?>><?php echo $option; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </form>
                        <div class="table-right-controls">
                            <form action="freewifi4all.php" method="get" class="search-form">
                                <div class="filter-group search-container">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" id="l_search" name="l_search" class="form-control search-input"
                                           placeholder="Search in actions, details..."
                                           value="<?php echo htmlspecialchars($search_term_letter); ?>">
                                    <!-- Pass current tab -->
                                    <input type="hidden" name="tab" value="letters">
                                    <!-- Pass current limit -->
                                    <input type="hidden" name="l_limit" value="<?php echo $results_per_page_letter; ?>">
                                    <!-- Pass filters -->
                                    <?php foreach ($filter_columns_letter as $column): ?>
                                        <?php if (isset($_GET['l_'.$column]) && trim($_GET['l_'.$column]) !== ''): ?>
                                            <input type="hidden" name="<?php echo 'l_'.$column; ?>" value="<?php echo htmlspecialchars(trim($_GET['l_'.$column])); ?>">
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </form>

                        </div>
                    </div>

                    <!-- Filter Dropdown Area -->
                    <div id="letterFilters" class="filter-dropdown-container" style="display: block;">
                        <form action="freewifi4all.php" method="get" class="filter-form">
                            <input type="hidden" name="tab" value="letters">
                            <!-- Carry over other params -->
                            <input type="hidden" name="l_search" value="<?php echo htmlspecialchars($search_term_letter); ?>">
                            <input type="hidden" name="l_limit" value="<?php echo htmlspecialchars($results_per_page_letter); ?>">
                            <input type="hidden" name="l_page" value="1">
                            <!-- Carry over activity params -->
                            <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                            <input type="hidden" name="limit" value="<?php echo htmlspecialchars($_GET['limit'] ?? $default_results_per_page); ?>">
                            <input type="hidden" name="page" value="<?php echo htmlspecialchars($_GET['page'] ?? 1); ?>">
                            <!-- Carry over participant params -->
                            <input type="hidden" name="p_search" value="<?php echo htmlspecialchars($_GET['p_search'] ?? ''); ?>">
                            <input type="hidden" name="p_limit" value="<?php echo htmlspecialchars($_GET['p_limit'] ?? $default_results_per_page); ?>">
                            <input type="hidden" name="p_page" value="<?php echo htmlspecialchars($_GET['p_page'] ?? 1); ?>">

                            <div class="filter-group">
                                <label for="filter_l_locality">Locality:</label>
                                <select name="l_locality" id="filter_l_locality">
                                    <option value="">All Localities</option>
                                    <?php
                                    // Get distinct localities from database
                                    $l_localities = [];
                                    if ($conn) {
                                        $l_locality_query = "SELECT DISTINCT locality FROM locationrequests WHERE locality IS NOT NULL AND locality != '' ORDER BY locality";
                                        $stmt_l_locality = mysqli_prepare($conn, $l_locality_query);
                                        if ($stmt_l_locality) {
                                            if (mysqli_stmt_execute($stmt_l_locality)) {
                                                $result_l_locality = mysqli_stmt_get_result($stmt_l_locality);
                                                while ($row = mysqli_fetch_assoc($result_l_locality)) {
                                                    if (!empty($row['locality'])) {
                                                        $l_localities[] = $row['locality'];
                                                    }
                                                }
                                                mysqli_free_result($result_l_locality);
                                            }
                                            mysqli_stmt_close($stmt_l_locality);
                                        }
                                    }

                                    foreach ($l_localities as $locality):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($locality); ?>" <?php echo (isset($_GET['l_locality']) && $_GET['l_locality'] == $locality) ? 'selected' : ''; ?>><?php echo htmlspecialchars($locality); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="filter_l_type">Type:</label>
                                <select name="l_type" id="filter_l_type">
                                    <option value="">All Types</option>
                                    <?php
                                    // Get distinct types from database
                                    $l_types = [];
                                    if ($conn) {
                                        $l_type_query = "SELECT DISTINCT type FROM locationrequests WHERE type IS NOT NULL AND type != '' ORDER BY type";
                                        $stmt_l_type = mysqli_prepare($conn, $l_type_query);
                                        if ($stmt_l_type) {
                                            if (mysqli_stmt_execute($stmt_l_type)) {
                                                $result_l_type = mysqli_stmt_get_result($stmt_l_type);
                                                while ($row = mysqli_fetch_assoc($result_l_type)) {
                                                    $l_types[] = $row['type'];
                                                }
                                                mysqli_free_result($result_l_type);
                                            }
                                            mysqli_stmt_close($stmt_l_type);
                                        }
                                    }
                                    foreach ($l_types as $option):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['l_type']) && $_GET['l_type'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="filter_l_status">Status:</label>
                                <select name="l_status" id="filter_l_status">
                                    <option value="">All Statuses</option>
                                    <?php
                                    // Get distinct statuses from database
                                    $l_statuses = [];
                                    if ($conn) {
                                        $l_status_query = "SELECT DISTINCT status FROM locationrequests WHERE status IS NOT NULL AND status != '' ORDER BY status";
                                        $stmt_l_status = mysqli_prepare($conn, $l_status_query);
                                        if ($stmt_l_status) {
                                            if (mysqli_stmt_execute($stmt_l_status)) {
                                                $result_l_status = mysqli_stmt_get_result($stmt_l_status);
                                                while ($row = mysqli_fetch_assoc($result_l_status)) {
                                                    $l_statuses[] = $row['status'];
                                                }
                                                mysqli_free_result($result_l_status);
                                            }
                                            mysqli_stmt_close($stmt_l_status);
                                        }
                                    }
                                    foreach ($l_statuses as $option):
                                    ?>
                                        <option value="<?php echo htmlspecialchars($option); ?>" <?php echo (isset($_GET['l_status']) && $_GET['l_status'] == $option) ? 'selected' : ''; ?>><?php echo htmlspecialchars($option); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="filter_l_year">Year:</label>
                                <select name="l_year" id="filter_l_year">
                                    <option value="">All Years</option>
                                    <?php
                                    // Get distinct years from database
                                    $l_years = [];
                                    if ($conn) {
                                        $l_year_query = "SELECT DISTINCT year FROM locationrequests WHERE year IS NOT NULL AND year != '' ORDER BY year DESC";
                                        $stmt_l_year = mysqli_prepare($conn, $l_year_query);
                                        if ($stmt_l_year) {
                                            if (mysqli_stmt_execute($stmt_l_year)) {
                                                $result_l_year = mysqli_stmt_get_result($stmt_l_year);
                                                while ($row = mysqli_fetch_assoc($result_l_year)) {
                                                    if (!empty($row['year'])) {
                                                        $l_years[] = $row['year'];
                                                    }
                                                }
                                                mysqli_free_result($result_l_year);
                                            }
                                            mysqli_stmt_close($stmt_l_year);
                                        }
                                    }

                                    // If no years found, use current year as fallback
                                    if (empty($l_years)) {
                                        $l_years[] = date('Y');
                                    }

                                    foreach ($l_years as $year):
                                    ?>
                                        <option value="<?php echo $year; ?>" <?php echo (isset($_GET['l_year']) && $_GET['l_year'] == $year) ? 'selected' : ''; ?>><?php echo $year; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="filter-actions">
                                <button type="submit" class="btn btn-primary btn-sm apply-filters-btn">Apply Filters</button>
                                <a href="freewifi4all.php?tab=letters&l_limit=<?php echo $results_per_page_letter; ?>" class="btn btn-secondary btn-sm clear-filters-btn">Clear Filters</a>
                            </div>
                        </form>
                    </div>

                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th class="col-checkbox"><input type="checkbox" id="selectAllCheckboxLetter" class="select-all-checkbox" data-type="letter" title="Select/Deselect All Letter Requests"></th>
                                    <th class="col-rownum">#</th>
                                    <th>Locality</th>
                                    <th>Barangay</th>
                                    <th>District</th>
                                    <th>Location</th>
                                    <th>Date</th>
                                    <th>Year</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Accomplished</th>
                                    <th>Remarks</th>
                                </tr>
                            </thead>
                            <tbody id="letterTableBody">
                                <?php if ($letters_fetch_error && !$letters_result): ?>
                                    <tr>
                                        <td colspan="<?php echo $table_colspan_letter; ?>" class="table-message error">
                                            <?php echo htmlspecialchars($letters_fetch_error); ?>
                                        </td>
                                    </tr>
                                <?php elseif ($letters_result && mysqli_num_rows($letters_result) > 0):
                                    $row_index_letter = 0;
                                    while ($letter = mysqli_fetch_assoc($letters_result)):
                                        $row_number_letter = $offset_letter + $row_index_letter + 1;
                                ?>
                                    <tr data-letter-id="<?php echo htmlspecialchars($letter['id']); ?>">
                                        <td class="col-checkbox"><input type="checkbox" class="row-checkbox letter-row-checkbox" name="letter_ids[]" value="<?php echo htmlspecialchars($letter['id']); ?>"></td>
                                        <td class="col-rownum"><?php echo $row_number_letter; ?></td>
                                        <td><?php echo htmlspecialchars($letter['locality'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($letter['barangay'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($letter['district'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($letter['location'] ?? ''); ?></td>
                                        <td><?php echo formatDateForDisplay($letter['date'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($letter['year'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($letter['type'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($letter['status'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($letter['accomplished'] ?? ''); ?></td>
                                        <td><?php echo nl2br(htmlspecialchars($letter['remarks'] ?? '')); ?></td>
                                    </tr>
                                <?php
                                    $row_index_letter++;
                                    endwhile;
                                else:
                                ?>
                                    <tr>
                                        <td colspan="<?php echo $table_colspan_letter; ?>" class="table-message">
                                            <?php echo htmlspecialchars($letters_fetch_error); ?>
                                        </td>
                                    </tr>
                                <?php endif;
                                if ($letters_result && is_object($letters_result)) mysqli_free_result($letters_result);
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="pagination-controls">
                        <div class="pagination-info">
                            <?php
                            if ($total_letters > 0) {
                                echo "Showing " . ($offset_letter + 1) . " - " . min($offset_letter + $results_per_page_letter, $total_letters) . " of " . number_format($total_letters);
                            } else {
                                echo "Showing 0 - 0 of 0";
                            }
                            ?>
                        </div>
                        <div class="pagination-nav">
                            <?php
                            $page_link_base_letter = "freewifi4all.php?l_page=";
                            if ($current_page_letter > 1):
                            ?>
                                <a href="<?php echo $page_link_base_letter . ($current_page_letter - 1) . $url_params_letter; ?>" class="btn btn-nav">Previous</a>
                            <?php else: ?>
                                <span class="btn btn-nav disabled">Previous</span>
                            <?php endif;

                            if ($total_pages_letter > 1) {
                                $max_links = 5;
                                $start_link = max(1, $current_page_letter - floor($max_links / 2));
                                $end_link = min($total_pages_letter, $start_link + $max_links - 1);

                                if ($end_link == $total_pages_letter) {
                                    $start_link = max(1, $total_pages_letter - $max_links + 1);
                                }

                                if ($start_link > 1) {
                                    echo '<a href="'.$page_link_base_letter.'1'.$url_params_letter.'" class="btn btn-page">1</a>';
                                    if ($start_link > 2) {
                                        echo '<span class="btn btn-page disabled">...</span>';
                                    }
                                }

                                for ($i = $start_link; $i <= $end_link; $i++):
                                    echo '<a href="'.$page_link_base_letter.$i.$url_params_letter.'" class="btn btn-page '.($i == $current_page_letter ? 'active' : '').'">'.$i.'</a>';
                                endfor;

                                if ($end_link < $total_pages_letter) {
                                    if ($end_link < $total_pages_letter - 1) {
                                        echo '<span class="btn btn-page disabled">...</span>';
                                    }
                                    echo '<a href="'.$page_link_base_letter.$total_pages_letter.$url_params_letter.'" class="btn btn-page">'.$total_pages_letter.'</a>';
                                }
                            } elseif ($total_letters > 0 && $total_pages_letter == 1) {
                                echo '<a href="'.$page_link_base_letter.'1'.$url_params_letter.'" class="btn btn-page active">1</a>';
                            }

                            if ($current_page_letter < $total_pages_letter):
                            ?>
                                <a href="<?php echo $page_link_base_letter . ($current_page_letter + 1) . $url_params_letter; ?>" class="btn btn-nav">Next</a>
                            <?php else: ?>
                                <span class="btn btn-nav disabled">Next</span>
                            <?php endif;
                            ?>
                        </div>
                    </div>
                </section>
            </div>

             <!-- Reports Tab -->
            <div class="tab-content <?php echo ($active_tab === 'reports') ? 'active' : ''; ?>" id="reports-tab">
                <section class="card">
                    <div class="card-header" style="padding-bottom: 10px;">
                        <div class="report-header-container" style="margin-bottom: 0;">
                            <h2>Free Wi-Fi Access Points Report</h2>
                        </div>
                    </div>
                    <div class="card-body" style="padding-top: 5px;">
                        <div class="fw4a-report-container">
                            <!-- Left column: Strategy visualization with statistics -->
                            <div class="fw4a-info-column">
                                <div class="fw4a-version-info">
                                    <div class="fw4a-stats-header">
                                        <div class="fw4a-big-number" id="fw4aBigNumber">
                                            <span id="totalAccessPointsCount">-</span>
                                        </div>
                                        <div class="fw4a-stats-title">
                                            <h3># of Access Points</h3>
                                            <div class="fw4a-stats-summary">
                                                <span id="activeAccessPointsCount">-</span> Active <span class="stats-divider">|</span> <span id="inactiveAccessPointsCount">-</span> Inactive
                                            </div>
                                        </div>
                                        <div class="fw4a-stats-illustration">
                                            <div class="wifi-icon">
                                                <span class="wifi-circle"></span>
                                                <span class="wifi-wave wave1"></span>
                                                <span class="wifi-wave wave2"></span>
                                                <span class="wifi-wave wave3"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="fw4a-strategy-stats" id="fw4aStrategyStats">
                                        <!-- Strategy statistics will be dynamically generated here -->
                                    </div>

                                    <div class="fw4a-description">
                                        <p>The Free Wi-Fi for All Program provides internet access points across different locations using various deployment strategies.</p>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>
    <!-- Action Bar -->
    <div id="selectionActionBar"> <span id="selectedCount">0 items selected</span> <div class="action-buttons"> <button id="editButton" class="btn btn-secondary" disabled title="Edit selected (1 item only)"><i class="fas fa-edit"></i> Edit</button> <button id="deleteButton" class="btn btn-delete" disabled title="Delete selected items"><i class="fas fa-trash"></i> Delete</button> <button id="uploadButton" class="btn btn-secondary" disabled title="Upload files for selected item(s)"><i class="fas fa-upload"></i> Upload Files</button> <button id="viewFilesButton" class="btn btn-secondary" disabled title="View files for selected item(s)"><i class="fas fa-folder-open"></i> View Files</button> </div> <button class="close-action-bar" title="Deselect All & Close">×</button> </div>

    <!-- ========= -->
    <!-- MODALS    -->
    <!-- ========= -->

    <!-- Add Activity Modal -->
    <div id="addModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2>Add New Activity</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <form id="addActivityForm"> <div class="form-grid"> <div class="form-field half-width"> <label for="addActStart">Start Date:</label> <input type="date" id="addActStart" name="start"> </div> <div class="form-field half-width"> <label for="addActEnd">End Date:</label> <input type="date" id="addActEnd" name="end"> </div> <div class="form-field half-width"> <label for="addActProject">Project:</label> <input type="text" id="addActProject" name="project" value="<?php echo htmlspecialchars($project_filter_value); ?>" readonly> </div> <div class="form-field half-width"> <label for="addActSubproject">Subproject:</label> <input type="text" id="addActSubproject" name="subproject"> </div> <div class="form-field full-width"> <label for="addActActivityName">Activity Name:</label> <input type="text" id="addActActivityName" name="activity" required> </div> <div class="form-field full-width"> <label for="addActIndicator">Indicator:</label> <input type="text" id="addActIndicator" name="indicator"> </div> <div class="form-field quarter-width"> <label for="addActMunicipality">Municipality:</label> <input type="text" id="addActMunicipality" name="municipality"> </div> <div class="form-field quarter-width"> <label for="addActDistrict">District:</label> <input type="text" id="addActDistrict" name="district"> </div> <div class="form-field quarter-width"> <label for="addActBarangay">Barangay:</label> <input type="text" id="addActBarangay" name="barangay"> </div> <div class="form-field quarter-width"> <label for="addActTraining">Training Venue:</label> <input type="text" id="addActTraining" name="training"> </div> <div class="form-field half-width"> <label for="addActAgency">Requesting Agency:</label> <input type="text" id="addActAgency" name="agency"> </div> <div class="form-field half-width"> <label for="addActMode">Mode:</label> <input type="text" id="addActMode" name="mode"> </div> <div class="form-field half-width"> <label for="addActSector">Target Sector:</label> <input type="text" id="addActSector" name="sector"> </div> <div class="form-field half-width"> <label for="addActApproved">Approved AD?:</label> <input type="text" id="addActApproved" name="approved"> </div> <div class="form-field half-width"> <label for="addActPerson">Responsible Person(s):</label> <input type="text" id="addActPerson" name="person"> </div> <div class="form-field half-width"> <label for="addActResource">Resource Person(s):</label> <input type="text" id="addActResource" name="resource"> </div> <div class="form-field quarter-width"> <label for="addActParticipants">Participants:</label> <input type="number" id="addActParticipants" name="participants" min="0" step="1" value="0"> </div> <div class="form-field quarter-width"> <label for="addActCompleters">Completers:</label> <input type="number" id="addActCompleters" name="completers" min="0" step="1" value="0"> </div> <div class="form-field quarter-width"> <label for="addActMale">Male:</label> <input type="number" id="addActMale" name="male" min="0" step="1" value="0"> </div> <div class="form-field quarter-width"> <label for="addActFemale">Female:</label> <input type="number" id="addActFemale" name="female" min="0" step="1" value="0"> </div> <div class="form-field full-width"> <label for="addActMov">Link to MOVs:</label> <input type="text" id="addActMov" name="mov"> </div> <div class="form-field remarks-field"> <label for="addActRemarks">Remarks:</label> <textarea id="addActRemarks" name="remarks" rows="3"></textarea> </div> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="addModal">Cancel</button> <button type="submit" form="addActivityForm" class="btn btn-primary" id="saveAddActivityButton">Save Activity</button> </div> </div> </div>
    <!-- Edit Activity Modal -->
    <div id="editModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2 id="editActivityModalTitle">Edit Activity</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <div class="loading-indicator" id="editActivityLoadingIndicator"> <i class="fas fa-spinner fa-spin"></i> Loading activity data... </div> <div class="error-indicator" id="editActivityErrorIndicator" style="display: none;"> Error loading data. </div> <form id="editActivityForm" style="display: none;"> <input type="hidden" id="editActivityFormId" name="id"> <div class="form-grid"> <div class="form-field half-width"> <label for="editActStart">Start Date:</label> <input type="date" id="editActStart" name="start"> </div> <div class="form-field half-width"> <label for="editActEnd">End Date:</label> <input type="date" id="editActEnd" name="end"> </div> <div class="form-field half-width"> <label for="editActProject">Project:</label> <input type="text" id="editActProject" name="project" readonly> </div> <div class="form-field half-width"> <label for="editActSubproject">Subproject:</label> <input type="text" id="editActSubproject" name="subproject"> </div> <div class="form-field full-width"> <label for="editActActivityName">Activity Name:</label> <input type="text" id="editActActivityName" name="activity"> </div> <div class="form-field full-width"> <label for="editActIndicator">Indicator:</label> <input type="text" id="editActIndicator" name="indicator"> </div> <div class="form-field quarter-width"> <label for="editActMunicipality">Municipality:</label> <input type="text" id="editActMunicipality" name="municipality"> </div> <div class="form-field quarter-width"> <label for="editActDistrict">District:</label> <input type="text" id="editActDistrict" name="district"> </div> <div class="form-field quarter-width"> <label for="editActBarangay">Barangay:</label> <input type="text" id="editActBarangay" name="barangay"> </div> <div class="form-field quarter-width"> <label for="editActTraining">Training Venue:</label> <input type="text" id="editActTraining" name="training"> </div> <div class="form-field half-width"> <label for="editActAgency">Requesting Agency:</label> <input type="text" id="editActAgency" name="agency"> </div> <div class="form-field half-width"> <label for="editActMode">Mode:</label> <input type="text" id="editActMode" name="mode"> </div> <div class="form-field half-width"> <label for="editActSector">Target Sector:</label> <input type="text" id="editActSector" name="sector"> </div> <div class="form-field half-width"> <label for="editActApproved">Approved AD?:</label> <input type="text" id="editActApproved" name="approved"> </div> <div class="form-field half-width"> <label for="editActPerson">Responsible Person(s):</label> <input type="text" id="editActPerson" name="person"> </div> <div class="form-field half-width"> <label for="editActResource">Resource Person(s):</label> <input type="text" id="editActResource" name="resource"> </div> <div class="form-field quarter-width"> <label for="editActParticipants">Participants:</label> <input type="number" id="editActParticipants" name="participants" min="0" step="1" placeholder="0"> </div> <div class="form-field quarter-width"> <label for="editActCompleters">Completers:</label> <input type="number" id="editActCompleters" name="completers" min="0" step="1" placeholder="0"> </div> <div class="form-field quarter-width"> <label for="editActMale">Male:</label> <input type="number" id="editActMale" name="male" min="0" step="1" placeholder="0"> </div> <div class="form-field quarter-width"> <label for="editActFemale">Female:</label> <input type="number" id="editActFemale" name="female" min="0" step="1" placeholder="0"> </div> <div class="form-field full-width"> <label for="editActMov">Link to MOVs:</label> <input type="text" id="editActMov" name="mov"> </div> <div class="form-field remarks-field"> <label for="editActRemarks">Remarks:</label> <textarea id="editActRemarks" name="remarks" rows="3"></textarea> </div> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="editModal">Cancel</button> <button type="submit" form="editActivityForm" class="btn btn-primary" id="saveEditActivityButton" disabled>Save Changes</button> </div> </div> </div>
    <!-- Add LGU Modal -->
    <div id="addParticipantModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2>Add New LGU</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <form id="addParticipantForm"> <div class="form-grid"> <div class="form-field half-width"> <label for="addPartLocality">Locality:</label> <input type="text" id="addPartLocality" name="locality" required> </div> <div class="form-field half-width"> <label for="addPartBarangay">Barangay:</label> <input type="text" id="addPartBarangay" name="barangay"> </div> <div class="form-field half-width"> <label for="addPartDistrict">District:</label> <input type="text" id="addPartDistrict" name="district"> </div> <div class="form-field half-width"> <label for="addPartLocations">Locations:</label> <input type="text" id="addPartLocations" name="locations"> </div> <div class="form-field half-width"> <label for="addPartType">Type:</label> <input type="text" id="addPartType" name="type"> </div> <div class="form-field half-width"> <label for="addPartCode">Code:</label> <input type="text" id="addPartCode" name="code"> </div> <div class="form-field half-width"> <label for="addPartStrategy">Strategy:</label> <input type="text" id="addPartStrategy" name="strategy"> </div> <div class="form-field half-width"> <label for="addPartStatus">Status:</label> <input type="text" id="addPartStatus" name="status"> </div> <div class="form-field half-width"> <label for="addPartReason">Reason:</label> <input type="text" id="addPartReason" name="reason"> </div> <div class="form-field half-width"> <label for="addPartRemarks">Remarks:</label> <input type="text" id="addPartRemarks" name="remarks"> </div> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="addParticipantModal">Cancel</button> <button type="submit" form="addParticipantForm" class="btn btn-primary" id="saveAddParticipantButton">Save LGU</button> </div> </div> </div>
    <!-- Edit LGU Modal -->
    <div id="editParticipantModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2 id="editParticipantModalTitle">Edit LGU</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <div class="loading-indicator" id="editParticipantLoadingIndicator"> <i class="fas fa-spinner fa-spin"></i> Loading data... </div> <div class="error-indicator" id="editParticipantErrorIndicator" style="display: none;"> Error loading data. </div> <form id="editParticipantForm" style="display: none;"> <input type="hidden" id="editParticipantFormId" name="id"> <div class="form-grid"> <div class="form-field half-width"> <label for="editPartLocality">Locality:</label> <input type="text" id="editPartLocality" name="locality" required> </div> <div class="form-field half-width"> <label for="editPartBarangay">Barangay:</label> <input type="text" id="editPartBarangay" name="barangay"> </div> <div class="form-field half-width"> <label for="editPartDistrict">District:</label> <input type="text" id="editPartDistrict" name="district"> </div> <div class="form-field half-width"> <label for="editPartLocations">Locations:</label> <input type="text" id="editPartLocations" name="locations"> </div> <div class="form-field half-width"> <label for="editPartType">Type:</label> <input type="text" id="editPartType" name="type"> </div> <div class="form-field half-width"> <label for="editPartCode">Code:</label> <input type="text" id="editPartCode" name="code"> </div> <div class="form-field half-width"> <label for="editPartStrategy">Strategy:</label> <input type="text" id="editPartStrategy" name="strategy"> </div> <div class="form-field half-width"> <label for="editPartStatus">Status:</label> <input type="text" id="editPartStatus" name="status"> </div> <div class="form-field half-width"> <label for="editPartReason">Reason:</label> <input type="text" id="editPartReason" name="reason"> </div> <div class="form-field half-width"> <label for="editPartRemarks">Remarks:</label> <input type="text" id="editPartRemarks" name="remarks"> </div> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="editParticipantModal">Cancel</button> <button type="submit" form="editParticipantForm" class="btn btn-primary" id="saveEditParticipantButton" disabled>Save Changes</button> </div> </div> </div>

    <!-- Edit Letter Modal -->
    <div id="editLetterModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="editLetterModalTitle">Edit Letter Request</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <div class="loading-indicator" id="editLetterLoadingIndicator">
                    <i class="fas fa-spinner fa-spin"></i> Loading data...
                </div>
                <div class="error-indicator" id="editLetterErrorIndicator" style="display: none;">
                    Error loading data.
                </div>
                <form id="editLetterForm" style="display: none;">
                    <input type="hidden" id="editLetterFormId" name="id">
                    <div class="form-grid">
                        <div class="form-field half-width">
                            <label for="editLetterLocality">Locality:</label>
                            <input type="text" id="editLetterLocality" name="locality" required>
                        </div>
                        <div class="form-field half-width">
                            <label for="editLetterBarangay">Barangay:</label>
                            <input type="text" id="editLetterBarangay" name="barangay">
                        </div>
                        <div class="form-field half-width">
                            <label for="editLetterDistrict">District:</label>
                            <input type="text" id="editLetterDistrict" name="district">
                        </div>
                        <div class="form-field half-width">
                            <label for="editLetterLocation">Location:</label>
                            <input type="text" id="editLetterLocation" name="location">
                        </div>
                        <div class="form-field half-width">
                            <label for="editLetterDate">Date:</label>
                            <input type="date" id="editLetterDate" name="date">
                        </div>
                        <div class="form-field half-width">
                            <label for="editLetterYear">Year:</label>
                            <input type="number" id="editLetterYear" name="year" min="2000" max="2100">
                        </div>
                        <div class="form-field half-width">
                            <label for="editLetterType">Type:</label>
                            <input type="text" id="editLetterType" name="type">
                        </div>
                        <div class="form-field half-width">
                            <label for="editLetterStatus">Status:</label>
                            <input type="text" id="editLetterStatus" name="status">
                        </div>
                        <div class="form-field half-width">
                            <label for="editLetterAccomplished">Accomplished:</label>
                            <input type="text" id="editLetterAccomplished" name="accomplished">
                        </div>
                        <div class="form-field half-width">
                            <label for="editLetterRemarks">Remarks:</label>
                            <input type="text" id="editLetterRemarks" name="remarks">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="editLetterModal">Cancel</button>
                <button type="submit" form="editLetterForm" class="btn btn-primary" id="saveEditLetterButton" disabled>Save Changes</button>
            </div>
        </div>
    </div>
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal"> <div class="modal-content small"> <div class="modal-header"> <h2 id="deleteModalTitle">Confirm Deletion</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <p>Are you sure you want to delete the selected <strong id="deleteItemType">item(s)</strong> (<strong id="deleteItemCount">0</strong>)?</p> <p id="deleteFileWarning" style="display: none; color: var(--text-medium); margin-top: 10px;">This will permanently remove the selected file(s).</p> <p id="deleteItemWarning" style="display: block; color: var(--red-color); margin-top: 15px;"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="deleteModal">Cancel</button> <button type="button" id="confirmDeleteButton" class="btn btn-danger">Delete</button> </div> </div> </div>
    <!-- Upload Files Modal -->
    <div id="uploadModal" class="modal"> <div class="modal-content"> <div class="modal-header"> <h2 id="uploadModalTitle">Upload Files</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body"> <p>Select files to associate with the <strong id="uploadItemCount">0</strong> selected <strong id="uploadItemType">item(s)</strong>:</p> <form id="uploadForm" enctype="multipart/form-data"> <div class="form-group"> <label for="fileInput">Choose files:</label> <input type="file" id="fileInput" name="files[]" multiple required style="padding: 10px; border: 1px dashed #ccc; background-color: #fdfdfd; display: block; width: 100%;"> <p style="margin-top: 5px; font-size: 12px; color: var(--text-light);">(Max size: 10MB per file. Allowed types: PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG)</p> <p id="uploadTypeInfo" style="margin-top: 5px; font-size: 12px; color: var(--text-light);"><i class="fas fa-info-circle"></i> Files will be associated with the selected items.</p> </div> </form> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="uploadModal">Cancel</button> <button type="submit" form="uploadForm" class="btn btn-primary" id="submitUploadButton"> <i class="fas fa-upload"></i> Upload </button> </div> </div> </div>
    <!-- View Files Modal -->
    <div id="viewFilesModal" class="modal large"> <div class="modal-content"> <div class="modal-header"> <h2 id="viewFilesModalTitle">View Associated Files</h2> <button class="close-modal" title="Close">×</button> </div> <div class="modal-body" id="viewFilesModalBody"> <div class="view-files-header"> <p id="viewFilesParentItemInfo">Files for 0 selected item(s):</p> <div class="view-files-controls"> <label class="checkbox-label"> <input type="checkbox" id="viewFilesSelectAll" disabled> Select All </label> <button id="viewFilesDeleteSelected" class="btn btn-delete btn-sm" disabled> <i class="fas fa-trash"></i> Delete Selected </button> </div> </div> <div id="fileListContainer" class="grouped"> <div class="loading-indicator active" id="viewLoadingIndicator"> <i class="fas fa-spinner fa-spin"></i> Loading file list... </div> <div id="fileGroupContainer"> <!-- Groups injected by JS --> </div> <div id="viewFilesEmptyMessage" class="empty-message" style="display: none;"> No files associated with the selected item(s). </div> <div id="viewFilesErrorMessage" class="error-message" style="display: none;"> <i class="fas fa-exclamation-triangle"></i> Error loading files. </div> </div> </div> <div class="modal-footer"> <button type="button" class="btn btn-secondary close-modal" data-modal-id="viewFilesModal">Close</button> </div> </div> </div>
    <!-- Export Modal -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="exportModalTitle"><i class="fas fa-file-export"></i> Export Data</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <h4>What would you like to export?</h4>
                <div style="margin: 20px 0;">
                    <!-- Option 1: All -->
                    <div class="export-option" style="margin-bottom: 15px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 4px; cursor: pointer;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="exportType" value="all" checked style="margin-right: 10px;">
                            <div style="display: flex; align-items: center;">
                                <div style="background-color: #e3f2fd; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                    <i class="fas fa-database" style="color: #2196f3; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <strong id="exportAllLabel">All items</strong>
                                    <div style="font-size: 13px; color: #666;">Export the complete dataset for the current tab</div>
                                </div>
                            </div>
                        </label>
                    </div>
                    <!-- Option 2: Filtered -->
                    <div class="export-option" style="margin-bottom: 15px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 4px; cursor: pointer;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="exportType" value="filtered" style="margin-right: 10px;">
                            <div style="display: flex; align-items: center;">
                                <div style="background-color: #fff8e1; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                    <i class="fas fa-filter" style="color: #ffc107; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <strong id="exportFilteredLabel">Filtered items</strong>
                                    <div id="filteredItemsCount" style="font-size: 13px; color: #666;">0 items in current filter</div>
                                </div>
                            </div>
                        </label>
                    </div>
                    <!-- Option 3: Selected -->
                    <div class="export-option" style="margin-bottom: 15px; padding: 15px; border: 1px solid #e0e0e0; border-radius: 4px; cursor: pointer;">
                        <label style="display: flex; align-items: center; cursor: pointer;">
                            <input type="radio" name="exportType" value="selected" style="margin-right: 10px;" disabled>
                            <div style="display: flex; align-items: center;">
                                <div style="background-color: #e8f5e9; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                    <i class="fas fa-check" style="color: #4caf50; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <strong id="exportSelectedLabel">Selected items</strong>
                                    <div id="selectedItemsCount" style="font-size: 13px; color: #666;">0 items selected</div>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
                <h4>Export format</h4>
                <div style="margin: 20px 0; padding: 15px; border: 1px solid #e0e0e0; border-radius: 4px;">
                    <label style="display: flex; align-items: center;">
                        <input type="radio" name="exportFormat" value="csv" checked style="margin-right: 10px;">
                        <div style="display: flex; align-items: center;">
                            <div style="margin-right: 15px;">
                                <i class="fas fa-file-csv" style="color: #607d8b; font-size: 24px;"></i>
                            </div>
                            <div>
                                <strong>CSV</strong>
                                <div style="font-size: 13px; color: #666;">Compatible with Excel, Google Sheets, etc.</div>
                            </div>
                        </div>
                    </label>
                </div>
                <div id="exportProcessingIndicator" class="loading-indicator" style="display: none; margin-top: 15px;">
                    <i class="fas fa-spinner fa-spin"></i> Preparing export...
                </div>
                <div id="exportErrorIndicator" class="error-indicator" style="display: none; margin-top: 15px;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="exportModal">Cancel</button>
                <button type="button" id="exportDataButton" class="btn btn-primary">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>
        </div>
    </div>
    <!-- Import Modal (Unified) -->
    <div id="importModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2 id="importModalTitle"><i class="fas fa-file-import"></i> Import Data</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <div id="importSteps">
                    <div id="importStep1">
                        <h4>Step 1: Upload CSV File</h4>
                        <p id="importInstructions">Select a CSV file containing the data to import.</p>
                        <div class="file-upload-area">
                            <input type="file" id="csvFileInput" accept=".csv" style="display: none;">
                            <label for="csvFileInput" class="btn btn-secondary">
                                <i class="fas fa-upload"></i> Choose File
                            </label>
                            <span id="fileNameDisplay" class="file-name-display">No file chosen</span>
                        </div>
                        <small style="color: var(--text-light); display: block; margin-top: 5px;">Ensure the first row contains the exact header names.</small>
                        <div id="importRequiredColumnsInfo" class="required-columns-info" style="margin-top: 15px;">
                            <p><i class="fas fa-info-circle"></i> <strong>Required Columns:</strong></p>
                            <div id="importRequiredColumnsList" class="column-tags">
                                <!-- Columns will be populated by JavaScript -->
                            </div>
                            <p style="margin-top: 10px; font-size: 12px; color: var(--text-light);"><i class="fas fa-exclamation-circle"></i> <strong>Note:</strong> For Activities, the 'Bureau' column must match '<?php echo htmlspecialchars($project_filter_value); ?>' and will be validated during import.</p> <!-- <-- Updated project name -->
                        </div>
                        <div id="importErrorStep1" class="error-indicator" style="display: none; margin-top: 15px;"></div>
                    </div>
                    <div id="importStep2" style="display: none;">
                        <h4 style="margin-top: 20px;">Step 2: Preview Data</h4>
                        <p>Review the first few rows.</p>
                        <div id="previewLoadingIndicator" class="loading-indicator" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i> Processing file...
                        </div>
                        <div id="importErrorStep2" class="error-indicator" style="display: none;"></div>
                        <div id="previewTableContainer" class="table-container" style="max-height: 300px; overflow-y: auto; margin-top: 10px; border: 1px solid var(--border-color); display: none;">
                            <table id="previewTable">
                                <thead></thead>
                                <tbody></tbody>
                            </table>
                            <p id="previewRowCount" style="text-align: center; margin-top: 10px; font-size: 12px; color: var(--text-light);"></p>
                        </div>
                    </div>
                </div>
                <div id="importProcessingIndicator" class="loading-indicator" style="display: none; margin-top: 15px;">
                    <i class="fas fa-spinner fa-spin"></i> Importing data...
                </div>
                <div id="importResultIndicator" style="display: none; margin-top: 15px; padding: 10px; border-radius: 4px; text-align: center;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="importModal">Cancel</button>
                <button type="button" id="previewDataButton" class="btn btn-secondary" disabled>
                    <i class="fas fa-eye"></i> Preview Data
                </button>
                <button type="button" id="importDataButton" class="btn btn-primary" disabled>
                    <i class="fas fa-check"></i> Import Data
                </button>
            </div>
        </div>
    </div>

    <!-- Add Letter Modal -->
    <div id="addLetterModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add New Letter Request</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <form id="addLetterForm">
                    <div class="form-grid">
                        <div class="form-field half-width">
                            <label for="addLetterLocality">Locality:</label>
                            <input type="text" id="addLetterLocality" name="locality" required>
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterBarangay">Barangay:</label>
                            <input type="text" id="addLetterBarangay" name="barangay">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterDistrict">District:</label>
                            <input type="text" id="addLetterDistrict" name="district">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterLocation">Location:</label>
                            <input type="text" id="addLetterLocation" name="location">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterDate">Date:</label>
                            <input type="date" id="addLetterDate" name="date">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterYear">Year:</label>
                            <input type="number" id="addLetterYear" name="year" min="2000" max="2100" value="<?php echo date('Y'); ?>">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterType">Type:</label>
                            <input type="text" id="addLetterType" name="type">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterStatus">Status:</label>
                            <input type="text" id="addLetterStatus" name="status">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterAccomplished">Accomplished:</label>
                            <input type="text" id="addLetterAccomplished" name="accomplished">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterRemarks">Remarks:</label>
                            <input type="text" id="addLetterRemarks" name="remarks">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="addLetterModal">Cancel</button>
                <button type="submit" form="addLetterForm" class="btn btn-primary" id="saveAddLetterButton"><i class="fas fa-save"></i> Save Letter</button>
            </div>
        </div>
    </div>

    <!-- Manage Targets Modal -->
    <div id="manageTargetsModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2><i class="fas fa-bullseye"></i> Manage <?php echo htmlspecialchars($target_category_filter); ?> Targets</h2> <!-- <-- Changed Title -->
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <!-- Existing Targets Table -->
                <h4>Existing Targets</h4>
                 <div class="loading-indicator" id="targetsLoadingIndicator" style="display: none; padding: 20px;"> <i class="fas fa-spinner fa-spin"></i> Loading targets... </div>
                 <div class="error-indicator" id="targetsErrorIndicator" style="display: none; margin-bottom: 15px;"> Error loading targets. </div>
                 <div id="existingTargetsTableContainer">
                    <table id="existingTargetsTable">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Subcategory</th>
                                <th>Indicator</th>
                                <th>Year</th>
                                <th>Target Activities</th>
                                <th>Target Participants</th>
                                <th class="actions-col">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="targetsTableBody">
                            <!-- Target rows will be inserted here by JavaScript -->
                             <tr><td colspan="6" class="table-message">No targets found or loaded yet.</td></tr>
                        </tbody>
                    </table>
                 </div>

                <!-- Add/Edit Target Form -->
                <div id="addTargetFormContainer">
                    <h4 id="targetFormTitle">Add New Target</h4>
                    <form id="addTargetForm">
                        <input type="hidden" id="targetEditId" name="id" value="">
                        <div class="form-grid">
                            <div class="form-field category-field">
                                <label for="targetCategory">Category <span class="required">*</span></label>
                                <input type="text" id="targetCategory" name="category" value="<?php echo htmlspecialchars($target_category_filter); ?>" readonly> <!-- <-- Changed Default -->
                            </div>
                            <div class="form-field subcategory-field">
                                <label for="targetSubcategory">Subcategory <span class="required">*</span></label>
                                <input type="text" id="targetSubcategory" name="subcategory" placeholder="e.g., Capacity Development" required>
                            </div>
                             <div class="form-field indicator-field">
                                <label for="targetIndicator">Indicator <span class="required">*</span></label>
                                <input type="text" id="targetIndicator" name="indicator" placeholder="Indicator name from Activity table" required>
                                <small>Must match 'Indicator' in Activities table.</small>
                            </div>
                            <div class="form-field year-field">
                                <label for="targetYear">Year <span class="required">*</span></label>
                                <input type="number" id="targetYear" name="year" min="2000" max="2100" step="1" value="<?php echo htmlspecialchars($selected_report_year); ?>" required>
                            </div>
                            <div class="form-field target-field">
                                <label for="targetValue">Target Activities <span class="required">*</span></label>
                                <input type="number" id="targetValue" name="target" min="0" step="1" placeholder="e.g., 50" required>
                            </div>
                            <div class="form-field target-field">
                                <label for="targetParticipantsValue">Target Participants</label>
                                <input type="number" id="targetParticipantsValue" name="target_participants" min="0" step="1" placeholder="e.g., 100">
                                <small>Optional. Leave empty if not tracking participants.</small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer target-modal-footer">
                <div class="left-buttons">
                     <button type="button" class="btn btn-secondary" id="resetTargetFormButton">
                        <i class="fas fa-undo"></i> Reset Form
                    </button>
                </div>
                <div class="right-buttons">
                     <button type="button" class="btn btn-secondary close-modal" data-modal-id="manageTargetsModal">Close</button>
                    <button type="submit" form="addTargetForm" class="btn btn-primary" id="saveTargetButton">
                        <i class="fas fa-save"></i> Save Target
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- AJAX Endpoint URL -->
    <script> const ajaxHandlerUrl = 'ajax_handler.php'; </script>

    <!-- Core JavaScript -->
    <script>
       document.addEventListener('DOMContentLoaded', () => {
         console.log("Free Wifi for All Dashboard loaded. Initializing UI..."); // <-- Changed Log
         // --- Element References ---
         const selectAllCheckboxActivity = document.getElementById('selectAllCheckboxActivity');
         const selectAllCheckboxParticipant = document.getElementById('selectAllCheckboxParticipant');
         const selectAllCheckboxLetter = document.getElementById('selectAllCheckboxLetter');
         const activityRowCheckboxes = document.querySelectorAll('.activity-row-checkbox');
         const participantRowCheckboxes = document.querySelectorAll('.participant-row-checkbox');
         const letterRowCheckboxes = document.querySelectorAll('.letter-row-checkbox');
         const selectionActionBar = document.getElementById('selectionActionBar');
         const selectedCountSpan = document.getElementById('selectedCount');
         const editButton = document.getElementById('editButton');
         const deleteButton = document.getElementById('deleteButton'); // Main delete button
         const uploadButton = document.getElementById('uploadButton');
         const viewFilesButton = document.getElementById('viewFilesButton');
         const closeActionBarButton = selectionActionBar?.querySelector('.close-action-bar');
         const addActivityButton = document.getElementById('addActivityButton');
         const addParticipantButton = document.getElementById('addParticipantButton');
         const notificationElement = document.getElementById('notification');
         const notificationMessageElement = document.getElementById('notificationMessage');
         const tabs = document.querySelectorAll('.nav-tab');
         const tabContents = document.querySelectorAll('.tab-content');
         const exportButton = document.getElementById('exportButton'); // Might be null if tab is reports
         const importButton = document.getElementById('importButton'); // Might be null if tab is reports
         const manageTargetsButton = document.getElementById('manageTargetsButton');
         const reportGraphContainer = document.getElementById('reportGraphContainer');
         const reportLoadingIndicator = document.getElementById('reportLoadingIndicator');
         const reportYearFilter = document.getElementById('reportYearFilter'); // NEW Year Filter

         // --- Modal References ---
         const modals = {
             addActivity: document.getElementById('addModal'),
             editActivity: document.getElementById('editModal'),
             addParticipant: document.getElementById('addParticipantModal'),
             editParticipant: document.getElementById('editParticipantModal'),
             upload: document.getElementById('uploadModal'),
             view: document.getElementById('viewFilesModal'),
             delete: document.getElementById('deleteModal'),
             import: document.getElementById('importModal'),
             export: document.getElementById('exportModal'),
             manageTargets: document.getElementById('manageTargetsModal'),
             strategyTable: document.getElementById('strategyTableModal'),
             addLetter: document.getElementById('addLetterModal'),
             editLetter: document.getElementById('editLetterModal'),
             statsBreakdown: document.getElementById('statsBreakdownModal')
         };
         // --- Manage Targets Modal Elements ---
         const targetsLoadingIndicator = document.getElementById('targetsLoadingIndicator');
         const targetsErrorIndicator = document.getElementById('targetsErrorIndicator');
         const targetsTableBody = document.getElementById('targetsTableBody');
         const targetForm = document.getElementById('addTargetForm');
         const targetFormTitle = document.getElementById('targetFormTitle');
         const targetEditIdInput = document.getElementById('targetEditId');
         const resetTargetFormButton = document.getElementById('resetTargetFormButton');
         const saveTargetButton = document.getElementById('saveTargetButton');
         const targetYearSelect = document.getElementById('targetYear'); // Reference to year input in target modal

         // --- Upload Modal Elements ---
         const uploadModalTitle = document.getElementById('uploadModalTitle'); const uploadItemCount = document.getElementById('uploadItemCount'); const uploadItemType = document.getElementById('uploadItemType'); const uploadForm = document.getElementById('uploadForm'); const submitUploadButton = document.getElementById('submitUploadButton'); const fileInput = document.getElementById('fileInput'); const uploadTypeInfo = document.getElementById('uploadTypeInfo');
         // --- View Files Modal Elements ---
         const viewFilesModal = document.getElementById('viewFilesModal'); const viewFilesModalTitle = document.getElementById('viewFilesModalTitle'); const viewFilesParentItemInfo = document.getElementById('viewFilesParentItemInfo'); const viewFilesSelectAll = document.getElementById('viewFilesSelectAll'); const viewFilesDeleteSelected = document.getElementById('viewFilesDeleteSelected'); const fileListContainer = document.getElementById('fileListContainer'); const fileGroupContainer = document.getElementById('fileGroupContainer'); const viewLoadingIndicator = document.getElementById('viewLoadingIndicator'); const viewFilesEmptyMessage = document.getElementById('viewFilesEmptyMessage'); const viewFilesErrorMessage = document.getElementById('viewFilesErrorMessage');
         // --- Delete Confirmation Modal Elements ---
         const deleteModal = document.getElementById('deleteModal'); const deleteModalTitle = document.getElementById('deleteModalTitle'); const deleteItemTypeSpan = document.getElementById('deleteItemType'); const deleteItemCountSpan = document.getElementById('deleteItemCount'); const deleteFileWarning = document.getElementById('deleteFileWarning'); const deleteItemWarning = document.getElementById('deleteItemWarning'); const confirmDeleteButton = document.getElementById('confirmDeleteButton');
         // --- Export Modal Elements ---
         const exportModal = document.getElementById('exportModal');
         const exportModalTitle = document.getElementById('exportModalTitle');
         const exportAllLabel = document.getElementById('exportAllLabel');
         const exportFilteredLabel = document.getElementById('exportFilteredLabel');
         const exportSelectedLabel = document.getElementById('exportSelectedLabel');
         const filteredItemsCount = document.getElementById('filteredItemsCount');
         const selectedItemsCount = document.getElementById('selectedItemsCount');
         const exportProcessingIndicator = document.getElementById('exportProcessingIndicator');
         const exportErrorIndicator = document.getElementById('exportErrorIndicator');
         const exportDataButton = document.getElementById('exportDataButton');
         // --- Import Modal Elements ---
         const importModal = document.getElementById('importModal');
         const importModalTitle = document.getElementById('importModalTitle');
         const importInstructions = document.getElementById('importInstructions');
         const importRequiredColumnsInfo = document.getElementById('importRequiredColumnsInfo');
         const importRequiredColumnsList = document.getElementById('importRequiredColumnsList');
         const csvFileInput = document.getElementById('csvFileInput');
         const fileNameDisplay = document.getElementById('fileNameDisplay');
         const previewDataButton = document.getElementById('previewDataButton');
         const importDataButton = document.getElementById('importDataButton');
         const previewTableContainer = document.getElementById('previewTableContainer');
         const previewTable = document.getElementById('previewTable');
         const previewTableHead = previewTable?.querySelector('thead');
         const previewTableBody = previewTable?.querySelector('tbody');
         const previewRowCount = document.getElementById('previewRowCount');
         const importStep1 = document.getElementById('importStep1');
         const importStep2 = document.getElementById('importStep2');
         const importErrorStep1 = document.getElementById('importErrorStep1');
         const importErrorStep2 = document.getElementById('importErrorStep2');
         const previewLoadingIndicator = document.getElementById('previewLoadingIndicator');
         const importProcessingIndicator = document.getElementById('importProcessingIndicator');
         const importResultIndicator = document.getElementById('importResultIndicator');

         // --- Import State ---
         let selectedFile = null; let parsedDataForImport = null;
         let currentImportType = 'activity'; // Default, will be set when modal opens
         const requiredColumnsActivity = [ 'Start Date', 'End Date', 'Bureau', 'Project', 'Activity Name', 'Indicator', 'Training Venue', 'Municipality/City', 'District', 'Barangay', 'Requesting Agency', 'Mode of Implementation', 'Target Sector', 'Responsible Person', 'Resource Person', 'Participants', 'Completers', 'Male', 'Female', 'Approved Activity Design', 'Link to MOVs', 'Remarks' ]; // Note: 'Bureau' maps to 'project' in DB, 'Project' maps to 'subproject'
         const requiredColumnsParticipant = [ 'Locality', 'Barangay', 'District', 'Locations', 'Type', 'Code', 'Strategy', 'Status', 'Reason', 'Remarks' ]; // New fields for tblfwfa
         const requiredColumnsLetter = [ 'Locality', 'Barangay', 'District', 'Location', 'Date', 'Year', 'Type', 'Status', 'Accomplished', 'Remarks' ]; // Fields for locationrequests



         // --- State Variables ---
         let notificationTimeout;
         let currentActiveTab = '<?php echo $active_tab; ?>'; // Use PHP value
         let currentViewFilesType = 'activity';
         let deleteContext = { type: null, ids: [] };
         let currentReportYear = '<?php echo $selected_report_year; ?>'; // Use PHP value

         // --- Utility Functions ---
         function showNotification(message, type = 'success', duration = 3000) { if (!notificationElement || !notificationMessageElement) return; clearTimeout(notificationTimeout); notificationMessageElement.textContent = message; notificationElement.className = 'notification ' + type; requestAnimationFrame(() => { notificationElement.classList.add('visible'); }); notificationTimeout = setTimeout(() => { notificationElement.classList.remove('visible'); }, duration); }
         function getSelectedIds(type = 'activity') {
             let selector;
             if (type === 'activity') {
                 selector = '.activity-row-checkbox:checked';
             } else if (type === 'participant') {
                 selector = '.participant-row-checkbox:checked';
             } else if (type === 'letter') {
                 selector = '.letter-row-checkbox:checked';
             } else {
                 selector = '.activity-row-checkbox:checked'; // Default fallback
             }
             return Array.from(document.querySelectorAll(selector)).map(cb => cb.value);
         }
         function updateActionBar() {
             let activeType = 'activity';
             if (currentActiveTab === 'participants') {
                 activeType = 'participant';
             } else if (currentActiveTab === 'letters') {
                 activeType = 'letter';
             }

             const selectedIds = getSelectedIds(activeType);
             const count = selectedIds.length;

             if (selectedCountSpan) {
                 if (activeType === 'participant') {
                     selectedCountSpan.textContent = `${count} location${count !== 1 ? 's' : ''} selected`;
                 } else if (activeType === 'letter') {
                     selectedCountSpan.textContent = `${count} letter request${count !== 1 ? 's' : ''} selected`;
                 } else {
                     selectedCountSpan.textContent = `${count} ${activeType}${count !== 1 ? 's' : ''} selected`;
                 }
             }

             if (selectionActionBar) selectionActionBar.classList.toggle('visible', count > 0);
             if (editButton) editButton.disabled = count !== 1;
             if (deleteButton) deleteButton.disabled = count === 0;
             if (uploadButton) uploadButton.disabled = count === 0;
             if (viewFilesButton) viewFilesButton.disabled = count === 0;

             let selectAllCheckbox, rowCheckboxes;
             if (activeType === 'activity') {
                 selectAllCheckbox = selectAllCheckboxActivity;
                 rowCheckboxes = activityRowCheckboxes;
             } else if (activeType === 'participant') {
                 selectAllCheckbox = selectAllCheckboxParticipant;
                 rowCheckboxes = participantRowCheckboxes;
             } else if (activeType === 'letter') {
                 selectAllCheckbox = selectAllCheckboxLetter;
                 rowCheckboxes = letterRowCheckboxes;
             }

             const totalCheckboxes = rowCheckboxes.length;
             if (selectAllCheckbox) {
                 if (totalCheckboxes > 0) {
                     selectAllCheckbox.checked = (count === totalCheckboxes);
                     selectAllCheckbox.indeterminate = (count > 0 && count < totalCheckboxes);
                 } else {
                     selectAllCheckbox.checked = false;
                     selectAllCheckbox.indeterminate = false;
                 }
             }

             // Reset other checkboxes
             if (activeType !== 'activity' && selectAllCheckboxActivity) {
                 selectAllCheckboxActivity.checked = false;
                 selectAllCheckboxActivity.indeterminate = false;
             }
             if (activeType !== 'participant' && selectAllCheckboxParticipant) {
                 selectAllCheckboxParticipant.checked = false;
                 selectAllCheckboxParticipant.indeterminate = false;
             }
             if (activeType !== 'letter' && selectAllCheckboxLetter) {
                 selectAllCheckboxLetter.checked = false;
                 selectAllCheckboxLetter.indeterminate = false;
             }
         }
         async function performAjax(action, data = {}) {
            const formData = new FormData();
            formData.append('action', action);

            for (const key in data) {
                if (Object.prototype.hasOwnProperty.call(data, key)) {
                    const value = data[key];
                    if (Array.isArray(value)) {
                        if (key === 'activities' || key === 'participants' || key === 'letters') {
                             formData.append(key, JSON.stringify(value));
                         } else {
                            value.forEach(item => formData.append(key + '[]', item));
                        }
                    } else if (value instanceof FileList) {
                        for (let i = 0; i < value.length; i++) {
                            formData.append('files[]', value[i]);
                        }
                    } else if (value !== null && value !== undefined) {
                        formData.append(key, value);
                    }
                }
            }

            try {
                const response = await fetch(ajaxHandlerUrl, { method: 'POST', body: formData });
                if (!response.ok) { throw new Error(`HTTP error! Status: ${response.status}`); }
                const responseClone = response.clone();
                let responseData;
                try { responseData = await response.json(); }
                catch (jsonError) { const errorText = await responseClone.text(); console.error('Non-JSON response:', errorText); throw new Error(`Server returned non-JSON response (Status: ${response.status}).`); }
                return responseData;
            } catch (error) {
                console.error('AJAX Error:', error);
                let userMessage = 'An unexpected error occurred.';
                if (error.message.includes('non-JSON')) userMessage = 'An error occurred communicating with the server.';
                else if (error.message.includes('HTTP error')) userMessage = `A server error occurred (${error.message}).`;
                else userMessage = `Error: ${error.message}.`;
                showNotification(userMessage, 'error', 5000);
                return { success: false, message: error.message, data: null };
            }
        }
         function escapeHtml(unsafe) { if (unsafe === null || typeof unsafe === 'undefined') return ''; return String(unsafe).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;"); }
         function formatDateForInputJS(dateString) { if (!dateString || dateString === '0000-00-00' || dateString.startsWith('0000-00-00')) return ''; try { const datePart = dateString.split(' ')[0]; const date = new Date(datePart + 'T00:00:00'); if (isNaN(date.getTime())) { console.warn("Invalid date string:", dateString); return ''; } const year = date.getFullYear(); const month = (date.getMonth() + 1).toString().padStart(2, '0'); const day = date.getDate().toString().padStart(2, '0'); return `${year}-${month}-${day}`; } catch (e) { console.error("Error formatting date:", dateString, e); return ''; } }
         function getFileIconClass(filename) { let fileIcon = 'fa-file'; const ext = filename?.split('.').pop()?.toLowerCase(); if (['pdf'].includes(ext)) fileIcon = 'fa-file-pdf'; else if (['doc', 'docx'].includes(ext)) fileIcon = 'fa-file-word'; else if (['xls', 'xlsx'].includes(ext)) fileIcon = 'fa-file-excel'; else if (['png', 'jpg', 'jpeg', 'gif', 'bmp'].includes(ext)) fileIcon = 'fa-file-image'; else if (['zip', 'rar', '7z'].includes(ext)) fileIcon = 'fa-file-archive'; return fileIcon; }

         // --- Modal Handling ---
         function openModal(modalKey) {
             const modal = modals[modalKey];
             if (!modal) { console.error("Modal not found for key:", modalKey); return; }

             // Special handling for manageTargets modal needs to happen *before* generic reset/open
             if (modalKey === 'manageTargets') {
                openManageTargetsModal(); // Call specific function to load data first
             } else {
                 resetModalContent(modalKey); // Standard reset for others
             }

             let activeType = 'activity';
             if (currentActiveTab === 'participants') {
                 activeType = 'participant';
             } else if (currentActiveTab === 'letters') {
                 activeType = 'letter';
             }
             const selectedParentIds = getSelectedIds(activeType);
             const count = selectedParentIds.length;

             if (modalKey === 'export') updateExportModalContent(activeType);
             else if (modalKey === 'import') { currentImportType = activeType; updateImportModalContent(activeType); }

             switch (modalKey) {
                 case 'editActivity': case 'editParticipant': case 'editLetter':
                    if (count !== 1) { showNotification(`Please select exactly one ${activeType} to edit.`, "error"); return; }

                    let editLoadingIndicatorId, editFormId, editErrorIndicatorId, saveEditButtonId;

                    if (activeType === 'activity') {
                        editLoadingIndicatorId = 'editActivityLoadingIndicator';
                        editFormId = 'editActivityForm';
                        editErrorIndicatorId = 'editActivityErrorIndicator';
                        saveEditButtonId = 'saveEditActivityButton';
                    } else if (activeType === 'participant') {
                        editLoadingIndicatorId = 'editParticipantLoadingIndicator';
                        editFormId = 'editParticipantForm';
                        editErrorIndicatorId = 'editParticipantErrorIndicator';
                        saveEditButtonId = 'saveEditParticipantButton';
                    } else if (activeType === 'letter') {
                        editLoadingIndicatorId = 'editLetterLoadingIndicator';
                        editFormId = 'editLetterForm';
                        editErrorIndicatorId = 'editLetterErrorIndicator';
                        saveEditButtonId = 'saveEditLetterButton';
                    }

                    modal.querySelector(`#${editLoadingIndicatorId}`).style.display = 'flex';
                    modal.querySelector(`#${editFormId}`).style.display = 'none';
                    modal.querySelector(`#${editErrorIndicatorId}`).style.display = 'none';
                    modal.querySelector(`#${saveEditButtonId}`).disabled = true;

                    if (activeType === 'activity') fetchActivityData(selectedParentIds[0]);
                    else if (activeType === 'participant') fetchParticipantData(selectedParentIds[0]);
                    else if (activeType === 'letter') fetchLetterData(selectedParentIds[0]);
                    break;
                 case 'delete':
                      if (deleteContext.type && deleteContext.ids && deleteContext.ids.length > 0) {
                         if (deleteModalTitle) deleteModalTitle.textContent = 'Confirm Deletion';
                         if (deleteItemTypeSpan) deleteItemTypeSpan.textContent = deleteContext.type === 'participant' ? 'location/s' : `${deleteContext.type}(s)`;
                         if (deleteItemCountSpan) deleteItemCountSpan.textContent = deleteContext.ids.length;
                         if (deleteFileWarning) deleteFileWarning.style.display = 'none';
                         if (deleteItemWarning) deleteItemWarning.style.display = 'block';
                     } else {
                         console.error("Delete modal open attempt with invalid context:", deleteContext);
                         showNotification("Error: Cannot determine items to delete. Please re-select.", "error");
                         return;
                     }
                     break;
                 case 'upload':
                     if (count === 0) { showNotification(`No ${activeType === 'participant' ? 'location/s' : activeType + 's'} selected for file upload.`, "info"); return; }
                     uploadModalTitle.textContent = `Upload Files for ${activeType === 'participant' ? 'Locations' : activeType.charAt(0).toUpperCase() + activeType.slice(1) + 's'}`;
                     uploadItemCount.textContent = count;
                     uploadItemType.textContent = `${activeType === 'participant' ? 'location/s' : activeType + '(s)'}`;
                     if(fileInput) fileInput.value = '';
                     uploadTypeInfo.innerHTML = `<i class="fas fa-info-circle"></i> Files will be associated with the selected ${activeType === 'participant' ? 'location/s' : activeType + '(s)'}. ${activeType === 'participant' ? 'Location files are stored separately.' : ''}`;
                     if(submitUploadButton) submitUploadButton.disabled = false;
                     break;
                 case 'view':
                     if (count === 0) { showNotification(`No ${activeType === 'participant' ? 'location/s' : activeType + 's'} selected to view files.`, "info"); return; }
                     currentViewFilesType = activeType;
                     viewFilesModalTitle.textContent = `View Associated Files`;
                     viewFilesParentItemInfo.textContent = `Files for ${count} selected ${activeType === 'participant' ? 'location/s' : activeType + '(s)'}:`;
                     if(viewLoadingIndicator) viewLoadingIndicator.classList.add('active');
                     if(fileGroupContainer) fileGroupContainer.innerHTML = '';
                     if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none';
                     if(viewFilesErrorMessage) viewFilesErrorMessage.style.display = 'none';
                     if(viewFilesSelectAll) { viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = false; viewFilesSelectAll.disabled = true; }
                     if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true;
                     if (activeType === 'activity') fetchActivityFiles(selectedParentIds);
                     else fetchParticipantFiles(selectedParentIds);
                     break;
                 case 'addActivity':
                     modal.querySelector('#addActivityForm')?.reset();
                     modal.querySelector('#addActProject').value = '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>';
                     modal.querySelector('#saveAddActivityButton').disabled = false;
                     modal.querySelector('#saveAddActivityButton').innerHTML = 'Save Activity';
                     break;
                 case 'addParticipant':
                     modal.querySelector('#addParticipantForm')?.reset();
                     modal.querySelector('#saveAddParticipantButton').disabled = false;
                     modal.querySelector('#saveAddParticipantButton').innerHTML = 'Save LGU';
                     break;
                 case 'statDetail':
                     // Content is populated by the click listener before calling openModal
                     break;
                 case 'manageTargets':
                    // Data loading handled by openManageTargetsModal() called earlier
                    break;
             }
             modal.classList.add('visible');
             document.body.style.overflow = 'hidden';
         }
         function closeModal(modalKey) {
             const modal = modals[modalKey];
             if (modal) { modal.classList.remove('visible'); }
             if (modalKey === 'delete') { deleteContext = { type: null, ids: [] }; }
             if (modalKey === 'import') resetImportModal();
             else if (modalKey === 'export') resetExportModal();
             // ADD: Reset target form if closing manageTargets modal
             else if (modalKey === 'manageTargets') {
                 resetTargetForm();
             }
             const anyVisible = Object.values(modals).some(m => m?.classList.contains('visible'));
             if (!anyVisible) { document.body.style.overflow = ''; }
         }
         function resetModalContent(modalKey) {
             const modal = modals[modalKey];
             if (!modal) return;
             switch(modalKey) {
                 case 'editActivity': modal.querySelector('#editActivityForm')?.reset(); modal.querySelector('#editActivityModalTitle').textContent = 'Edit Activity'; modal.querySelector('#editActivityLoadingIndicator').style.display = 'block'; modal.querySelector('#editActivityErrorIndicator').style.display = 'none'; modal.querySelector('#editActivityForm').style.display = 'none'; modal.querySelector('#saveEditActivityButton').disabled = true; break;
                 case 'editParticipant': modal.querySelector('#editParticipantForm')?.reset(); modal.querySelector('#editParticipantModalTitle').textContent = 'Edit LGU'; modal.querySelector('#editParticipantLoadingIndicator').style.display = 'block'; modal.querySelector('#editParticipantErrorIndicator').style.display = 'none'; modal.querySelector('#editParticipantForm').style.display = 'none'; modal.querySelector('#saveEditParticipantButton').disabled = true; break;
                 case 'editLetter': modal.querySelector('#editLetterForm')?.reset(); modal.querySelector('#editLetterModalTitle').textContent = 'Edit Letter Request'; modal.querySelector('#editLetterLoadingIndicator').style.display = 'block'; modal.querySelector('#editLetterErrorIndicator').style.display = 'none'; modal.querySelector('#editLetterForm').style.display = 'none'; modal.querySelector('#saveEditLetterButton').disabled = true; break;
                 case 'delete': if(deleteModalTitle) deleteModalTitle.textContent = 'Confirm Deletion'; if(deleteItemTypeSpan) deleteItemTypeSpan.textContent = 'item(s)'; if(deleteItemCountSpan) deleteItemCountSpan.textContent = '0'; if(deleteFileWarning) deleteFileWarning.style.display = 'none'; if(deleteItemWarning) deleteItemWarning.style.display = 'block'; break;
                 case 'upload': modal.querySelector('#uploadForm')?.reset(); modal.querySelector('#uploadItemCount').textContent = '0'; modal.querySelector('#uploadItemType').textContent = 'item(s)'; break;
                 case 'view': if (viewLoadingIndicator) viewLoadingIndicator.classList.remove('active'); if (fileGroupContainer) fileGroupContainer.innerHTML = ''; if (viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none'; if (viewFilesErrorMessage) viewFilesErrorMessage.style.display = 'none'; if (viewFilesSelectAll) { viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = false; viewFilesSelectAll.disabled = true; } if (viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true; if (viewFilesParentItemInfo) viewFilesParentItemInfo.textContent = 'Files for 0 selected item(s):'; break;
                 case 'addActivity': modal.querySelector('#addActivityForm')?.reset(); modal.querySelector('#addActProject').value = '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>'; modal.querySelector('#saveAddActivityButton').disabled = false; modal.querySelector('#saveAddActivityButton').innerHTML = 'Save Activity'; break;
                 case 'addParticipant': modal.querySelector('#addParticipantForm')?.reset(); modal.querySelector('#saveAddParticipantButton').disabled = false; modal.querySelector('#saveAddParticipantButton').innerHTML = 'Save LGU'; break;
                 case 'addLetter': modal.querySelector('#addLetterForm')?.reset(); modal.querySelector('#addLetterYear').value = new Date().getFullYear(); modal.querySelector('#saveAddLetterButton').disabled = false; modal.querySelector('#saveAddLetterButton').innerHTML = '<i class="fas fa-save"></i> Save Letter'; break;
                 case 'import': resetImportModal(); break;
                 case 'export': resetExportModal(); break;
                 case 'manageTargets':
                    resetTargetForm(); // Ensure form is reset
                    if(targetsTableBody) targetsTableBody.innerHTML = '<tr><td colspan="6" class="table-message">Loading targets...</td></tr>'; // Clear table visually
                    if(targetsLoadingIndicator) targetsLoadingIndicator.style.display = 'none';
                    if(targetsErrorIndicator) targetsErrorIndicator.style.display = 'none';
                    break;
                 case 'statsBreakdown':
                    modal.querySelector('#statsBreakdownLoadingIndicator').style.display = 'block';
                    modal.querySelector('#statsBreakdownErrorIndicator').style.display = 'none';
                    modal.querySelector('#statsBreakdownContent').style.display = 'none';
                    modal.querySelector('#statsModalTitle').textContent = 'Statistics Breakdown';

                    // Reset table
                    const tableBody = modal.querySelector('#statsBreakdownTable tbody');
                    if (tableBody) tableBody.innerHTML = '';

                    // Reset chart
                    if (currentChart) {
                        currentChart.destroy();
                        currentChart = null;
                    }

                    // Reset tabs
                    const tabBtns = modal.querySelectorAll('.stats-tab-btn');
                    tabBtns.forEach(btn => {
                        btn.classList.toggle('active', btn.dataset.view === 'table');
                    });

                    // Show table view by default
                    const tableView = modal.querySelector('#statsTableView');
                    const chartView = modal.querySelector('#statsChartView');
                    if (tableView) tableView.style.display = 'block';
                    if (chartView) chartView.style.display = 'none';
                    break;
                 case 'strategyTable':
                    // Reset the strategy table modal
                    const strategyTableLoading = document.getElementById('strategyTableLoading');
                    const strategyTableError = document.getElementById('strategyTableError');
                    const strategyTableContainer = document.getElementById('strategyTableContainer');
                    const strategyTableHead = document.getElementById('strategyTableHead');
                    const strategyTableBody = document.getElementById('strategyTableBody');

                    if (strategyTableLoading) strategyTableLoading.style.display = 'flex';
                    if (strategyTableError) strategyTableError.style.display = 'none';
                    if (strategyTableContainer) strategyTableContainer.style.display = 'none';
                    if (strategyTableHead) strategyTableHead.innerHTML = '';
                    if (strategyTableBody) strategyTableBody.innerHTML = '';

                    // Load strategy table data
                    loadStrategyTableData();
                    break;
             }
         }
         // --- Reset Functions for Specific Modals ---
         function resetExportModal() {
             if (!exportModal) return;
             const allRadio = exportModal.querySelector('input[name="exportType"][value="all"]');
             if (allRadio) allRadio.checked = true;
             const csvRadio = exportModal.querySelector('input[name="exportFormat"][value="csv"]');
             if (csvRadio) csvRadio.checked = true;

             let activeType = 'activity';
             if (currentActiveTab === 'participants') {
                 activeType = 'participant';
             } else if (currentActiveTab === 'letters') {
                 activeType = 'letter';
             }
             updateExportModalContent(activeType);

             if (exportProcessingIndicator) exportProcessingIndicator.style.display = 'none';
             if (exportErrorIndicator) {
                 exportErrorIndicator.style.display = 'none';
                 exportErrorIndicator.textContent = '';
             }
             if (exportDataButton) exportDataButton.disabled = false;
         }
         function resetImportModal() { if (!importModal) return; selectedFile = null; parsedDataForImport = null; if(csvFileInput) csvFileInput.value = ''; if(fileNameDisplay) fileNameDisplay.textContent = 'No file chosen'; if(importStep1) importStep1.style.display = 'block'; if(importStep2) importStep2.style.display = 'none'; if(previewTableContainer) previewTableContainer.style.display = 'none'; if(previewTableHead) previewTableHead.innerHTML = ''; if(previewTableBody) previewTableBody.innerHTML = ''; if(previewRowCount) previewRowCount.textContent = ''; if(previewDataButton) previewDataButton.disabled = true; if(importDataButton) importDataButton.disabled = true; if(importErrorStep1) importErrorStep1.style.display = 'none'; if(importErrorStep2) importErrorStep2.style.display = 'none'; if(previewLoadingIndicator) previewLoadingIndicator.style.display = 'none'; if(importProcessingIndicator) importProcessingIndicator.style.display = 'none'; if(importResultIndicator) { importResultIndicator.style.display = 'none'; importResultIndicator.className = ''; importResultIndicator.textContent = ''; } let activeType = 'activity';
             if (currentActiveTab === 'participants') {
                 activeType = 'participant';
             } else if (currentActiveTab === 'letters') {
                 activeType = 'letter';
             }
             updateImportModalContent(activeType); }
         function resetTargetForm() {
            if (targetForm) targetForm.reset();
            if (targetEditIdInput) targetEditIdInput.value = '';
            if (targetFormTitle) targetFormTitle.textContent = 'Add New Target';
            if (saveTargetButton) {
                saveTargetButton.disabled = false;
                saveTargetButton.innerHTML = '<i class="fas fa-save"></i> Save Target';
            }
             // Set category back to default readonly value if form reset clears it
             const categoryInput = document.getElementById('targetCategory');
             if(categoryInput) categoryInput.value = '<?php echo htmlspecialchars($target_category_filter, ENT_QUOTES); ?>'; // <-- Use correct variable
             // Set default year using the JS variable tracking the report filter
             if(targetYearSelect) targetYearSelect.value = currentReportYear;
             // Clear the target participants field
             const targetParticipantsInput = document.getElementById('targetParticipantsValue');
             if(targetParticipantsInput) targetParticipantsInput.value = '';
         }


         function updateExportModalContent(activeType) {
             let typeName, typeNamePlural, totalFiltered;

             if (activeType === 'participant') {
                 typeName = 'LGU';
                 typeNamePlural = 'LGUs';
                 totalFiltered = <?php echo $total_participants; ?>;
             } else if (activeType === 'letter') {
                 typeName = 'Letter Request';
                 typeNamePlural = 'Letter Requests';
                 totalFiltered = <?php echo $total_letters; ?>;
             } else {
                 typeName = 'Activity';
                 typeNamePlural = 'Activities';
                 totalFiltered = <?php echo $total_activities; ?>;
             }

             if (exportModalTitle) exportModalTitle.innerHTML = `<i class="fas fa-file-export"></i> Export ${typeNamePlural}`;
             if (exportAllLabel) exportAllLabel.textContent = `All ${typeNamePlural.toLowerCase()}`;
             if (exportFilteredLabel) exportFilteredLabel.textContent = `Filtered ${typeNamePlural.toLowerCase()}`;
             if (exportSelectedLabel) exportSelectedLabel.textContent = `Selected ${typeNamePlural.toLowerCase()}`;

             if (filteredItemsCount) { filteredItemsCount.textContent = `${totalFiltered} ${typeNamePlural.toLowerCase()} in current filter`; }
             const selectedIds = getSelectedIds(activeType);
             const selectedCount = selectedIds.length;
             if (selectedItemsCount) { selectedItemsCount.textContent = `${selectedCount} ${typeNamePlural.toLowerCase()} selected`; }
             const selectedRadio = exportModal?.querySelector('input[name="exportType"][value="selected"]');
             const allRadio = exportModal?.querySelector('input[name="exportType"][value="all"]');
             if (selectedRadio) {
                 selectedRadio.disabled = (selectedCount === 0);
                 if (selectedCount === 0 && selectedRadio.checked && allRadio) {
                     allRadio.checked = true;
                 }
             }
         }
         function updateImportModalContent(activeType) {
             let typeName, typeNamePlural, requiredCols;

             if (activeType === 'participant') {
                 typeName = 'LGU';
                 typeNamePlural = 'LGUs';
                 requiredCols = requiredColumnsParticipant;
             } else if (activeType === 'letter') {
                 typeName = 'Letter Request';
                 typeNamePlural = 'Letter Requests';
                 requiredCols = requiredColumnsLetter;
             } else {
                 typeName = 'Activity';
                 typeNamePlural = 'Activities';
                 requiredCols = requiredColumnsActivity;
             }

             if (importModalTitle) importModalTitle.innerHTML = `<i class="fas fa-file-import"></i> Import ${typeNamePlural}`;
             if (importInstructions) importInstructions.textContent = `Select a CSV file containing the ${typeNamePlural.toLowerCase()} to import.`;
             if (importRequiredColumnsList) {
                 importRequiredColumnsList.innerHTML = '';
                 requiredCols.forEach(col => {
                     const span = document.createElement('span');
                     span.className = 'tag';
                     span.textContent = col;
                     importRequiredColumnsList.appendChild(span);
                 });
             }
         }
         function showImportError(message, step = 1) { const errorDiv = step === 1 ? importErrorStep1 : importErrorStep2; if (errorDiv) { errorDiv.textContent = message; errorDiv.style.display = 'block'; errorDiv.className = 'error-indicator'; } if (importResultIndicator) importResultIndicator.style.display = 'none'; }
         function showImportMessage(message, step = 2) { const errorDiv = step === 1 ? importErrorStep1 : importErrorStep2; const resultDiv = importResultIndicator; if (errorDiv) { errorDiv.textContent = message; errorDiv.style.display = 'block'; errorDiv.className = 'info-indicator'; } if(resultDiv) resultDiv.style.display = 'none'; }
         function displayPreviewTable(headers, data) { if (!previewTableHead || !previewTableBody || !previewTableContainer) return; previewTableHead.innerHTML = ''; previewTableBody.innerHTML = ''; const headerRow = previewTableHead.insertRow(); headers.forEach(headerText => { const th = document.createElement('th'); th.textContent = headerText; headerRow.appendChild(th); }); const previewLimit = 10; const dataToPreview = data.slice(0, previewLimit); dataToPreview.forEach(rowData => { const row = previewTableBody.insertRow(); headers.forEach(header => { const cell = row.insertCell(); const cellValue = rowData[header] !== null && rowData[header] !== undefined ? String(rowData[header]) : ''; cell.textContent = cellValue; cell.title = cellValue; }); }); previewTableContainer.style.display = 'block'; if (previewRowCount) { previewRowCount.textContent = `Showing first ${dataToPreview.length} of ${data.length} data rows.`; } }

         // --- Data Fetching Functions ---
         async function fetchActivityData(id) { console.log(`Fetching activity data for ID: ${id}`); const modal = modals.editActivity; const form = modal.querySelector('#editActivityForm'); const loadingIndicator = modal.querySelector('#editActivityLoadingIndicator'); const errorIndicator = modal.querySelector('#editActivityErrorIndicator'); const saveButton = modal.querySelector('#saveEditActivityButton'); loadingIndicator.style.display = 'flex'; errorIndicator.style.display = 'none'; form.style.display = 'none'; form.reset(); saveButton.disabled = true; const result = await performAjax('getActivity', { id: id }); loadingIndicator.style.display = 'none'; if (result?.success && result.data) { const data = result.data; try { form.querySelector('#editActivityFormId').value = data.id || ''; form.querySelector('#editActStart').value = formatDateForInputJS(data.start); form.querySelector('#editActEnd').value = formatDateForInputJS(data.end); form.querySelector('#editActProject').value = data.project ?? ''; form.querySelector('#editActSubproject').value = data.subproject ?? ''; form.querySelector('#editActActivityName').value = data.activity ?? ''; form.querySelector('#editActIndicator').value = data.indicator ?? ''; form.querySelector('#editActTraining').value = data.training ?? ''; form.querySelector('#editActMunicipality').value = data.municipality ?? ''; form.querySelector('#editActDistrict').value = data.district ?? ''; form.querySelector('#editActBarangay').value = data.barangay ?? ''; form.querySelector('#editActAgency').value = data.agency ?? ''; form.querySelector('#editActMode').value = data.mode ?? ''; form.querySelector('#editActSector').value = data.sector ?? ''; form.querySelector('#editActPerson').value = data.person ?? ''; form.querySelector('#editActResource').value = data.resource ?? ''; form.querySelector('#editActParticipants').value = data.participants ?? '0'; form.querySelector('#editActCompleters').value = data.completers ?? '0'; form.querySelector('#editActMale').value = data.male ?? '0'; form.querySelector('#editActFemale').value = data.female ?? '0'; form.querySelector('#editActApproved').value = data.approved ?? ''; form.querySelector('#editActMov').value = data.mov ?? ''; form.querySelector('#editActRemarks').value = data.remarks ?? ''; modal.querySelector('#editActivityModalTitle').textContent = 'Edit Activity'; form.style.display = 'grid'; saveButton.disabled = false; } catch (populateError) { console.error("Error populating activity edit form:", populateError); errorIndicator.textContent = `Error displaying data: ${populateError.message}.`; errorIndicator.style.display = 'block'; } } else { errorIndicator.textContent = `Error: ${result?.message || 'Failed to retrieve activity data.'}`; errorIndicator.style.display = 'block'; } }
         async function fetchParticipantData(id) { console.log(`Fetching LGU data for ID: ${id}`); const modal = modals.editParticipant; const form = modal.querySelector('#editParticipantForm'); const loadingIndicator = modal.querySelector('#editParticipantLoadingIndicator'); const errorIndicator = modal.querySelector('#editParticipantErrorIndicator'); const saveButton = modal.querySelector('#saveEditParticipantButton'); loadingIndicator.style.display = 'flex'; errorIndicator.style.display = 'none'; form.style.display = 'none'; form.reset(); saveButton.disabled = true; const result = await performAjax('getParticipant', { id: id }); loadingIndicator.style.display = 'none'; if (result?.success && result.data) { const data = result.data; try { form.querySelector('#editParticipantFormId').value = data.id || ''; form.querySelector('#editPartLocality').value = data.locality ?? ''; form.querySelector('#editPartBarangay').value = data.barangay ?? ''; form.querySelector('#editPartDistrict').value = data.district ?? ''; form.querySelector('#editPartLocations').value = data.locations ?? ''; form.querySelector('#editPartType').value = data.type ?? ''; form.querySelector('#editPartCode').value = data.code ?? ''; form.querySelector('#editPartStrategy').value = data.strategy ?? ''; form.querySelector('#editPartStatus').value = data.status ?? ''; form.querySelector('#editPartReason').value = data.reason ?? ''; form.querySelector('#editPartRemarks').value = data.remarks ?? ''; modal.querySelector('#editParticipantModalTitle').textContent = 'Edit LGU'; form.style.display = 'grid'; saveButton.disabled = false; } catch (populateError) { console.error("Error populating LGU edit form:", populateError); errorIndicator.textContent = `Error displaying data: ${populateError.message}.`; errorIndicator.style.display = 'block'; } } else { errorIndicator.textContent = `Error: ${result?.message || 'Failed to retrieve LGU data.'}`; errorIndicator.style.display = 'block'; } }

         async function fetchLetterData(id) {
             console.log(`Fetching Letter Request data for ID: ${id}`);
             const modal = modals.editLetter;
             const form = modal.querySelector('#editLetterForm');
             const loadingIndicator = modal.querySelector('#editLetterLoadingIndicator');
             const errorIndicator = modal.querySelector('#editLetterErrorIndicator');
             const saveButton = modal.querySelector('#saveEditLetterButton');

             loadingIndicator.style.display = 'flex';
             errorIndicator.style.display = 'none';
             form.style.display = 'none';
             form.reset();
             saveButton.disabled = true;

             const result = await performAjax('getLetter', { id: id });
             loadingIndicator.style.display = 'none';

             if (result?.success && result.data) {
                 const data = result.data;
                 try {
                     form.querySelector('#editLetterFormId').value = data.id || '';
                     form.querySelector('#editLetterLocality').value = data.locality ?? '';
                     form.querySelector('#editLetterBarangay').value = data.barangay ?? '';
                     form.querySelector('#editLetterDistrict').value = data.district ?? '';
                     form.querySelector('#editLetterLocation').value = data.location ?? '';
                     form.querySelector('#editLetterDate').value = formatDateForInputJS(data.date);
                     form.querySelector('#editLetterYear').value = data.year ?? '';
                     form.querySelector('#editLetterType').value = data.type ?? '';
                     form.querySelector('#editLetterStatus').value = data.status ?? '';
                     form.querySelector('#editLetterAccomplished').value = data.accomplished ?? '';
                     form.querySelector('#editLetterRemarks').value = data.remarks ?? '';

                     modal.querySelector('#editLetterModalTitle').textContent = 'Edit Letter Request';
                     form.style.display = 'grid';
                     saveButton.disabled = false;
                 } catch (populateError) {
                     console.error("Error populating Letter Request edit form:", populateError);
                     errorIndicator.textContent = `Error displaying data: ${populateError.message}.`;
                     errorIndicator.style.display = 'block';
                 }
             } else {
                 errorIndicator.textContent = `Error: ${result?.message || 'Failed to retrieve Letter Request data.'}`;
                 errorIndicator.style.display = 'block';
             }
         }
         async function fetchActivityFiles(activityIds) {
             console.log("Fetching files for Activity IDs:", activityIds);
             console.log("Current active tab:", currentActiveTab);
             console.log("Current view files type:", currentViewFilesType);

             if(viewLoadingIndicator) viewLoadingIndicator.classList.add('active');
             if(fileGroupContainer) fileGroupContainer.innerHTML = '';
             if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none';
             if(viewFilesErrorMessage) viewFilesErrorMessage.style.display = 'none';

             console.log("Sending getFiles request with IDs:", activityIds);
             const result = await performAjax('getFiles', { ids: activityIds });
             console.log("getFiles result:", result);

             if(viewLoadingIndicator) viewLoadingIndicator.classList.remove('active');
             if (result.success && result.data?.groupedFiles) {
                 console.log("Successfully retrieved files, populating UI with type: activity");
                 populateFileGroups(result.data.groupedFiles, result.data.totalFiles, 'activity');
             } else {
                 if(viewFilesErrorMessage) {
                     viewFilesErrorMessage.textContent = `Error loading files: ${result.message || 'Unknown error'}`;
                     viewFilesErrorMessage.style.display = 'block';
                 }
                 console.error("Error fetching activity files:", result.message);
                 updateViewFilesControls();
             }
         }
         async function fetchParticipantFiles(participantIds) {
             let endpoint = 'getFW4AFiles';
             let itemType = 'fw4a';

             console.log("Current active tab:", currentActiveTab);
             console.log("Current view files type:", currentViewFilesType);

             // If we're in the letters tab, use the letter files endpoint
             if (currentActiveTab === 'letters') {
                 console.log("Fetching files for Letter IDs:", participantIds);
                 endpoint = 'getLetterFiles';
                 itemType = 'letter';
             } else {
                 console.log("Fetching files for FW4A IDs:", participantIds);
             }

             if(viewLoadingIndicator) viewLoadingIndicator.classList.add('active');
             if(fileGroupContainer) fileGroupContainer.innerHTML = '';
             if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none';
             if(viewFilesErrorMessage) viewFilesErrorMessage.style.display = 'none';

             console.log(`Sending ${endpoint} request with IDs:`, participantIds);
             const result = await performAjax(endpoint, { ids: participantIds });
             console.log(`${endpoint} result:`, result);

             if(viewLoadingIndicator) viewLoadingIndicator.classList.remove('active');
             if (result.success && result.data?.groupedFiles) {
                 console.log(`Successfully retrieved files, populating UI with type: ${itemType}`);
                 populateFileGroups(result.data.groupedFiles, result.data.totalFiles, itemType);
             } else {
                 if(viewFilesErrorMessage) {
                     viewFilesErrorMessage.textContent = `Error loading files: ${result.message || 'Unknown error'}`;
                     viewFilesErrorMessage.style.display = 'block';
                 }
                 console.error(`Error fetching ${itemType} files:`, result.message);
                 updateViewFilesControls();
             }
         }
         function populateFileGroups(groupedFiles, totalFiles, itemType = 'activity') { if(fileGroupContainer) fileGroupContainer.innerHTML = ''; if (totalFiles === 0) { if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'block'; if(viewFilesSelectAll) viewFilesSelectAll.disabled = true; if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true; return; } else { if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'none'; if(viewFilesSelectAll) viewFilesSelectAll.disabled = false; if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true; } let groupIndex = 0; for (const groupName in groupedFiles) { const filesInGroup = groupedFiles[groupName]; const groupDiv = document.createElement('div'); groupDiv.className = 'file-group collapsed'; const headerDiv = document.createElement('div'); headerDiv.className = 'file-group-header'; headerDiv.innerHTML = `<button class="toggle-group"><i class="fas fa-chevron-right"></i></button><span class="group-title" title="${escapeHtml(groupName)}">${escapeHtml(groupName)}</span><span class="badge">${filesInGroup.length} file(s)</span>`; const contentDiv = document.createElement('div'); contentDiv.className = 'file-group-content'; filesInGroup.forEach(file => { const safeFilename = escapeHtml(file.original_filename || 'Unnamed File'); const fileIconClass = getFileIconClass(safeFilename); let downloadAction;
                if (itemType === 'participant') {
                    downloadAction = 'downloadParticipantFile';
                } else if (itemType === 'fw4a') {
                    downloadAction = 'downloadFW4AFile';
                } else if (itemType === 'letter') {
                    downloadAction = 'downloadFile';
                } else {
                    downloadAction = 'downloadFile';
                } const downloadUrl = `${ajaxHandlerUrl}?action=${downloadAction}&file_id=${file.file_id}`; const fileItemDiv = document.createElement('div'); fileItemDiv.className = 'file-item'; fileItemDiv.dataset.fileId = file.file_id; const previewUrl = `${ajaxHandlerUrl}?action=previewFile&file_id=${file.file_id}&type=${itemType}`;
fileItemDiv.innerHTML = `<input type="checkbox" class="file-select-checkbox" value="${file.file_id}"><i class="fas ${fileIconClass} file-icon"></i><div class="file-details"><span class="file-name" title="${safeFilename}">${safeFilename}</span><span class="file-meta">Uploaded: ${escapeHtml(file.formatted_uploaded_at || 'N/A')}</span></div><span class="file-size">${escapeHtml(file.formatted_filesize || 'N/A')}</span><a href="${previewUrl}" class="btn-icon btn-preview-file" title="Preview ${safeFilename}" target="_blank"><i class="fas fa-eye"></i></a><a href="${downloadUrl}" class="btn-icon btn-download-file" title="Download ${safeFilename}"><i class="fas fa-download"></i></a>`;
contentDiv.appendChild(fileItemDiv);
fileItemDiv.querySelector('.file-select-checkbox').addEventListener('change', updateViewFilesControls);
fileItemDiv.querySelector('.btn-download-file').addEventListener('click', function(e) {
    e.preventDefault();
    console.log("Download URL:", this.getAttribute('href'));
    window.location.href = this.getAttribute('href');
}); }); groupDiv.appendChild(headerDiv); groupDiv.appendChild(contentDiv); fileGroupContainer.appendChild(groupDiv); headerDiv.addEventListener('click', (e) => { if(e.target.tagName === 'INPUT') return; const group = headerDiv.closest('.file-group'); group.classList.toggle('collapsed'); const icon = headerDiv.querySelector('.toggle-group i'); icon.className = group.classList.contains('collapsed') ? 'fas fa-chevron-right' : 'fas fa-chevron-down'; }); if (groupIndex === 0) { groupDiv.classList.remove('collapsed'); headerDiv.querySelector('.toggle-group i').className = 'fas fa-chevron-down'; } groupIndex++; } updateViewFilesControls(); }
         function updateViewFilesControls() { if (!viewFilesModal) return; const allCheckboxes = viewFilesModal.querySelectorAll('.file-select-checkbox'); const checkedCheckboxes = viewFilesModal.querySelectorAll('.file-select-checkbox:checked'); const totalCount = allCheckboxes.length; const checkedCount = checkedCheckboxes.length; if (totalCount === 0) { if(viewFilesSelectAll){ viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = false; viewFilesSelectAll.disabled = true;} if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = true; } else { if(viewFilesSelectAll){ viewFilesSelectAll.disabled = false; if (checkedCount === 0) { viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = false; } else if (checkedCount === totalCount) { viewFilesSelectAll.checked = true; viewFilesSelectAll.indeterminate = false; } else { viewFilesSelectAll.checked = false; viewFilesSelectAll.indeterminate = true; }} if(viewFilesDeleteSelected) viewFilesDeleteSelected.disabled = (checkedCount === 0); } }

         // --- Target Management Functions ---
         async function openManageTargetsModal() {
             if (!modals.manageTargets) return;
             resetTargetForm(); // Reset form first
             if(targetsLoadingIndicator) targetsLoadingIndicator.style.display = 'flex';
             if(targetsErrorIndicator) targetsErrorIndicator.style.display = 'none';
             if(targetsTableBody) targetsTableBody.innerHTML = '<tr><td colspan="6" class="table-message">Loading...</td></tr>';

             // Fetch targets for the specific category
             const result = await performAjax('getTargets', {
                 category: '<?php echo $target_category_filter; ?>' // <-- Use Correct PHP Variable
                 // Year filter is removed here, modal shows all years for the category
             });

             if(targetsLoadingIndicator) targetsLoadingIndicator.style.display = 'none';
             if (result.success && result.data) {
                 populateTargetsTable(result.data);
             } else {
                 if(targetsErrorIndicator) {
                     targetsErrorIndicator.textContent = `Error loading targets: ${result.message || 'Unknown error'}`;
                     targetsErrorIndicator.style.display = 'block';
                 }
                 if(targetsTableBody) targetsTableBody.innerHTML = '<tr><td colspan="6" class="table-message error">Could not load targets.</td></tr>';
             }
             // Modal visibility is handled by the calling openModal function
         }

         function populateTargetsTable(targets) {
             if (!targetsTableBody) return;
             targetsTableBody.innerHTML = ''; // Clear existing

             if (!targets || targets.length === 0) {
                 targetsTableBody.innerHTML = '<tr><td colspan="6" class="table-message">No targets defined for this category yet.</td></tr>';
                 return;
             }

             // Sort targets for display (optional, can be done in SQL too)
             targets.sort((a, b) => {
                if (a.year !== b.year) return b.year - a.year; // Descending year
                const subcatCompare = (a.subcategory || '').localeCompare(b.subcategory || '');
                if (subcatCompare !== 0) return subcatCompare; // Ascending subcategory
                return (a.indicator || '').localeCompare(b.indicator || ''); // Ascending indicator
             });


             targets.forEach(target => {
                 const row = targetsTableBody.insertRow();
                 row.dataset.targetId = target.id;
                 row.innerHTML = `
                     <td>${escapeHtml(target.category)}</td>
                     <td>${escapeHtml(target.subcategory)}</td>
                     <td>${escapeHtml(target.indicator)}</td>
                     <td>${escapeHtml(target.year)}</td>
                     <td>${escapeHtml(target.target)}</td>
                     <td>${target.target_participants !== null ? escapeHtml(target.target_participants) : '<span class="text-muted">-</span>'}</td>
                     <td class="actions-col">
                         <button class="btn-icon edit-target" title="Edit Target" data-id="${target.id}"><i class="fas fa-edit"></i></button>
                         <button class="btn-icon delete-target" title="Delete Target" data-id="${target.id}"><i class="fas fa-trash"></i></button>
                     </td>
                 `;
                 // Add event listeners for edit/delete buttons
                 row.querySelector('.edit-target').addEventListener('click', () => editTarget(target));
                 row.querySelector('.delete-target').addEventListener('click', () => deleteTarget(target.id));
             });
         }

         function editTarget(targetData) {
            if (!targetForm || !targetData) return;
            targetEditIdInput.value = targetData.id;
            targetFormTitle.textContent = 'Edit Target';
            document.getElementById('targetCategory').value = targetData.category || '<?php echo htmlspecialchars($target_category_filter, ENT_QUOTES); ?>'; // <-- Use Correct PHP Variable
            document.getElementById('targetSubcategory').value = targetData.subcategory || '';
            document.getElementById('targetIndicator').value = targetData.indicator || '';
            document.getElementById('targetYear').value = targetData.year || currentReportYear; // Use current report year as fallback
            document.getElementById('targetValue').value = targetData.target || '';
            document.getElementById('targetParticipantsValue').value = targetData.target_participants || '';
            saveTargetButton.innerHTML = '<i class="fas fa-save"></i> Update Target';
            document.getElementById('targetSubcategory').focus(); // Focus on first editable field
         }

         async function deleteTarget(targetId) {
             const result = await performAjax('deleteTarget', { id: targetId });
             if (result.success) {
                 showNotification(result.message || 'Target deleted successfully!', 'success');
                 // Refresh the table IF the modal is currently visible
                 if (modals.manageTargets && modals.manageTargets.classList.contains('visible')) {
                     openManageTargetsModal();
                 }
                 fetchAndRenderGraphs(); // Refresh graphs outside the modal
             } else {
                 showNotification(`Error deleting target: ${result.message || 'Unknown error'}`, 'error', 5000);
             }
         }

         // --- FW4A Report Functions ---
         // Variables for FW4A Report Tab
         let fw4aData = [];
         let fw4aFilteredData = [];
         let fw4aCurrentPage = 1;
         let fw4aRowsPerPage = 10;
         let fw4aStrategies = [];

         // Variables for Statistics Modal
         let currentStatKey = '';
         let currentStatData = [];
         let currentChart = null;
         let filteredData = [];
         let currentPage = 1;
         let rowsPerPage = 10;

         async function loadFW4AReportData() {
             try {
                 // Fetch data from the server
                 const result = await performAjax('getFW4AData', {});

                 if (result.success && result.data) {
                     // Store the data
                     fw4aData = result.data.locations || [];
                     fw4aStrategies = result.data.strategies || [];

                     // Update the statistics
                     updateFW4AStatistics(result.data);
                 } else {
                     console.error("Failed to fetch FW4A data:", result.message || "Unknown error");
                 }
             } catch (error) {
                 console.error("Error loading FW4A report data:", error);
             }
         }





         function updateFW4AStatistics(data) {
             // Update the main statistics
             document.getElementById('totalAccessPointsCount').textContent = data.total_count || 0;
             document.getElementById('activeAccessPointsCount').textContent = data.active_count || 0;
             document.getElementById('inactiveAccessPointsCount').textContent = data.inactive_count || 0;

             // Generate strategy statistics
             const strategyStatsContainer = document.getElementById('fw4aStrategyStats');
             if (strategyStatsContainer) {
                 strategyStatsContainer.innerHTML = '';

                 // Filter out strategies with zero count
                 const nonZeroStrategies = data.strategy_counts.filter(strategy => strategy.count > 0);

                 // Create a stat box for each non-zero strategy
                 nonZeroStrategies.forEach(strategy => {
                     // Format the strategy label for better display
                     const formattedLabel = formatStrategyLabel(strategy.strategy);

                     const strategyStatItem = document.createElement('div');
                     strategyStatItem.className = 'strategy-stat-item';

                     // Determine appropriate icon based on strategy name
                     let iconClass = 'fas fa-wifi'; // Default icon

                     // You can customize icons based on strategy name if needed
                     if (strategy.strategy.includes('CoRe')) {
                         iconClass = 'fas fa-broadcast-tower';
                     } else if (strategy.strategy.includes('PICS')) {
                         iconClass = 'fas fa-project-diagram';
                     } else if (strategy.strategy.includes('RIS')) {
                         iconClass = 'fas fa-network-wired';
                     }

                     strategyStatItem.innerHTML = `
                         <div class="strategy-stat-number">${strategy.count}</div>
                         <div class="strategy-stat-label">${formattedLabel}</div>
                     `;

                     strategyStatsContainer.appendChild(strategyStatItem);
                 });
             }
         }

         // Helper function to format strategy labels
         function formatStrategyLabel(strategy) {
             // Replace underscores with spaces
             let formatted = strategy.replace(/_/g, ' ');

             // Handle specific strategy formats
             if (formatted.includes('CoRe-FW4A')) {
                 formatted = formatted.replace('CoRe-FW4A', 'CoRe-FW4A');
             }

             // Handle PICS-MUN, PICS-PP, PICS-SUC formats
             if (formatted.includes('PICS-')) {
                 formatted = formatted.replace('PICS-', 'PICS-');
             }

             // Handle RIS-PICS, RIS-WISPS formats
             if (formatted.includes('RIS-')) {
                 formatted = formatted.replace('RIS-', 'RIS-');
             }

             return formatted;
         }

         function populateFW4ATable() {
             const tableBody = document.getElementById('fw4aTableBody');
             if (!tableBody) return;

             // Search functionality removed as requested
             fw4aFilteredData = [...fw4aData];

             // Calculate pagination
             const totalItems = fw4aFilteredData.length;
             const totalPages = Math.ceil(totalItems / fw4aRowsPerPage);

             // Adjust current page if needed
             if (fw4aCurrentPage > totalPages) {
                 fw4aCurrentPage = Math.max(1, totalPages);
             }

             const startIndex = (fw4aCurrentPage - 1) * fw4aRowsPerPage;
             const endIndex = Math.min(startIndex + fw4aRowsPerPage, totalItems);
             const paginatedData = fw4aFilteredData.slice(startIndex, endIndex);

             // Generate table rows
             if (paginatedData.length === 0) {
                 tableBody.innerHTML = '<tr><td colspan="10" class="table-message">No data found.</td></tr>';
             } else {
                 tableBody.innerHTML = '';

                 paginatedData.forEach(item => {
                     const row = document.createElement('tr');
                     row.innerHTML = `
                         <td>${escapeHtml(item.locality || '')}</td>
                         <td>${escapeHtml(item.barangay || '')}</td>
                         <td>${escapeHtml(item.district || '')}</td>
                         <td>${escapeHtml(item.locations || '')}</td>
                         <td>${escapeHtml(item.type || '')}</td>
                         <td>${escapeHtml(item.code || '')}</td>
                         <td>${escapeHtml(item.strategy || '')}</td>
                         <td>${escapeHtml(item.status || '')}</td>
                         <td>${escapeHtml(item.reason || '')}</td>
                         <td>${escapeHtml(item.remarks || '')}</td>
                     `;
                     tableBody.appendChild(row);
                 });
             }

             // Update pagination info
             const paginationInfo = document.getElementById('fw4aTableInfo');
             if (paginationInfo) {
                 if (totalItems > 0) {
                     paginationInfo.textContent = `Showing ${startIndex + 1} - ${endIndex} of ${totalItems}`;
                 } else {
                     paginationInfo.textContent = 'Showing 0 - 0 of 0';
                 }
             }

             // Update pagination controls
             updateFW4APagination(totalPages);
         }

         function updateFW4APagination(totalPages) {
             const paginationNav = document.getElementById('fw4aTablePagination');
             if (!paginationNav) return;

             let paginationHTML = '';

             // Previous button
             if (fw4aCurrentPage > 1) {
                 paginationHTML += `<a href="#" class="btn btn-nav fw4a-prev-page">Previous</a>`;
             } else {
                 paginationHTML += `<span class="btn btn-nav disabled">Previous</span>`;
             }

             // Page numbers
             if (totalPages > 1) {
                 const maxLinks = 5;
                 let startLink = Math.max(1, fw4aCurrentPage - Math.floor(maxLinks / 2));
                 let endLink = Math.min(totalPages, startLink + maxLinks - 1);

                 if (endLink === totalPages) {
                     startLink = Math.max(1, totalPages - maxLinks + 1);
                 }

                 if (startLink > 1) {
                     paginationHTML += `<a href="#" class="btn btn-page fw4a-goto-page" data-page="1">1</a>`;
                     if (startLink > 2) {
                         paginationHTML += `<span class="btn btn-page disabled">...</span>`;
                     }
                 }

                 for (let i = startLink; i <= endLink; i++) {
                     if (i === fw4aCurrentPage) {
                         paginationHTML += `<a href="#" class="btn btn-page active fw4a-goto-page" data-page="${i}">${i}</a>`;
                     } else {
                         paginationHTML += `<a href="#" class="btn btn-page fw4a-goto-page" data-page="${i}">${i}</a>`;
                     }
                 }

                 if (endLink < totalPages) {
                     if (endLink < totalPages - 1) {
                         paginationHTML += `<span class="btn btn-page disabled">...</span>`;
                     }
                     paginationHTML += `<a href="#" class="btn btn-page fw4a-goto-page" data-page="${totalPages}">${totalPages}</a>`;
                 }
             } else if (totalPages === 1) {
                 paginationHTML += `<a href="#" class="btn btn-page active fw4a-goto-page" data-page="1">1</a>`;
             }

             // Next button
             if (fw4aCurrentPage < totalPages) {
                 paginationHTML += `<a href="#" class="btn btn-nav fw4a-next-page">Next</a>`;
             } else {
                 paginationHTML += `<span class="btn btn-nav disabled">Next</span>`;
             }

             paginationNav.innerHTML = paginationHTML;

             // Add event listeners to pagination controls
             document.querySelectorAll('.fw4a-goto-page').forEach(button => {
                 button.addEventListener('click', (e) => {
                     e.preventDefault();
                     const page = parseInt(button.getAttribute('data-page'));
                     fw4aCurrentPage = page;
                     populateFW4ATable();
                 });
             });

             const prevButton = document.querySelector('.fw4a-prev-page');
             if (prevButton) {
                 prevButton.addEventListener('click', (e) => {
                     e.preventDefault();
                     if (fw4aCurrentPage > 1) {
                         fw4aCurrentPage--;
                         populateFW4ATable();
                     }
                 });
             }

             const nextButton = document.querySelector('.fw4a-next-page');
             if (nextButton) {
                 nextButton.addEventListener('click', (e) => {
                     e.preventDefault();
                     if (fw4aCurrentPage < totalPages) {
                         fw4aCurrentPage++;
                         populateFW4ATable();
                     }
                 });
             }
         }


         // --- Event Listeners ---
         // FW4A Report Tab Event Listeners
         document.addEventListener('DOMContentLoaded', function() {
             // If we're on the reports tab, load the data
             if (currentActiveTab === 'reports') {
                 loadFW4AReportData();
             }

             // Default rows per page value
             fw4aRowsPerPage = 10; // Set a default value since the selector is removed
         });

         if (importButton) { importButton.addEventListener('click', () => {
             if (currentActiveTab === 'participants') {
                 currentImportType = 'participant';
             } else if (currentActiveTab === 'letters') {
                 currentImportType = 'letter';
             } else {
                 currentImportType = 'activity';
             }
             openModal('import');
         }); }
         if (exportButton) { exportButton.addEventListener('click', () => { openModal('export'); }); }
         if (exportDataButton) { exportDataButton.addEventListener('click', async () => {
             const exportType = document.querySelector('input[name="exportType"]:checked')?.value || 'all';
             const exportFormat = document.querySelector('input[name="exportFormat"]:checked')?.value || 'csv';

             let activeType, action, typeName, typeNamePlural;

             if (currentActiveTab === 'participants') {
                 activeType = 'participant';
                 action = 'exportParticipants';
                 typeName = 'LGU';
                 typeNamePlural = 'LGUs';
             } else if (currentActiveTab === 'letters') {
                 activeType = 'letter';
                 action = 'exportLetters';
                 typeName = 'Letter Request';
                 typeNamePlural = 'Letter Requests';
             } else {
                 activeType = 'activity';
                 action = 'exportActivities';
                 typeName = 'Activity';
                 typeNamePlural = 'Activities';
             }

             exportProcessingIndicator.style.display = 'flex';
             exportErrorIndicator.style.display = 'none';
             exportDataButton.disabled = true;
             const exportFormData = new FormData();
             exportFormData.append('action', action);
             exportFormData.append('format', exportFormat);
             exportFormData.append('type', exportType);
             const currentParams = new URLSearchParams(window.location.search);
             if (exportType === 'filtered') {
                 if (activeType === 'activity') {
                     exportFormData.append('search', currentParams.get('search') || '');
                     <?php foreach ($filter_columns_activity as $column): ?>
                     exportFormData.append('<?php echo $column; ?>', currentParams.get('<?php echo $column; ?>') || '');
                     <?php endforeach; ?>
                 } else if (activeType === 'letter') {
                     exportFormData.append('l_search', currentParams.get('l_search') || '');
                     <?php foreach ($filter_columns_letter as $column): ?>
                     exportFormData.append('l_<?php echo $column; ?>', currentParams.get('l_<?php echo $column; ?>') || '');
                     <?php endforeach; ?>
                 } else {
                     exportFormData.append('p_search', currentParams.get('p_search') || '');
                     <?php foreach ($filter_columns_participant as $column): ?>
                     exportFormData.append('p_<?php echo $column; ?>', currentParams.get('p_<?php echo $column; ?>') || '');
                     <?php endforeach; ?>
                 }
             } else if (exportType === 'selected') { const selectedIds = getSelectedIds(activeType); if (selectedIds.length === 0) { showNotification(`No ${typeNamePlural.toLowerCase()} selected for export.`, "warning", 3000); exportProcessingIndicator.style.display = 'none'; exportDataButton.disabled = false; return; } selectedIds.forEach(id => exportFormData.append('ids[]', id)); } try { const response = await fetch(ajaxHandlerUrl, { method: 'POST', body: exportFormData }); if (!response.ok) { let errorMsg = `Export failed (Status: ${response.status}).`; try { const errData = await response.json(); errorMsg = errData.message || errorMsg; } catch (e) { errorMsg = await response.text(); } throw new Error(errorMsg); } const disposition = response.headers.get('Content-Disposition'); let filename = `${activeType}s_export_${new Date().toISOString().slice(0,10)}.csv`; if (disposition && disposition.indexOf('attachment') !== -1) { const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/; const matches = filenameRegex.exec(disposition); if (matches != null && matches[1]) { filename = matches[1].replace(/['"]/g, ''); } } const blob = await response.blob(); const url = window.URL.createObjectURL(blob); const a = document.createElement('a'); a.style.display = 'none'; a.href = url; a.download = filename; document.body.appendChild(a); a.click(); window.URL.revokeObjectURL(url); a.remove(); showNotification(`${typeName} export successful. Download started.`, 'success'); closeModal('export'); } catch (error) { console.error('Export error:', error); exportErrorIndicator.textContent = `Error during export: ${error.message}`; exportErrorIndicator.style.display = 'block'; showNotification(`Export failed: ${error.message}`, 'error', 5000); } finally { exportProcessingIndicator.style.display = 'none'; exportDataButton.disabled = false; } }); }

         tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = tab.getAttribute('data-tab');
                if (tabId === currentActiveTab) return;

                const previousActiveTab = currentActiveTab;
                currentActiveTab = tabId;

                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                tab.classList.add('active');
                const activeTabContent = document.getElementById(tabId + '-tab');
                if (activeTabContent) activeTabContent.classList.add('active');

                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('tab', tabId);
                // Also update the year param in URL when switching tabs if it came from the filter
                 currentUrl.searchParams.set('year', currentReportYear);
                window.history.pushState({ path: currentUrl.href }, '', currentUrl.href);

                // --- Manage button/filter visibility using JS ---
                const dynamicImportButton = document.getElementById('importButton');
                const dynamicExportButton = document.getElementById('exportButton');
                const dynamicManageTargetsButton = document.getElementById('manageTargetsButton');
                const dynamicReportYearFilter = document.getElementById('reportYearFilter'); // Get year filter


                if (currentActiveTab === 'reports') {
                    if (dynamicImportButton) dynamicImportButton.style.display = 'none';
                    if (dynamicExportButton) dynamicExportButton.style.display = 'none';
                    // Manage targets button and year filter are removed
                } else {
                     // Ensure Import/Export are visible (re-add if needed, simpler to just show/hide)
                     if (dynamicImportButton) dynamicImportButton.style.display = 'inline-flex';
                     if (dynamicExportButton) dynamicExportButton.style.display = 'inline-flex';
                }
                // --- End button visibility ---

                // If switching to reports tab, load the data
                if (currentActiveTab === 'reports') {
                    loadFW4AReportData();
                }

                updateActionBar(); // Update for potential selection changes
                // Clear selections when switching tabs
                if (selectAllCheckboxActivity) selectAllCheckboxActivity.checked = false;
                if (selectAllCheckboxParticipant) selectAllCheckboxParticipant.checked = false;
                if (selectAllCheckboxLetter) selectAllCheckboxLetter.checked = false;
                activityRowCheckboxes.forEach(cb => cb.checked = false);
                participantRowCheckboxes.forEach(cb => cb.checked = false);
                letterRowCheckboxes.forEach(cb => cb.checked = false);
                updateActionBar(); // Update again after clearing
            });
         });


         if (selectAllCheckboxActivity) { selectAllCheckboxActivity.addEventListener('change', () => { const isChecked = selectAllCheckboxActivity.checked; activityRowCheckboxes.forEach(cb => { cb.checked = isChecked; }); updateActionBar(); }); }
         if (selectAllCheckboxParticipant) { selectAllCheckboxParticipant.addEventListener('change', () => { const isChecked = selectAllCheckboxParticipant.checked; participantRowCheckboxes.forEach(cb => { cb.checked = isChecked; }); updateActionBar(); }); }
         if (selectAllCheckboxLetter) { selectAllCheckboxLetter.addEventListener('change', () => { const isChecked = selectAllCheckboxLetter.checked; letterRowCheckboxes.forEach(cb => { cb.checked = isChecked; }); updateActionBar(); }); }
         activityRowCheckboxes.forEach(checkbox => { checkbox.addEventListener('change', () => { updateActionBar(); }); });
         participantRowCheckboxes.forEach(checkbox => { checkbox.addEventListener('change', () => { updateActionBar(); }); });
         letterRowCheckboxes.forEach(checkbox => { checkbox.addEventListener('change', () => { updateActionBar(); }); });
         if(closeActionBarButton) {
             closeActionBarButton.addEventListener('click', () => {
                 let activeType = 'activity';
                 if (currentActiveTab === 'participants') {
                     activeType = 'participant';
                 } else if (currentActiveTab === 'letters') {
                     activeType = 'letter';
                 }

                 let selectAllCheckbox, rowCheckboxes;
                 if (activeType === 'activity') {
                     selectAllCheckbox = selectAllCheckboxActivity;
                     rowCheckboxes = activityRowCheckboxes;
                 } else if (activeType === 'participant') {
                     selectAllCheckbox = selectAllCheckboxParticipant;
                     rowCheckboxes = participantRowCheckboxes;
                 } else if (activeType === 'letter') {
                     selectAllCheckbox = selectAllCheckboxLetter;
                     rowCheckboxes = letterRowCheckboxes;
                 }

                 if (selectAllCheckbox) selectAllCheckbox.checked = false;
                 rowCheckboxes.forEach(cb => { cb.checked = false; });
                 updateActionBar();
             });
         }
         if (addActivityButton) { addActivityButton.addEventListener('click', () => openModal('addActivity')); }
         if (addParticipantButton) { addParticipantButton.addEventListener('click', () => openModal('addParticipant')); }
         // Add Letter button event listener
         const addLetterButton = document.getElementById('addLetterButton');
         if (addLetterButton) { addLetterButton.addEventListener('click', () => openModal('addLetter')); }
         // Strategy Table button event listener
         const strategyTableButton = document.getElementById('strategyTableButton');
         if (strategyTableButton) { strategyTableButton.addEventListener('click', () => openModal('strategyTable')); }
         if (editButton) {
             editButton.addEventListener('click', () => {
                 if (!editButton.disabled) {
                     let modalKey = 'editActivity';
                     if (currentActiveTab === 'participants') {
                         modalKey = 'editParticipant';
                     } else if (currentActiveTab === 'letters') {
                         modalKey = 'editLetter';
                     }
                     openModal(modalKey);
                 }
             });
         }
         if (deleteButton) {
             deleteButton.addEventListener('click', () => {
                 if (!deleteButton.disabled) {
                     let activeType = 'activity';
                     if (currentActiveTab === 'participants') {
                         activeType = 'participant';
                     } else if (currentActiveTab === 'letters') {
                         activeType = 'letter';
                     }
                     const selectedIds = getSelectedIds(activeType);
                     deleteContext = { type: activeType, ids: selectedIds };
                     openModal('delete');
                 }
             });
         }
         if (uploadButton) {
             uploadButton.addEventListener('click', () => {
                 if (!uploadButton.disabled) {
                     openModal('upload');
                 }
             });
         }
         if (viewFilesButton) {
             viewFilesButton.addEventListener('click', () => {
                 if (!viewFilesButton.disabled) {
                     openModal('view');
                 }
             });
         }
         document.querySelectorAll('.close-modal').forEach(button => { button.addEventListener('click', () => { const modalId = button.closest('.modal')?.id; const modalKey = Object.keys(modals).find(key => modals[key]?.id === modalId); if(modalKey) { closeModal(modalKey); } else { console.warn("Could not find modal key for ID:", modalId); button.closest('.modal')?.classList.remove('visible'); if (!Object.values(modals).some(m => m?.classList.contains('visible'))) { document.body.style.overflow = ''; } } }); });
         Object.values(modals).forEach(modal => { if(modal) { modal.addEventListener('click', (event) => { if (event.target === modal) { const modalKey = Object.keys(modals).find(key => modals[key] === modal); if (modalKey) { closeModal(modalKey); } else modal.classList.remove('visible'); } }); } });
         if (viewFilesSelectAll) { viewFilesSelectAll.addEventListener('change', () => { const isChecked = viewFilesSelectAll.checked; viewFilesModal.querySelectorAll('.file-select-checkbox').forEach(cb => { cb.checked = isChecked; }); updateViewFilesControls(); }); }
         if (viewFilesDeleteSelected) { viewFilesDeleteSelected.addEventListener('click', async () => { const checkedCheckboxes = viewFilesModal.querySelectorAll('.file-select-checkbox:checked'); const fileIdsToDelete = Array.from(checkedCheckboxes).map(cb => cb.value); if (fileIdsToDelete.length === 0) { showNotification("No files selected for deletion.", "info"); return; } viewFilesDeleteSelected.disabled = true; viewFilesDeleteSelected.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
             // Determine the file type based on the current active tab and view files type
             let fileTypeParam;
             console.log("Current view files type:", currentViewFilesType);
             console.log("Current active tab:", currentActiveTab);
             if (currentViewFilesType === 'fw4a') {
                 fileTypeParam = 'fw4a';
                 console.log("Setting file type parameter to: fw4a");
             } else if (currentViewFilesType === 'letter') {
                 fileTypeParam = 'letter';
                 console.log("Setting file type parameter to: letter");
             } else {
                 fileTypeParam = 'activity';
                 console.log("Setting file type parameter to: activity");
             }
             console.log("Sending deleteMultipleFiles request with IDs:", fileIdsToDelete, "and type:", fileTypeParam);
             const result = await performAjax('deleteMultipleFiles', { file_ids: fileIdsToDelete, type: fileTypeParam });
             console.log("Delete result:", result);
             viewFilesDeleteSelected.innerHTML = '<i class="fas fa-trash"></i> Delete Selected';
             viewFilesDeleteSelected.disabled = false;
             if (result.success) {
                 showNotification(result.message || `${result.data?.deleted_count || 0} file(s) deleted successfully.`, 'success');
                 const activeType = currentActiveTab === 'participants' ? 'participant' : (currentActiveTab === 'letters' ? 'letter' : 'activity');
                 const parentIds = getSelectedIds(activeType);
                 if (parentIds.length > 0) {
                     if (currentActiveTab === 'activities') {
                         fetchActivityFiles(parentIds);
                     } else {
                         fetchParticipantFiles(parentIds);
                     }
                 } else {
                     if(fileGroupContainer) fileGroupContainer.innerHTML = '';
                     if(viewFilesEmptyMessage) viewFilesEmptyMessage.style.display = 'block';
                     updateViewFilesControls();
                 }
             } else {
                 showNotification(`Error deleting files: ${result.message || 'Unknown error'}`, 'error', 5000);
                 updateViewFilesControls();
             }
         }); }
         if (fileGroupContainer) { fileGroupContainer.addEventListener('change', (event) => { if (event.target.classList.contains('file-select-checkbox')) { updateViewFilesControls(); } }); }
         if (confirmDeleteButton) { confirmDeleteButton.addEventListener('click', async () => { if (!deleteContext || (deleteContext.type !== 'activity' && deleteContext.type !== 'participant' && deleteContext.type !== 'letter')) { console.error("Confirm delete clicked with invalid or missing context:", deleteContext); showNotification("Error: Cannot determine items to delete. Please try again.", "error"); closeModal('delete'); return; } if (!deleteContext.ids || deleteContext.ids.length === 0) { console.warn("Confirm delete clicked but no IDs found in context:", deleteContext); showNotification("No items were selected for deletion.", "warning"); closeModal('delete'); return; } confirmDeleteButton.disabled = true; confirmDeleteButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';

             let deleteAction;
             if (deleteContext.type === 'activity') {
                 deleteAction = 'deleteActivities';
             } else if (deleteContext.type === 'participant') {
                 deleteAction = 'deleteParticipants';
             } else if (deleteContext.type === 'letter') {
                 deleteAction = 'deleteLetters';
             }

             const result = await performAjax(deleteAction, { ids: deleteContext.ids }); confirmDeleteButton.disabled = false; confirmDeleteButton.innerHTML = 'Delete'; closeModal('delete'); if (result.success) { const itemTypeText = deleteContext.type === 'participant' ? 'location/s' : (deleteContext.type === 'letter' ? 'letter request(s)' : `${deleteContext.type}(s)`); showNotification(result.message || `Selected ${itemTypeText} deleted successfully.`, 'success'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', currentActiveTab); if (!currentSearch.get('search')) currentSearch.delete('search'); if (!currentSearch.get('limit')) currentSearch.delete('limit'); window.location.search = currentSearch.toString(); } else { const itemTypeText = deleteContext.type === 'participant' ? 'location/s' : (deleteContext.type === 'letter' ? 'letter request(s)' : `${deleteContext.type}(s)`); showNotification(`Error deleting ${itemTypeText}: ${result.message || 'Unknown error'}`, 'error', 5000); } }); }

         // Manage Targets button listener removed

         // Listener for Target Form Reset Button
         if (resetTargetFormButton) {
            resetTargetFormButton.addEventListener('click', resetTargetForm);
         }

         // Listener for Target Form Submission
         if (targetForm && saveTargetButton) {
            targetForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                saveTargetButton.disabled = true;
                saveTargetButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

                const formData = new FormData(targetForm);
                const data = Object.fromEntries(formData.entries());
                const action = data.id ? 'updateTarget' : 'addTarget'; // Determine action based on hidden ID field

                const result = await performAjax(action, data);

                if (result.success) {
                    showNotification(result.message || `Target ${action === 'updateTarget' ? 'updated' : 'added'} successfully!`, 'success');
                    resetTargetForm();
                     // Refresh the table IF the modal is currently visible (might be closed by now)
                    if (modals.manageTargets && modals.manageTargets.classList.contains('visible')) {
                        openManageTargetsModal();
                    }
                    fetchAndRenderGraphs(); // Refresh graphs outside the modal
                } else {
                    showNotification(`Error ${action === 'updateTarget' ? 'updating' : 'adding'} target: ${result.message || 'Unknown error'}`, 'error', 5000);
                    saveTargetButton.disabled = false; // Re-enable button on error
                    saveTargetButton.innerHTML = data.id ? '<i class="fas fa-save"></i> Update Target' : '<i class="fas fa-save"></i> Save Target';
                }
            });
         }

         // Report Year Filter change listener removed


         // --- Form Submissions ---
         // (Existing form submissions for add/edit activity/participant, upload)
         const addLetterForm = document.getElementById('addLetterForm');
         const saveAddLetterButton = document.getElementById('saveAddLetterButton');
         if (addLetterForm && saveAddLetterButton) {
             addLetterForm.addEventListener('submit', async (event) => {
                 event.preventDefault();
                 saveAddLetterButton.disabled = true;
                 saveAddLetterButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                 const formData = new FormData(addLetterForm);
                 const data = Object.fromEntries(formData.entries());
                 const result = await performAjax('addLetter', data);
                 if (result.success) {
                     showNotification(result.message || 'Letter request added successfully!', 'success');
                     closeModal('addLetter');
                     const currentSearch = new URLSearchParams(window.location.search);
                     currentSearch.set('tab', 'letters');
                     window.location.search = currentSearch.toString();
                 } else {
                     showNotification(`Error adding letter request: ${result.message || 'Unknown error'}`, 'error', 5000);
                     saveAddLetterButton.disabled = false;
                     saveAddLetterButton.innerHTML = '<i class="fas fa-save"></i> Save Letter';
                 }
             });
         }

         const addActivityForm = document.getElementById('addActivityForm');
         const saveAddActivityButton = document.getElementById('saveAddActivityButton');
         if (addActivityForm && saveAddActivityButton) { addActivityForm.addEventListener('submit', async (event) => { event.preventDefault(); saveAddActivityButton.disabled = true; saveAddActivityButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...'; const formData = new FormData(addActivityForm); const data = Object.fromEntries(formData.entries()); const result = await performAjax('addActivity', data); if (result.success) { showNotification(result.message || 'Activity added successfully!', 'success'); closeModal('addActivity'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', 'activities'); window.location.search = currentSearch.toString(); } else { showNotification(`Error adding activity: ${result.message || 'Unknown error'}`, 'error', 5000); saveAddActivityButton.disabled = false; saveAddActivityButton.innerHTML = 'Save Activity'; } }); }
         const addParticipantForm = document.getElementById('addParticipantForm');
         const saveAddParticipantButton = document.getElementById('saveAddParticipantButton');
         if (addParticipantForm && saveAddParticipantButton) { addParticipantForm.addEventListener('submit', async (event) => { event.preventDefault(); saveAddParticipantButton.disabled = true; saveAddParticipantButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...'; const formData = new FormData(addParticipantForm); const data = Object.fromEntries(formData.entries()); const result = await performAjax('addParticipant', data); if (result.success) { showNotification(result.message || 'Participant added successfully!', 'success'); closeModal('addParticipant'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', 'participants'); window.location.search = currentSearch.toString(); } else { showNotification(`Error adding participant: ${result.message || 'Unknown error'}`, 'error', 5000); saveAddParticipantButton.disabled = false; saveAddParticipantButton.innerHTML = 'Save Participant'; } }); }
         const editActivityForm = document.getElementById('editActivityForm');
         const saveEditActivityButton = document.getElementById('saveEditActivityButton');
         if (editActivityForm && saveEditActivityButton) { editActivityForm.addEventListener('submit', async (event) => { event.preventDefault(); saveEditActivityButton.disabled = true; saveEditActivityButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...'; const formData = new FormData(editActivityForm); const data = Object.fromEntries(formData.entries()); data.id = document.getElementById('editActivityFormId').value; const result = await performAjax('updateActivity', data); if (result.success) { showNotification(result.message || 'Activity updated successfully!', 'success'); closeModal('editActivity'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', 'activities'); window.location.search = currentSearch.toString(); } else { showNotification(`Error updating activity: ${result.message || 'Unknown error'}`, 'error', 5000); saveEditActivityButton.disabled = false; saveEditActivityButton.innerHTML = 'Save Changes'; } }); }

         const editLetterForm = document.getElementById('editLetterForm');
         const saveEditLetterButton = document.getElementById('saveEditLetterButton');
         if (editLetterForm && saveEditLetterButton) {
             editLetterForm.addEventListener('submit', async (event) => {
                 event.preventDefault();
                 saveEditLetterButton.disabled = true;
                 saveEditLetterButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                 const formData = new FormData(editLetterForm);
                 const data = Object.fromEntries(formData.entries());
                 data.id = document.getElementById('editLetterFormId').value;
                 const result = await performAjax('updateLetter', data);
                 if (result.success) {
                     showNotification(result.message || 'Letter request updated successfully!', 'success');
                     closeModal('editLetter');
                     const currentSearch = new URLSearchParams(window.location.search);
                     currentSearch.set('tab', 'letters');
                     window.location.search = currentSearch.toString();
                 } else {
                     showNotification(`Error updating letter request: ${result.message || 'Unknown error'}`, 'error', 5000);
                     saveEditLetterButton.disabled = false;
                     saveEditLetterButton.innerHTML = 'Save Changes';
                 }
             });
         }

         const editParticipantForm = document.getElementById('editParticipantForm');
         const saveEditParticipantButton = document.getElementById('saveEditParticipantButton');
         if (editParticipantForm && saveEditParticipantButton) { editParticipantForm.addEventListener('submit', async (event) => { event.preventDefault(); saveEditParticipantButton.disabled = true; saveEditParticipantButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...'; const formData = new FormData(editParticipantForm); const data = Object.fromEntries(formData.entries()); data.id = document.getElementById('editParticipantFormId').value; const result = await performAjax('updateParticipant', data); if (result.success) { showNotification(result.message || 'Participant updated successfully!', 'success'); closeModal('editParticipant'); const currentSearch = new URLSearchParams(window.location.search); currentSearch.set('tab', 'participants'); window.location.search = currentSearch.toString(); } else { showNotification(`Error updating participant: ${result.message || 'Unknown error'}`, 'error', 5000); saveEditParticipantButton.disabled = false; saveEditParticipantButton.innerHTML = 'Save Changes'; } }); }
         if (uploadForm && submitUploadButton) { uploadForm.addEventListener('submit', async (event) => { event.preventDefault(); const files = fileInput?.files; const activeType = currentActiveTab === 'participants' ? 'participant' : (currentActiveTab === 'letters' ? 'letter' : 'activity'); const parentIdsToUpload = getSelectedIds(activeType); if (!files || files.length === 0) { showNotification('Please select at least one file.', 'error'); return; } if (parentIdsToUpload.length === 0) { showNotification(`No ${activeType === 'participant' ? 'location/s' : (activeType === 'letter' ? 'letter request(s)' : activeType + 's')} selected for file upload.`, 'error'); return; } submitUploadButton.disabled = true; submitUploadButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
             // Use the correct upload action based on the active tab
             let uploadAction;
             if (activeType === 'activity') {
                 uploadAction = 'uploadFiles';
             } else if (activeType === 'participant') {
                 // For freewifi4all.php, we use the FW4A upload endpoint
                 uploadAction = 'uploadFW4AFiles';
             } else if (activeType === 'letter') {
                 // For letter requests, use the letter upload endpoint
                 uploadAction = 'uploadLetterFiles';
             } else {
                 uploadAction = 'uploadParticipantFiles';
             }
             const result = await performAjax(uploadAction, { ids: parentIdsToUpload, files: files }); submitUploadButton.disabled = false; submitUploadButton.innerHTML = '<i class="fas fa-upload"></i> Upload'; if (result.success) { showNotification(result.message || `${result.data?.uploaded_count || '?'} file(s) uploaded successfully!`, 'success'); closeModal('upload'); } else { showNotification(`Error uploading files: ${result.message || 'Unknown error'}`, 'error', 5000); } }); }

         // --- Import Listeners ---
         if (csvFileInput) { csvFileInput.addEventListener('change', (event) => { selectedFile = event.target.files[0]; if (selectedFile) { if (!selectedFile.name.toLowerCase().endsWith('.csv')) { showImportError('Please select a CSV file.', 1); fileNameDisplay.textContent = 'Invalid file type'; selectedFile = null; previewDataButton.disabled = true; return; } fileNameDisplay.textContent = selectedFile.name; previewDataButton.disabled = false; importDataButton.disabled = true; importErrorStep1.style.display = 'none'; importStep2.style.display = 'none'; previewTableContainer.style.display = 'none'; } else { fileNameDisplay.textContent = 'No file chosen'; previewDataButton.disabled = true; selectedFile = null; } }); }
         if (previewDataButton) { previewDataButton.addEventListener('click', () => { if (!selectedFile) { showImportError('No file selected.', 1); return; } previewDataButton.disabled = true; importDataButton.disabled = true; importStep2.style.display = 'block'; previewLoadingIndicator.style.display = 'flex'; importErrorStep2.style.display = 'none'; previewTableContainer.style.display = 'none'; if(previewTableHead) previewTableHead.innerHTML = ''; if(previewTableBody) previewTableBody.innerHTML = ''; if(previewRowCount) previewRowCount.textContent = '';

             let requiredCols;
             if (currentImportType === 'activity') {
                 requiredCols = requiredColumnsActivity;
             } else if (currentImportType === 'letter') {
                 requiredCols = requiredColumnsLetter;
             } else {
                 requiredCols = requiredColumnsParticipant;
             } Papa.parse(selectedFile, { header: true, skipEmptyLines: true, complete: (results) => { previewLoadingIndicator.style.display = 'none'; if (results.errors.length > 0) { showImportError(`Error parsing CSV: ${results.errors[0].message}. Check row ${results.errors[0].row + 1}.`, 2); previewDataButton.disabled = false; return; } if (!results.data || results.data.length === 0) { showImportError('CSV file appears empty.', 2); previewDataButton.disabled = false; return; } const headers = results.meta.fields; const lowerCaseHeaders = headers.map(h => h.toLowerCase().trim()); const lowerCaseRequired = requiredCols.map(rc => rc.toLowerCase().trim()); const missingColumns = []; lowerCaseRequired.forEach(reqCol => { if (!lowerCaseHeaders.includes(reqCol)) { const originalCaseCol = requiredCols.find(rc => rc.toLowerCase().trim() === reqCol) || reqCol; missingColumns.push(originalCaseCol); } }); if (missingColumns.length > 0) { showImportError(`Missing required columns: ${missingColumns.join(', ')}.`, 2); previewDataButton.disabled = false; return; }

                // Validate that all records have the correct project value - only for activity and participant imports
                if (currentImportType !== 'letter') {
                    const currentProject = '<?php echo addslashes(htmlspecialchars($project_filter_value)); ?>'; // <-- Use correct PHP variable
                    const projectColumnName = currentImportType === 'activity' ? 'Bureau' : 'Project';

                    // Find the actual project column name in the CSV (case-insensitive)
                    let actualProjectColumn = null;
                    for (const header of headers) {
                        if (header.toLowerCase().trim() === projectColumnName.toLowerCase().trim()) {
                            actualProjectColumn = header;
                            break;
                        }
                    }

                    if (actualProjectColumn) {
                        // Check if any rows have a different project value
                        const invalidRows = [];
                        results.data.forEach((row, index) => {
                            const rowProject = row[actualProjectColumn]?.trim();
                            if (rowProject && rowProject !== currentProject) {
                                invalidRows.push({
                                    rowIndex: index + 2, // +2 because index is 0-based and we skip header row
                                    project: rowProject
                                });
                            }
                        });

                        if (invalidRows.length > 0) {
                            // Format error message with details about invalid rows
                            let errorMsg = `This file contains data for projects other than "${currentProject}". `;
                            errorMsg += `Found ${invalidRows.length} row(s) with different project values:\n`;

                            // Show up to 5 examples
                            const examples = invalidRows.slice(0, 5);
                            examples.forEach(row => {
                                errorMsg += `- Row ${row.rowIndex}: "${row.project}"\n`;
                            });

                            if (invalidRows.length > 5) {
                                errorMsg += `...and ${invalidRows.length - 5} more.\n`;
                            }

                            errorMsg += `\nPlease upload a file containing only "${currentProject}" data.`;

                            showImportError(errorMsg, 2);
                            previewDataButton.disabled = false;
                            return;
                        }
                    }
                }

                parsedDataForImport = results.data;
                displayPreviewTable(headers, results.data);
                importDataButton.disabled = false;
                showImportMessage('Preview ready. Review data and click "Import Data".', 2);
            },
            error: (error) => {
                previewLoadingIndicator.style.display = 'none';
                showImportError(`Error reading file: ${error.message}`, 2);
                previewDataButton.disabled = false;
            }
        });
    });
}
         if (importDataButton) { importDataButton.addEventListener('click', async () => { if (!parsedDataForImport || parsedDataForImport.length === 0) { showImportError('No data available to import.', 2); return; } importDataButton.disabled = true; previewDataButton.disabled = true; importProcessingIndicator.style.display = 'flex'; importErrorStep2.style.display = 'none'; importResultIndicator.style.display = 'none';

             let importAction, dataKey, typeNamePlural;

             if (currentImportType === 'activity') {
                 importAction = 'importActivities';
                 dataKey = 'activities';
                 typeNamePlural = 'Activities';
             } else if (currentImportType === 'letter') {
                 importAction = 'importLetters';
                 dataKey = 'letters';
                 typeNamePlural = 'Letter Requests';
             } else {
                 importAction = 'importParticipants';
                 dataKey = 'participants';
                 typeNamePlural = 'LGUs';
             }
             const result = await performAjax(importAction, { [dataKey]: parsedDataForImport }); importProcessingIndicator.style.display = 'none'; if (result.success) { importResultIndicator.textContent = result.message || `Successfully imported ${result.data?.imported_count || 0} ${typeNamePlural}.`; importResultIndicator.className = 'success-indicator'; importResultIndicator.style.display = 'block'; showNotification(result.message || `Successfully imported ${result.data?.imported_count || 0} ${typeNamePlural}.`, 'success', 5000); setTimeout(() => { closeModal('import'); const currentSearch = new URLSearchParams(window.location.search);
                 if (currentImportType === 'activity') {
                     currentSearch.set('tab', 'activities');
                 } else if (currentImportType === 'letter') {
                     currentSearch.set('tab', 'letters');
                 } else {
                     currentSearch.set('tab', 'participants');
                 }
                 window.location.search = currentSearch.toString();
             }, 2000); } else { let errorDetails = result.data?.errors ? result.data.errors.join('\n') : 'Unknown error details.'; importResultIndicator.textContent = `Import failed: ${result.message || 'Unknown error'}.\n${result.data?.errors ? 'Details:\n' + errorDetails : ''}`; importResultIndicator.className = 'error-indicator'; importResultIndicator.style.whiteSpace = 'pre-wrap'; importResultIndicator.style.display = 'block'; showNotification(`Import failed: ${result.message || 'Unknown error'}. Check modal for details.`, 'error', 7000); previewDataButton.disabled = false; } }); }

         // --- Strategy Table Functions ---
         let strategyTableData = null; // Store the full dataset
         let strategyTableCurrentPage = 1;
         let strategyTableRowsPerPage = 10; // Default to 10 rows per page

         // Function to render the strategy table pagination
         function renderStrategyTablePagination(totalItems) {
             const strategyTablePagination = document.getElementById('strategyTablePagination');
             const strategyTablePaginationInfo = document.getElementById('strategyTablePaginationInfo');
             const strategyTablePaginationNav = document.getElementById('strategyTablePaginationNav');

             if (!strategyTablePagination || !strategyTablePaginationInfo || !strategyTablePaginationNav) {
                 console.error('Strategy table pagination elements not found');
                 return;
             }

             // Calculate pagination values
             const totalPages = Math.ceil(totalItems / strategyTableRowsPerPage);
             const startIndex = (strategyTableCurrentPage - 1) * strategyTableRowsPerPage + 1;
             const endIndex = Math.min(startIndex + strategyTableRowsPerPage - 1, totalItems);

             // Update pagination info text with fixed layout
             strategyTablePaginationInfo.textContent = `Showing ${startIndex} - ${endIndex} of ${totalItems} municipalities`;

             // Create pagination navigation with fixed layout
             let paginationHtml = '';

             // Previous button - always show with consistent width
             if (strategyTableCurrentPage > 1) {
                 paginationHtml += `<a href="#" class="btn btn-nav" data-page="${strategyTableCurrentPage - 1}">Previous</a>`;
             } else {
                 paginationHtml += `<span class="btn btn-nav disabled">Previous</span>`;
             }

             // Page numbers with fixed layout
             const maxLinks = 5;
             let startLink = Math.max(1, strategyTableCurrentPage - Math.floor(maxLinks / 2));
             let endLink = Math.min(totalPages, startLink + maxLinks - 1);

             // Adjust start link if end link reaches total pages prematurely
             if (endLink === totalPages && totalPages >= maxLinks) {
                 startLink = Math.max(1, totalPages - maxLinks + 1);
             }

             // First page and ellipsis
             if (startLink > 1) {
                 paginationHtml += `<a href="#" class="btn btn-page" data-page="1">1</a>`;
                 if (startLink > 2) {
                     paginationHtml += `<span class="btn btn-page disabled">...</span>`;
                 }
             }

             // Page links
             for (let i = startLink; i <= endLink; i++) {
                 if (i === strategyTableCurrentPage) {
                     paginationHtml += `<span class="btn btn-page active">${i}</span>`;
                 } else {
                     paginationHtml += `<a href="#" class="btn btn-page" data-page="${i}">${i}</a>`;
                 }
             }

             // Last page and ellipsis
             if (endLink < totalPages) {
                 if (endLink < totalPages - 1) {
                     paginationHtml += `<span class="btn btn-page disabled">...</span>`;
                 }
                 paginationHtml += `<a href="#" class="btn btn-page" data-page="${totalPages}">${totalPages}</a>`;
             }

             // Next button - always show with consistent width
             if (strategyTableCurrentPage < totalPages) {
                 paginationHtml += `<a href="#" class="btn btn-nav" data-page="${strategyTableCurrentPage + 1}">Next</a>`;
             } else {
                 paginationHtml += `<span class="btn btn-nav disabled">Next</span>`;
             }

             // Update the pagination navigation
             strategyTablePaginationNav.innerHTML = paginationHtml;

             // Ensure the pagination container is visible
             strategyTablePagination.style.display = 'flex';

             // Add event listeners to pagination buttons
             strategyTablePaginationNav.querySelectorAll('a.btn-nav, a.btn-page').forEach(button => {
                 button.addEventListener('click', (e) => {
                     e.preventDefault();
                     const page = parseInt(button.getAttribute('data-page'));
                     if (page && page !== strategyTableCurrentPage) {
                         strategyTableCurrentPage = page;
                         renderStrategyTable();
                     }
                 });
             });
         }

         // Function to render the strategy table with current pagination settings
         function renderStrategyTable() {
             if (!strategyTableData) {
                 console.error('Strategy table data not loaded');
                 return;
             }

             const { strategies, data } = strategyTableData;
             const strategyTableHead = document.getElementById('strategyTableHead');
             const strategyTableBody = document.getElementById('strategyTableBody');
             const strategyTableContainer = document.getElementById('strategyTableContainer');

             if (!strategyTableHead || !strategyTableBody || !strategyTableContainer) {
                 console.error('Strategy table elements not found');
                 return;
             }

             // Create table header
             let headerRow = '<tr><th class="municipality-col">Municipality/City</th>';

             // Add a column for each strategy
             strategies.forEach(strategy => {
                 // Add data-strategy attribute to help with styling specific columns
                 headerRow += `<th class="strategy-col" data-strategy="${escapeHtml(strategy)}">${escapeHtml(strategy)}</th>`;
             });

             // Add total column
             headerRow += '<th class="total-col">Total</th></tr>';
             strategyTableHead.innerHTML = headerRow;

             // Calculate pagination
             const startIndex = (strategyTableCurrentPage - 1) * strategyTableRowsPerPage;
             const endIndex = Math.min(startIndex + strategyTableRowsPerPage, data.length);
             const paginatedData = data.slice(startIndex, endIndex);

             // Create table rows
             let tableRows = '';

             // Add a row for each municipality in the current page
             paginatedData.forEach(row => {
                 tableRows += `<tr>
                     <td class="municipality-col">${escapeHtml(row.municipality)}</td>`;

                 // Add a cell for each strategy
                 strategies.forEach(strategy => {
                     const count = row[strategy] || 0;
                     // Use standard styling for all cells
                     tableRows += `<td class="strategy-col" data-strategy="${escapeHtml(strategy)}">${count}</td>`;
                 });

                 // Add total cell with standard styling
                 tableRows += `<td class="total-col"><strong>${row.total}</strong></td>
                 </tr>`;
             });

             // Add a total row at the bottom
             if (data.length > 0) {
                 tableRows += '<tr class="total-row"><td class="municipality-col"><strong>Total</strong></td>';

                 // Calculate column totals for all data (not just paginated)
                 const columnTotals = {};
                 let grandTotal = 0;

                 data.forEach(row => {
                     strategies.forEach(strategy => {
                         columnTotals[strategy] = (columnTotals[strategy] || 0) + (row[strategy] || 0);
                         grandTotal += (row[strategy] || 0);
                     });
                 });

                 // Add total cells for each strategy
                 strategies.forEach(strategy => {
                     const total = columnTotals[strategy] || 0;
                     tableRows += `<td class="strategy-col" data-strategy="${escapeHtml(strategy)}"><strong>${total}</strong></td>`;
                 });

                 // Add grand total cell
                 tableRows += `<td class="total-col"><strong>${grandTotal}</strong></td></tr>`;
             }

             strategyTableBody.innerHTML = tableRows;

             // If no data, show message
             if (data.length === 0) {
                 strategyTableBody.innerHTML = '<tr><td colspan="' + (strategies.length + 2) + '" class="table-message">No strategy data available.</td></tr>';
             }

             // Update pagination
             renderStrategyTablePagination(data.length);
         }

         async function loadStrategyTableData() {
             const strategyTableLoading = document.getElementById('strategyTableLoading');
             const strategyTableError = document.getElementById('strategyTableError');
             const strategyTableErrorMessage = document.getElementById('strategyTableErrorMessage');
             const strategyTableContainer = document.getElementById('strategyTableContainer');
             const strategyTableControls = document.getElementById('strategyTableControls');
             const strategyTableRowsPerPageSelect = document.getElementById('strategyTableRowsPerPage');

             if (!strategyTableLoading || !strategyTableError || !strategyTableContainer ||
                 !strategyTableControls || !strategyTableRowsPerPageSelect) {
                 console.error('Strategy table elements not found');
                 return;
             }

             try {
                 // Fetch strategy table data
                 const result = await performAjax('getStrategyTable', {});

                 strategyTableLoading.style.display = 'none';

                 if (result.success && result.data) {
                     const { strategies, data } = result.data;

                     if (!strategies || !strategies.length || !data) {
                         strategyTableError.style.display = 'block';
                         strategyTableErrorMessage.textContent = 'No strategy data available.';
                         return;
                     }

                     // Store the data for pagination
                     strategyTableData = result.data;

                     // Show the controls
                     strategyTableControls.style.display = 'flex';
                     strategyTableContainer.style.display = 'block';

                     // Add event listener to rows per page select
                     strategyTableRowsPerPageSelect.addEventListener('change', () => {
                         strategyTableRowsPerPage = parseInt(strategyTableRowsPerPageSelect.value);
                         strategyTableCurrentPage = 1; // Reset to first page
                         renderStrategyTable();
                     });

                     // Render the table with pagination
                     renderStrategyTable();
                 } else {
                     strategyTableError.style.display = 'block';
                     strategyTableErrorMessage.textContent = result.message || 'Error loading strategy data.';
                 }
             } catch (error) {
                 console.error('Error loading strategy table data:', error);
                 strategyTableLoading.style.display = 'none';
                 strategyTableError.style.display = 'block';
                 strategyTableErrorMessage.textContent = 'Error loading strategy data: ' + (error.message || 'Unknown error');
             }
         }

         // Statistics Card Click Handlers
         const statCards = document.querySelectorAll('.stat-card');
         statCards.forEach(card => {
            card.addEventListener('click', async function() {
                const statKey = this.querySelector('[data-key]')?.dataset.key || this.dataset.key;
                if (!statKey) {
                    console.error('No stat key found for this card');
                    return;
                }

                const statTitle = this.querySelector('h4')?.textContent || 'Statistics Breakdown';
                openStatsModal(statKey, statTitle);
            });
         });

         // Function to open stats modal and load data
         async function openStatsModal(statKey, statTitle) {
            // Store the current statistic key
            currentStatKey = statKey;

            // Reset current data
            currentStatData = [];

            // Open the modal
            openModal('statsBreakdown');

            // Update modal title
            const titleEl = document.querySelector('#statsModalTitle');
            if (titleEl) titleEl.textContent = statTitle;

            try {
                // Show loading indicator
                document.querySelector('#statsBreakdownLoadingIndicator').style.display = 'block';
                document.querySelector('#statsBreakdownContent').style.display = 'none';
                document.querySelector('#statsBreakdownErrorIndicator').style.display = 'none';

                // Fetch data from server
                const result = await performAjax('getStatBreakdown', {
                    stat_key: statKey,
                    project: '<?php echo htmlspecialchars($project_filter_value); ?>'
                });

                handleStatBreakdownResult(result, statKey);
            } catch (error) {
                console.error('Error loading statistics:', error);
                document.querySelector('#statsBreakdownLoadingIndicator').style.display = 'none';
                document.querySelector('#statsBreakdownErrorIndicator').style.display = 'block';
                document.querySelector('#statsBreakdownErrorIndicator span').textContent = 'Error loading statistics data.';
            }
         }

         // Function to handle stat breakdown result
         function handleStatBreakdownResult(result, statKey) {
            document.querySelector('#statsBreakdownLoadingIndicator').style.display = 'none';

            if (result.success && result.data) {
                currentStatData = result.data;
                document.querySelector('#statsBreakdownContent').style.display = 'block';

                // Initialize table view by default
                initializeStatsTable(result.data);

                // Set up tabs
                setupStatsTabs();
            } else {
                document.querySelector('#statsBreakdownErrorIndicator').style.display = 'block';
                const errorSpan = document.querySelector('#statsBreakdownErrorIndicator span');
                if (errorSpan) {
                    errorSpan.textContent = result.message || 'Failed to load statistics data.';
                }
            }
         }

         // Function to initialize stats table with pagination and search
         function initializeStatsTable(data) {
            // Store the original data for pagination
            filteredData = [...data];
            currentPage = 1;
            rowsPerPage = 10;

            // Set up event listeners for search, pagination, and rows per page
            setupTableControls();

            // Show pagination and search controls
            const tableControls = document.querySelector('.stats-table-controls');
            const tablePagination = document.querySelector('.stats-table-pagination');
            if (tableControls) tableControls.style.display = '';
            if (tablePagination) tablePagination.style.display = '';

            // Initial table population
            updateTableDisplay();
         }

         // Function to set up table controls (search, pagination, rows per page)
         function setupTableControls() {
            // Search input
            const searchInput = document.getElementById('statsTableSearch');
            if (searchInput) {
                // Remove existing event listener
                const newSearchInput = searchInput.cloneNode(true);
                searchInput.parentNode.replaceChild(newSearchInput, searchInput);

                // Add event listener to the new input
                document.getElementById('statsTableSearch').addEventListener('input', function() {
                    currentPage = 1; // Reset to first page on search
                    updateTableDisplay();
                });
            }

            // Rows per page selector
            const lengthSelector = document.getElementById('statsTableLength');
            if (lengthSelector) {
                // Remove existing event listener
                const newLengthSelector = lengthSelector.cloneNode(true);
                lengthSelector.parentNode.replaceChild(newLengthSelector, lengthSelector);

                // Add event listener to the new select
                document.getElementById('statsTableLength').addEventListener('change', function() {
                    rowsPerPage = parseInt(this.value);
                    currentPage = 1; // Reset to first page when changing rows per page
                    updateTableDisplay();
                });
            }

            // Previous page button
            const prevButton = document.getElementById('statsTablePrevious');
            if (prevButton) {
                // Remove existing event listener
                const newPrevButton = prevButton.cloneNode(true);
                prevButton.parentNode.replaceChild(newPrevButton, prevButton);

                // Add event listener to the new button
                document.getElementById('statsTablePrevious').addEventListener('click', function() {
                    if (currentPage > 1) {
                        currentPage--;
                        updateTableDisplay();
                    }
                });
            }

            // Next page button
            const nextButton = document.getElementById('statsTableNext');
            if (nextButton) {
                // Remove existing event listener
                const newNextButton = nextButton.cloneNode(true);
                nextButton.parentNode.replaceChild(newNextButton, nextButton);

                // Add event listener to the new button
                document.getElementById('statsTableNext').addEventListener('click', function() {
                    const totalPages = Math.ceil(getFilteredData().length / rowsPerPage);
                    if (currentPage < totalPages) {
                        currentPage++;
                        updateTableDisplay();
                    }
                });
            }
         }

         // Function to filter data based on search input
         function getFilteredData() {
            const searchTerm = document.getElementById('statsTableSearch')?.value.toLowerCase() || '';

            if (!searchTerm) {
                return filteredData;
            }

            return filteredData.filter(item =>
                item.name.toLowerCase().includes(searchTerm)
            );
         }

         // Function to update table display with current page data
         function updateTableDisplay() {
            const tableBody = document.querySelector('#statsBreakdownTable tbody');
            if (!tableBody) return;

            const data = getFilteredData();
            const totalItems = data.length;
            const totalPages = Math.ceil(totalItems / rowsPerPage);
            const startIndex = (currentPage - 1) * rowsPerPage;
            const endIndex = Math.min(startIndex + rowsPerPage, totalItems);
            const pageData = data.slice(startIndex, endIndex);

            // Clear table body
            tableBody.innerHTML = '';

            if (totalItems === 0) {
                tableBody.innerHTML = '<tr><td colspan="3" class="table-message">No data available</td></tr>';
            } else {
                // Calculate total count for percentage calculation
                const totalCount = data.reduce((sum, item) => sum + (item.count || 0), 0);

                pageData.forEach(item => {
                    const count = item.count || 0;
                    const percentage = totalCount > 0 ? ((count / totalCount) * 100).toFixed(1) : '0.0';

                    const row = tableBody.insertRow();
                    row.innerHTML = `
                        <td>${escapeHtml(item.name || 'N/A')}</td>
                        <td>${count}</td>
                        <td>${percentage}%</td>
                    `;
                });
            }

            // Update pagination info
            updatePaginationInfo(startIndex + 1, endIndex, totalItems);
            updatePaginationButtons(currentPage, totalPages);
         }

         // Function to update pagination info text
         function updatePaginationInfo(start, end, total) {
            const startSpan = document.getElementById('statsTableStart');
            const endSpan = document.getElementById('statsTableEnd');
            const totalSpan = document.getElementById('statsTableTotal');

            if (startSpan) startSpan.textContent = start;
            if (endSpan) endSpan.textContent = end;
            if (totalSpan) totalSpan.textContent = total;
         }

         // Function to update pagination buttons
         function updatePaginationButtons(current, total) {
            const prevButton = document.getElementById('statsTablePrevious');
            const nextButton = document.getElementById('statsTableNext');
            const pageNumbersContainer = document.getElementById('statsTablePageNumbers');

            // Update previous button
            if (prevButton) {
                prevButton.disabled = current <= 1;
            }

            // Update next button
            if (nextButton) {
                nextButton.disabled = current >= total;
            }

            // Update page numbers
            if (pageNumbersContainer) {
                pageNumbersContainer.innerHTML = '';

                if (total <= 1) return;

                const maxVisiblePages = 5;
                let startPage = Math.max(1, current - Math.floor(maxVisiblePages / 2));
                let endPage = Math.min(total, startPage + maxVisiblePages - 1);

                // Adjust start page if we're near the end
                if (endPage - startPage < maxVisiblePages - 1) {
                    startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                // First page and ellipsis if needed
                if (startPage > 1) {
                    const firstPageBtn = document.createElement('a');
                    firstPageBtn.href = "#";
                    firstPageBtn.className = 'page-number';
                    firstPageBtn.textContent = '1';
                    firstPageBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = 1;
                        updateTableDisplay();
                    });
                    pageNumbersContainer.appendChild(firstPageBtn);

                    if (startPage > 2) {
                        const ellipsis = document.createElement('span');
                        ellipsis.className = 'page-number disabled';
                        ellipsis.textContent = '...';
                        pageNumbersContainer.appendChild(ellipsis);
                    }
                }

                // Add page number buttons
                for (let i = startPage; i <= endPage; i++) {
                    const pageButton = document.createElement('a');
                    pageButton.href = "#";
                    pageButton.className = i === current ? 'page-number active' : 'page-number';
                    pageButton.textContent = i;
                    pageButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = i;
                        updateTableDisplay();
                    });
                    pageNumbersContainer.appendChild(pageButton);
                }

                // Last page and ellipsis if needed
                if (endPage < total) {
                    if (endPage < total - 1) {
                        const ellipsis = document.createElement('span');
                        ellipsis.className = 'page-number disabled';
                        ellipsis.textContent = '...';
                        pageNumbersContainer.appendChild(ellipsis);
                    }

                    const lastPageBtn = document.createElement('a');
                    lastPageBtn.href = "#";
                    lastPageBtn.className = 'page-number';
                    lastPageBtn.textContent = total;
                    lastPageBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = total;
                        updateTableDisplay();
                    });
                    pageNumbersContainer.appendChild(lastPageBtn);
                }
            }
         }

         // Function to initialize stats chart
         function initializeStatsChart(data) {
            const chartContainer = document.getElementById('statsChartContainer');
            if (!chartContainer) return;

            // Destroy existing chart
            if (currentChart) {
                currentChart.destroy();
                currentChart = null;
            }

            if (!data || data.length === 0) {
                chartContainer.innerHTML = '<div class="chart-message">No data available for chart</div>';
                return;
            }

            // Create canvas element
            chartContainer.innerHTML = '<canvas id="statsChart"></canvas>';
            const canvas = document.getElementById('statsChart');
            const ctx = canvas.getContext('2d');

            // Prepare chart data
            const labels = data.map(item => item.name || 'N/A');
            const values = data.map(item => item.count || 0);

            // Create chart
            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Count',
                        data: values,
                        backgroundColor: 'rgba(106, 90, 224, 0.8)',
                        borderColor: 'rgba(106, 90, 224, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
         }

         // Function to set up tabs in stats modal
         function setupStatsTabs() {
            const tabBtns = document.querySelectorAll('.stats-tab-btn');
            const tableView = document.getElementById('statsTableView');
            const chartView = document.getElementById('statsChartView');

            // Remove existing event listeners by cloning and replacing buttons
            tabBtns.forEach(btn => {
                const newBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newBtn, btn);
            });

            // Get fresh references to the buttons
            const freshTabBtns = document.querySelectorAll('.stats-tab-btn');

            freshTabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const view = btn.dataset.view;

                    // Update active tab
                    freshTabBtns.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');

                    // Show/hide views
                    if (view === 'table') {
                        if (tableView) tableView.style.display = 'block';
                        if (chartView) chartView.style.display = 'none';
                    } else if (view === 'chart') {
                        if (tableView) tableView.style.display = 'none';
                        if (chartView) chartView.style.display = 'block';
                        // Initialize chart when switching to chart view
                        initializeStatsChart(currentStatData);
                    }
                });
            });
         }

         // --- Initial UI State ---
         updateActionBar();
         // Load data if the report tab is active on initial load
         if (currentActiveTab === 'reports') {
             loadFW4AReportData();
         }

       }); // End DOMContentLoaded
    </script>
    <script src="js/filter-dropdown.js"></script>
    <script src="js/settings.js"></script>

    <!-- Statistics Breakdown Modal -->
    <div id="statsBreakdownModal" class="modal large">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-chart-bar"></i> <span id="statsModalTitle">Statistics Breakdown</span></h2>
                <button type="button" class="close-modal" data-modal-id="statsBreakdown">&times;</button>
            </div>
            <div class="modal-body">
                <div id="statsBreakdownLoadingIndicator" class="loading-indicator active">
                    <i class="fas fa-spinner fa-spin"></i> Loading statistics data...
                </div>
                <div id="statsBreakdownErrorIndicator" class="error-indicator" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> <span>Could not load statistics data.</span>
                </div>
                <div id="statsBreakdownContent" style="display: none;">
                    <div class="stats-header">
                        <div class="stats-tabs">
                            <button class="stats-tab-btn active" data-view="table">
                                <i class="fas fa-table"></i> Table View
                            </button>
                            <button class="stats-tab-btn" data-view="chart">
                                <i class="fas fa-chart-bar"></i> Chart View
                            </button>
                        </div>
                    </div>

                    <div id="statsTableView" class="stats-view">
                        <div class="stats-table-controls">
                            <div class="stats-table-length">
                                Result per page:
                                <select id="statsTableLength">
                                    <option value="10" selected>10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <div class="stats-table-search">
                                <input type="search" id="statsTableSearch" placeholder="Search in actions, details...">
                            </div>
                        </div>
                        <div class="stats-table-container">
                            <table id="statsBreakdownTable" class="stats-table">
                                <thead>
                                    <tr>
                                        <th>NAME</th>
                                        <th>COUNT</th>
                                        <th>PERCENTAGE</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be populated here -->
                                </tbody>
                            </table>
                        </div>
                        <div class="stats-table-pagination">
                            <div class="stats-table-info">
                                Showing <span id="statsTableStart">1</span> to <span id="statsTableEnd">10</span> of <span id="statsTableTotal">0</span> entries
                            </div>
                            <div class="stats-table-pages">
                                <button id="statsTablePrevious" class="pagination-btn" disabled>&laquo; Previous</button>
                                <div id="statsTablePageNumbers" class="pagination-numbers"></div>
                                <button id="statsTableNext" class="pagination-btn">Next &raquo;</button>
                            </div>
                        </div>
                    </div>

                    <div id="statsChartView" class="stats-view" style="display: none;">
                        <div id="statsChartContainer" style="height: 400px;">
                            <!-- Chart will be rendered here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="statsBreakdown">Close</button>
            </div>
        </div>
    </div>

    <!-- Strategy Table Modal -->
    <div id="strategyTableModal" class="modal large">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-table"></i> Strategy Table</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <div id="strategyTableLoading" class="loading-indicator">
                    <i class="fas fa-spinner fa-spin"></i> Loading strategy data...
                </div>
                <div id="strategyTableError" class="error-message" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i> <span id="strategyTableErrorMessage">Error loading strategy data.</span>
                </div>
                <div id="strategyTableControls" class="table-controls" style="display: none; margin-bottom: 15px;">
                    <div class="results-per-page">
                        <label for="strategyTableRowsPerPage">Rows per page:</label>
                        <div class="select-wrapper">
                            <select id="strategyTableRowsPerPage" class="form-control">
                                <option value="5">5</option>
                                <option value="10" selected>10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div id="strategyTableContainer" style="display: none; overflow-x: auto; height: 400px;">
                    <table id="strategyTable">
                        <thead id="strategyTableHead">
                            <!-- Table headers will be populated by JavaScript -->
                        </thead>
                        <tbody id="strategyTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
                <div id="strategyTablePagination" class="pagination-container" style="display: none; margin-top: 15px;">
                    <div class="pagination-info" id="strategyTablePaginationInfo">
                        <!-- This will be populated with pagination info -->
                    </div>
                    <div class="pagination-nav" id="strategyTablePaginationNav">
                        <!-- This will be populated with pagination controls -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="strategyTableModal">Close</button>
            </div>
        </div>
    </div>

    <!-- Add Letter Modal -->
    <div id="addLetterModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-envelope"></i> Add New Letter</h2>
                <button class="close-modal" title="Close">×</button>
            </div>
            <div class="modal-body">
                <form id="addLetterForm">
                    <div class="form-grid">
                        <div class="form-field half-width">
                            <label for="addLetterLocality">Locality:</label>
                            <input type="text" id="addLetterLocality" name="locality" required>
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterBarangay">Barangay:</label>
                            <input type="text" id="addLetterBarangay" name="barangay">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterDistrict">District:</label>
                            <input type="text" id="addLetterDistrict" name="district">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterLocation">Location:</label>
                            <input type="text" id="addLetterLocation" name="location">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterDate">Date:</label>
                            <input type="date" id="addLetterDate" name="date">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterYear">Year:</label>
                            <input type="number" id="addLetterYear" name="year" min="2000" max="2100" value="<?php echo date('Y'); ?>">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterType">Type:</label>
                            <input type="text" id="addLetterType" name="type">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterStatus">Status:</label>
                            <input type="text" id="addLetterStatus" name="status">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterAccomplished">Accomplished:</label>
                            <input type="text" id="addLetterAccomplished" name="accomplished">
                        </div>
                        <div class="form-field half-width">
                            <label for="addLetterRemarks">Remarks:</label>
                            <input type="text" id="addLetterRemarks" name="remarks">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary close-modal" data-modal-id="addLetterModal">Cancel</button>
                <button type="submit" form="addLetterForm" class="btn btn-primary" id="saveAddLetterButton">
                    <i class="fas fa-save"></i> Save Letter
                </button>
            </div>
        </div>
    </div>

    <?php
      // Include common modals
      include 'modals.php';

      // Output access control JavaScript if user has limited access
      echo generateAccessControlJS($hasFullAccess);

      // Close DB connection if still open
      if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
          mysqli_close($conn);
      }
    ?>
</body>
</html>